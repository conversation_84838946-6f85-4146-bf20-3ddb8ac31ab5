nohup: 忽略输入
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00345_UAV_outdoor6——————————————
00345_UAV_outdoor6 , fps:12.995000343897
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00351_UAV_outdoor6——————————————
00351_UAV_outdoor6 , fps:10.779131151300383
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00355_UAV_outdoor6——————————————
00355_UAV_outdoor6 , fps:13.953943461668445
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00370_UAV_outdoor6——————————————
00370_UAV_outdoor6 , fps:10.181695829709293
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00449_UAV_outdoor6——————————————
00449_UAV_outdoor6 , fps:11.338958893907947
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00451_UAV_outdoor6——————————————
00451_UAV_outdoor6 , fps:12.01709803305038
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00453_UAV_outdoor6——————————————
00453_UAV_outdoor6 , fps:11.971010703924794
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00458_UAV_outdoor6——————————————
00458_UAV_outdoor6 , fps:11.432145138917368
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00421_UAV_outdoor6——————————————
00421_UAV_outdoor6 , fps:11.298647806423809
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00423_UAV_outdoor6——————————————
00423_UAV_outdoor6 , fps:14.97228071444372
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00425_UAV_outdoor6——————————————
00425_UAV_outdoor6 , fps:11.662801433297167
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00430_UAV_outdoor6——————————————
00430_UAV_outdoor6 , fps:10.196758860057127
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00404_UAV_outdoor6——————————————
00404_UAV_outdoor6 , fps:10.473913434940776
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00406_UAV_outdoor6——————————————
00406_UAV_outdoor6 , fps:10.660416552309723
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00408_UAV_outdoor6——————————————
00408_UAV_outdoor6 , fps:11.303188268748997
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00410_UAV_outdoor6——————————————
00410_UAV_outdoor6 , fps:11.301691927467358
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00141_tank_outdoor2——————————————
00141_tank_outdoor2 , fps:10.769575524531973
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00147_tank_outdoor2——————————————
00147_tank_outdoor2 , fps:14.887318783150942
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00197_driving_outdoor3——————————————
00197_driving_outdoor3 , fps:11.55805018860224
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00236_tennis_outdoor4——————————————
00236_tennis_outdoor4 , fps:13.459145233384676
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00435_UAV_outdoor6——————————————
00435_UAV_outdoor6 , fps:9.651173358592223
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00437_UAV_outdoor6——————————————
00437_UAV_outdoor6 , fps:12.369725074153628
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00439_UAV_outdoor6——————————————
00439_UAV_outdoor6 , fps:11.30278014466521
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00442_UAV_outdoor6——————————————
00442_UAV_outdoor6 , fps:11.040485810622918
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00508_person_outdoor6——————————————
00508_person_outdoor6 , fps:9.450805745874158
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00510_person_outdoor6——————————————
00510_person_outdoor6 , fps:11.955107865699762
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00511_person_outdoor6——————————————
00511_person_outdoor6 , fps:8.766122001347819
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00514_person_outdoor6——————————————
00514_person_outdoor6 , fps:12.75118197092147
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00471_UAV_outdoor6——————————————
00471_UAV_outdoor6 , fps:8.400779341101368
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00473_UAV_outdoor6——————————————
00473_UAV_outdoor6 , fps:12.67786719063979
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00478_UAV_outdoor6——————————————
00478_UAV_outdoor6 , fps:8.959810350796253
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00490_UAV_outdoor6——————————————
00490_UAV_outdoor6 , fps:10.985901912163392
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00432_UAV_outdoor6——————————————
00432_UAV_outdoor6 , fps:11.671736232466948
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00433_UAV_outdoor6——————————————
00433_UAV_outdoor6 , fps:10.156145329759994
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_15_36_redcar——————————————
dvSave-2021_02_06_15_15_36_redcar , fps:11.775551143029126
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_17_48_whitecar——————————————
dvSave-2021_02_06_15_17_48_whitecar , fps:8.624880290307045
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00385_UAV_outdoor6——————————————
00385_UAV_outdoor6 , fps:11.273545088530883
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00398_UAV_outdoor6——————————————
00398_UAV_outdoor6 , fps:14.814532337347757
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_08_56_18_windowPattern——————————————
dvSave-2021_02_06_08_56_18_windowPattern , fps:10.536262463912863
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_08_56_40_windowPattern2——————————————
dvSave-2021_02_06_08_56_40_windowPattern2 , fps:11.201676968251446
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00413_UAV_outdoor6——————————————
00413_UAV_outdoor6 , fps:13.328880433075415
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00416_UAV_outdoor6——————————————
00416_UAV_outdoor6 , fps:12.650453025610496
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_36_15_Pedestrian——————————————
dvSave-2021_02_06_09_36_15_Pedestrian , fps:9.40597914489088
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_36_44_Pedestrian——————————————
dvSave-2021_02_06_09_36_44_Pedestrian , fps:10.841444065074095
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00445_UAV_outdoor6——————————————
00445_UAV_outdoor6 , fps:9.731356585884402
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00447_UAV_outdoor6——————————————
00447_UAV_outdoor6 , fps:10.494145328624642
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_21_41_personFootball——————————————
dvSave-2021_02_06_17_21_41_personFootball , fps:12.880806961104044
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_23_26_personFootball——————————————
dvSave-2021_02_06_17_23_26_personFootball , fps:14.161876799139304
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00503_UAV_outdoor6——————————————
00503_UAV_outdoor6 , fps:11.247826838889766
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00506_person_outdoor6——————————————
00506_person_outdoor6 , fps:15.411787297686857
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_36_49_personBasketball——————————————
dvSave-2021_02_06_17_36_49_personBasketball , fps:13.003822909051541
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_41_45_personBasketball——————————————
dvSave-2021_02_06_17_41_45_personBasketball , fps:12.420353641691149
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_16_35_car——————————————
dvSave-2021_02_06_09_16_35_car , fps:12.96525311986376
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_21_53_car——————————————
dvSave-2021_02_06_09_21_53_car , fps:11.73636700205495
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_24_26_Pedestrian1——————————————
dvSave-2021_02_06_09_24_26_Pedestrian1 , fps:10.933399628308743
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_35_08_Pedestrian——————————————
dvSave-2021_02_06_09_35_08_Pedestrian , fps:12.90318094501513
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00464_UAV_outdoor6——————————————
00464_UAV_outdoor6 , fps:15.005830744033473
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00466_UAV_outdoor6——————————————
00466_UAV_outdoor6 , fps:14.093541076521289
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_20_41_53——————————————
dvSave-2021_02_04_20_41_53 , fps:12.330033408880732
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_20_49_43——————————————
dvSave-2021_02_04_20_49_43 , fps:12.564761032821588
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_18_36_redcar——————————————
dvSave-2021_02_06_15_18_36_redcar , fps:9.803360039163287
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_15_20_whitecar——————————————
dvSave-2021_02_06_17_15_20_whitecar , fps:9.793864812523415
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_16_26_whitecar——————————————
dvSave-2021_02_06_17_16_26_whitecar , fps:10.702842638526114
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_20_28_personFootball——————————————
dvSave-2021_02_06_17_20_28_personFootball , fps:13.143752166935382
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00241_tennis_outdoor4——————————————
00241_tennis_outdoor4 , fps:13.306210886649858
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: 00340_UAV_outdoor6——————————————
00340_UAV_outdoor6 , fps:12.229421262533348
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_11_59_paperClips——————————————
dvSave-2021_02_06_10_11_59_paperClips , fps:10.292187448035802
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_14_17_paperClip——————————————
dvSave-2021_02_06_10_14_17_paperClip , fps:10.152523641893607
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_20_56_55——————————————
dvSave-2021_02_04_20_56_55 , fps:12.895219396589017
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_21_18_52——————————————
dvSave-2021_02_04_21_18_52 , fps:10.278899324053777
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_21_20_22——————————————
dvSave-2021_02_04_21_20_22 , fps:11.688010957604142
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_04_21_21_24——————————————
dvSave-2021_02_04_21_21_24 , fps:9.407425181277569
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_27_53_personFootball——————————————
dvSave-2021_02_06_17_27_53_personFootball , fps:12.95930765690805
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_31_03_personBasketball——————————————
dvSave-2021_02_06_17_31_03_personBasketball , fps:11.410251530643912
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_33_01_personBasketball——————————————
dvSave-2021_02_06_17_33_01_personBasketball , fps:12.740423096901171
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_34_58_personBasketball——————————————
dvSave-2021_02_06_17_34_58_personBasketball , fps:13.47308895093206
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_53_39_personFootball——————————————
dvSave-2021_02_06_17_53_39_personFootball , fps:14.127918140980144
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_57_54_personFootball——————————————
dvSave-2021_02_06_17_57_54_personFootball , fps:14.357415476408773
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_08_21_05_56_motor——————————————
dvSave-2021_02_08_21_05_56_motor , fps:13.877513121666484
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_08_21_07_52——————————————
dvSave-2021_02_08_21_07_52 , fps:12.574524428856076
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_45_17_personBasketball——————————————
dvSave-2021_02_06_17_45_17_personBasketball , fps:10.601719045795907
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_47_49_personBasketball——————————————
dvSave-2021_02_06_17_47_49_personBasketball , fps:13.52112340604772
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_49_51_personBasketball——————————————
dvSave-2021_02_06_17_49_51_personBasketball , fps:14.464152347091188
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_17_51_05_personBasketball——————————————
dvSave-2021_02_06_17_51_05_personBasketball , fps:14.407542641386824
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: UAV_long_001——————————————
UAV_long_001 , fps:11.914032250259492
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dightNUM_001——————————————
dightNUM_001 , fps:10.096424421192902
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_12_13_43_54——————————————
dvSave-2021_02_12_13_43_54 , fps:10.8474244743249
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_12_13_46_18——————————————
dvSave-2021_02_12_13_46_18 , fps:13.103564297870536
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_10_54_person——————————————
dvSave-2021_02_15_13_10_54_person , fps:11.492418351078683
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_12_45_redcar——————————————
dvSave-2021_02_15_13_12_45_redcar , fps:12.175741759691569
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_13_44_whitecar——————————————
dvSave-2021_02_15_13_13_44_whitecar , fps:12.688485066572992
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_14_18_blackcar——————————————
dvSave-2021_02_15_13_14_18_blackcar , fps:11.227080634620485
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_01_16_Duck——————————————
dvSave-2021_02_15_13_01_16_Duck , fps:11.480329897073554
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_02_21_Chicken——————————————
dvSave-2021_02_15_13_02_21_Chicken , fps:11.992535990082413
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_04_57_Duck——————————————
dvSave-2021_02_15_13_04_57_Duck , fps:10.47465936573672
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_05_43_Chicken——————————————
dvSave-2021_02_15_13_05_43_Chicken , fps:9.898137991540892
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_17_16_paperClips——————————————
dvSave-2021_02_06_10_17_16_paperClips , fps:10.527407494392696
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_08_41_flag——————————————
dvSave-2021_02_06_15_08_41_flag , fps:11.8948304616925
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_12_44_car——————————————
dvSave-2021_02_06_15_12_44_car , fps:10.15889460755523
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_15_14_26_blackcar——————————————
dvSave-2021_02_06_15_14_26_blackcar , fps:12.032380886264395
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_14_16_56_01_house——————————————
dvSave-2021_02_14_16_56_01_house , fps:9.17958006374658
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_14_17_00_48——————————————
dvSave-2021_02_14_17_00_48 , fps:15.84555880816394
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_12_19_basketball——————————————
dvSave-2021_02_15_10_12_19_basketball , fps:13.376467999506804
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_14_18_chicken——————————————
dvSave-2021_02_15_10_14_18_chicken , fps:12.14743882527771
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_basketall——————————————
dvSave-2021_02_15_10_23_05_basketall , fps:14.609432140063914
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_boyhead——————————————
dvSave-2021_02_15_10_23_05_boyhead , fps:12.75639225259381
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_26_11_chicken——————————————
dvSave-2021_02_15_10_26_11_chicken , fps:12.16291481964114
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_12_44_27_chicken——————————————
dvSave-2021_02_15_12_44_27_chicken , fps:10.422589695167265
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_09_58_27_DigitAI——————————————
dvSave-2021_02_06_09_58_27_DigitAI , fps:10.267156174561897
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_03_17_GreenPlant——————————————
dvSave-2021_02_06_10_03_17_GreenPlant , fps:11.72301374417432
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_05_38_phone——————————————
dvSave-2021_02_06_10_05_38_phone , fps:13.68151441727881
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_06_10_09_04_bottle——————————————
dvSave-2021_02_06_10_09_04_bottle , fps:9.849903113951957
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_12_13_56_29——————————————
dvSave-2021_02_12_13_56_29 , fps:10.541430130680313
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_14_16_21_40——————————————
dvSave-2021_02_14_16_21_40 , fps:15.31458835031087
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_14_16_22_06——————————————
dvSave-2021_02_14_16_22_06 , fps:13.030219499264039
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_14_16_53_15_flag——————————————
dvSave-2021_02_14_16_53_15_flag , fps:10.080328182527742
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_24_03_girlhead——————————————
dvSave-2021_02_15_13_24_03_girlhead , fps:9.860939307257912
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_24_49_girlhead——————————————
dvSave-2021_02_15_13_24_49_girlhead , fps:10.30639140449851
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_27_20_bottle——————————————
dvSave-2021_02_15_13_27_20_bottle , fps:10.538890166471425
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_28_20_cash——————————————
dvSave-2021_02_15_13_28_20_cash , fps:6.159891951346511
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_08_12_blackcar——————————————
dvSave-2021_02_15_13_08_12_blackcar , fps:12.906795907625705
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_13_09_09_person——————————————
dvSave-2021_02_15_13_09_09_person , fps:12.30853009178511
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_38_25——————————————
dvSave-2021_02_16_17_38_25 , fps:10.094936107087854
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_42_50——————————————
dvSave-2021_02_16_17_42_50 , fps:11.669231940047869
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_basketball——————————————
dvSave-2021_02_15_10_22_23_basketball , fps:12.141391060455046
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_boyhead——————————————
dvSave-2021_02_15_10_22_23_boyhead , fps:11.681712848482443
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0005——————————————
video_0005 , fps:9.7058358339847
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0008——————————————
video_0008 , fps:9.632296216247264
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_12_45_02_Duck——————————————
dvSave-2021_02_15_12_45_02_Duck , fps:10.841608489783173
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_12_53_54_personHead——————————————
dvSave-2021_02_15_12_53_54_personHead , fps:11.171140597498756
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0070——————————————
video_0070 , fps:13.184847091392603
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0073——————————————
video_0073 , fps:12.834931187827777
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_12_13_38_26——————————————
dvSave-2021_02_12_13_38_26 , fps:12.23229670395588
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_12_13_39_56——————————————
dvSave-2021_02_12_13_39_56 , fps:10.313486245533948
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0054——————————————
video_0054 , fps:10.914915547047048
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0056——————————————
video_0056 , fps:15.254420810325984
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_003——————————————
tennis_long_003 , fps:12.470547740725818
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_004——————————————
tennis_long_004 , fps:12.464194391142971
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_005——————————————
tennis_long_005 , fps:13.581699512563517
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_006——————————————
tennis_long_006 , fps:18.19236093191443
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_12_18——————————————
dvSave-2021_02_16_17_12_18 , fps:10.998345387968888
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_15_53——————————————
dvSave-2021_02_16_17_15_53 , fps:12.654878116478955
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_20_20——————————————
dvSave-2021_02_16_17_20_20 , fps:13.066140740345112
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_23_10——————————————
dvSave-2021_02_16_17_23_10 , fps:16.80010374086875
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0029——————————————
video_0029 , fps:11.564872312398206
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0032——————————————
video_0032 , fps:16.149269322337616
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0039——————————————
video_0039 , fps:18.5471058919355
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0045——————————————
video_0045 , fps:16.132785431950772
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0076——————————————
video_0076 , fps:9.047033224595161
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0079——————————————
video_0079 , fps:14.499113551215622
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dydrant_001——————————————
dydrant_001 , fps:10.560145970849677
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: roadLight_001——————————————
roadLight_001 , fps:9.477515067009895
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_001——————————————
tennis_long_001 , fps:14.058241535639478
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_002——————————————
tennis_long_002 , fps:15.881964746701602
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_23_51_36——————————————
dvSave-2021_02_15_23_51_36 , fps:10.692885984575227
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_23_54_17——————————————
dvSave-2021_02_15_23_54_17 , fps:6.554453540978847
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_15_23_56_17——————————————
dvSave-2021_02_15_23_56_17 , fps:11.535613846530342
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_07_38——————————————
dvSave-2021_02_16_17_07_38 , fps:16.118554355246665
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0015——————————————
video_0015 , fps:8.048486664814115
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0018——————————————
video_0018 , fps:10.261998285371764
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0021——————————————
video_0021 , fps:15.177756385230598
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0026——————————————
video_0026 , fps:15.281573048644495
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0058——————————————
video_0058 , fps:17.88044981642344
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0060——————————————
video_0060 , fps:18.05566011341345
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0064——————————————
video_0064 , fps:18.031548132004346
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0067——————————————
video_0067 , fps:19.3777161836485
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: tennis_long_007——————————————
tennis_long_007 , fps:18.49767843892785
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0004——————————————
video_0004 , fps:17.04573276384318
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_29_37——————————————
dvSave-2021_02_16_17_29_37 , fps:18.233296710995546
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: dvSave-2021_02_16_17_34_11——————————————
dvSave-2021_02_16_17_34_11 , fps:19.334859269199736
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0049——————————————
video_0049 , fps:17.997867701433037
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 2, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 2, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}}
——————————Process sequence: video_0050——————————————
video_0050 , fps:17.73176040034998
Totally cost 1367.2366099357605 seconds!
