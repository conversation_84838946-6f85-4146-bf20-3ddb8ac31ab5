nohup: 忽略输入
W0727 01:23:20.408862 3553198 site-packages/torch/distributed/run.py:793] 
W0727 01:23:20.408862 3553198 site-packages/torch/distributed/run.py:793] *****************************************
W0727 01:23:20.408862 3553198 site-packages/torch/distributed/run.py:793] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0727 01:23:20.408862 3553198 site-packages/torch/distributed/run.py:793] *****************************************
Process 3553258: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
2
Process 3553256: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 3553262: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
0
6
Process 3553257: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 3553259: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
1
Process 3553261: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 3553263: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
3
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
7
5
Process 3553260: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
script_name: vipt.py  config_name: coesot.yaml
4
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
New configuration is shown below.
MODEL configuration: {'PRETRAIN_FILE': './pretrained/OSTrack_coesot.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}


TRAIN configuration: {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 5e-05, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 5, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}


DATA configuration: {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['COESOT'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['COESOT_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 40000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}


TEST configuration: {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 10}


sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
Learnable parameters are shown below.
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
module.backbone.voxel_extractor.sparse_encoder.sparse_conv1.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv1.bias
module.backbone.voxel_extractor.sparse_encoder.norm1.weight
module.backbone.voxel_extractor.sparse_encoder.norm1.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv2.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv2.bias
module.backbone.voxel_extractor.sparse_encoder.norm2.weight
module.backbone.voxel_extractor.sparse_encoder.norm2.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv3.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv3.bias
module.backbone.voxel_extractor.sparse_encoder.norm3.weight
module.backbone.voxel_extractor.sparse_encoder.norm3.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv4.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv4.bias
module.backbone.voxel_extractor.sparse_encoder.norm4.weight
module.backbone.voxel_extractor.sparse_encoder.norm4.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_pool.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_pool.bias
module.backbone.voxel_extractor.sparse_encoder.norm_pool.weight
module.backbone.voxel_extractor.sparse_encoder.norm_pool.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_conv.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_conv.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_norm.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_norm.bias
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_conv.weight
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_conv.bias
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_norm.weight
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_norm.bias
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_conv.weight
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_conv.bias
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_norm.weight
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_norm.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.motion_saliency.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.motion_saliency.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.temporal_consistency.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.temporal_consistency.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.spatial_coherence.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.spatial_coherence.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.correlation_fusion.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.correlation_fusion.bias
module.backbone.voxel_extractor.adaptive_temporal_attention.0.weight
module.backbone.voxel_extractor.adaptive_temporal_attention.0.bias
module.backbone.voxel_extractor.adaptive_temporal_attention.2.weight
module.backbone.voxel_extractor.adaptive_temporal_attention.2.bias
module.backbone.voxel_projector.0.weight
module.backbone.voxel_projector.0.bias
module.backbone.voxel_projector.1.weight
module.backbone.voxel_projector.1.bias
module.backbone.voxel_attn.cross_attn.in_proj_weight
module.backbone.voxel_attn.cross_attn.in_proj_bias
module.backbone.voxel_attn.cross_attn.out_proj.weight
module.backbone.voxel_attn.cross_attn.out_proj.bias
module.backbone.voxel_attn.fusion.0.weight
module.backbone.voxel_attn.fusion.0.bias
module.backbone.voxel_attn.fusion.1.weight
module.backbone.voxel_attn.fusion.1.bias
module.backbone.voxel_attn.alpha_predictor.0.weight
module.backbone.voxel_attn.alpha_predictor.0.bias
module.backbone.voxel_attn.alpha_predictor.2.weight
module.backbone.voxel_attn.alpha_predictor.2.bias
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
No matching checkpoint file found
No matching checkpoint file found
No matching checkpoint file found
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpointsNo matching checkpoint file found

No matching checkpoint file found
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 1.585 (0.046)  ,  ForwardTime: 9.456  ,  TotalTime: 11.087  ,  Loss/total: 0.71016  ,  Loss/giou: 0.16787  ,  Loss/l1: 0.01482  ,  Loss/location: 0.30034  ,  IoU: 0.84348
[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 1.365 (0.046)  ,  ForwardTime: 9.676  ,  TotalTime: 11.087  ,  Loss/total: 0.73871  ,  Loss/giou: 0.17621  ,  Loss/l1: 0.01626  ,  Loss/location: 0.30498  ,  IoU: 0.83705[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 0.742 (0.044)  ,  ForwardTime: 10.301  ,  TotalTime: 11.087  ,  Loss/total: 0.69225  ,  Loss/giou: 0.16939  ,  Loss/l1: 0.01509  ,  Loss/location: 0.27804  ,  IoU: 0.84262

[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 1.782 (0.045)  ,  ForwardTime: 9.261  ,  TotalTime: 11.087  ,  Loss/total: 0.71661  ,  Loss/giou: 0.16829  ,  Loss/l1: 0.01490  ,  Loss/location: 0.30556  ,  IoU: 0.84301
[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 4.027 (0.041)  ,  ForwardTime: 7.019  ,  TotalTime: 11.087  ,  Loss/total: 0.71894  ,  Loss/giou: 0.16895  ,  Loss/l1: 0.01504  ,  Loss/location: 0.30584  ,  IoU: 0.84296
[train: 1, 50 / 117] FPS: 5.8 (101.8)  ,  DataTime: 3.652 (0.042)  ,  ForwardTime: 7.394  ,  TotalTime: 11.087  ,  Loss/total: 0.71051  ,  Loss/giou: 0.16874  ,  Loss/l1: 0.01513  ,  Loss/location: 0.29737  ,  IoU: 0.84305
[train: 1, 50 / 117] FPS: 5.8 (101.9)  ,  DataTime: 10.271 (0.041)  ,  ForwardTime: 0.775  ,  TotalTime: 11.087  ,  Loss/total: 0.70631  ,  Loss/giou: 0.16586  ,  Loss/l1: 0.01506  ,  Loss/location: 0.29929  ,  IoU: 0.84575
[train: 1, 50 / 117] FPS: 5.8 (101.6)  ,  DataTime: 4.144 (0.041)  ,  ForwardTime: 6.902  ,  TotalTime: 11.087  ,  Loss/total: 0.69822  ,  Loss/giou: 0.16860  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28575  ,  IoU: 0.84275
[train: 1, 100 / 117] FPS: 5.7 (101.8)  ,  DataTime: 2.717 (0.040)  ,  ForwardTime: 8.512  ,  TotalTime: 11.269  ,  Loss/total: 0.69065  ,  Loss/giou: 0.16697  ,  Loss/l1: 0.01483  ,  Loss/location: 0.28255  ,  IoU: 0.84383
[train: 1, 100 / 117] FPS: 5.7 (101.8)  ,  DataTime: 1.917 (0.045)  ,  ForwardTime: 9.307  ,  TotalTime: 11.269  ,  Loss/total: 0.72001  ,  Loss/giou: 0.17135  ,  Loss/l1: 0.01528  ,  Loss/location: 0.30090  ,  IoU: 0.84018
[train: 1, 100 / 117] FPS: 5.7 (101.8)  ,  DataTime: 0.389 (0.044)  ,  ForwardTime: 10.837  ,  TotalTime: 11.269  ,  Loss/total: 0.68549  ,  Loss/giou: 0.16639  ,  Loss/l1: 0.01484  ,  Loss/location: 0.27851  ,  IoU: 0.84499
[train: 1, 100 / 117] FPS: 5.7 (101.7)  ,  DataTime: 0.714 (0.044)  ,  ForwardTime: 10.511  ,  TotalTime: 11.269  ,  Loss/total: 0.72546  ,  Loss/giou: 0.17302  ,  Loss/l1: 0.01595  ,  Loss/location: 0.29965  ,  IoU: 0.83982
[train: 1, 100 / 117] FPS: 5.7 (101.8)  ,  DataTime: 3.404 (0.041)  ,  ForwardTime: 7.824  ,  TotalTime: 11.269  ,  Loss/total: 0.69676  ,  Loss/giou: 0.16643  ,  Loss/l1: 0.01455  ,  Loss/location: 0.29116  ,  IoU: 0.84440[train: 1, 100 / 117] FPS: 5.7 (102.3)  ,  DataTime: 10.121 (0.041)  ,  ForwardTime: 1.107  ,  TotalTime: 11.269  ,  Loss/total: 0.71357  ,  Loss/giou: 0.17038  ,  Loss/l1: 0.01567  ,  Loss/location: 0.29444  ,  IoU: 0.84197

[train: 1, 100 / 117] FPS: 5.7 (101.7)  ,  DataTime: 0.809 (0.044)  ,  ForwardTime: 10.416  ,  TotalTime: 11.269  ,  Loss/total: 0.71635  ,  Loss/giou: 0.17233  ,  Loss/l1: 0.01550  ,  Loss/location: 0.29419  ,  IoU: 0.83991
[train: 1, 100 / 117] FPS: 5.7 (102.0)  ,  DataTime: 3.532 (0.041)  ,  ForwardTime: 7.696  ,  TotalTime: 11.269  ,  Loss/total: 0.71552  ,  Loss/giou: 0.17161  ,  Loss/l1: 0.01562  ,  Loss/location: 0.29418  ,  IoU: 0.84081
[train: 1, 117 / 117] FPS: 6.2 (101.1)  ,  DataTime: 0.753 (0.044)  ,  ForwardTime: 9.605  ,  TotalTime: 10.402  ,  Loss/total: 0.71282  ,  Loss/giou: 0.17169  ,  Loss/l1: 0.01541  ,  Loss/location: 0.29237  ,  IoU: 0.84033[train: 1, 117 / 117] FPS: 6.2 (101.2)  ,  DataTime: 0.337 (0.044)  ,  ForwardTime: 10.022  ,  TotalTime: 10.402  ,  Loss/total: 0.68999  ,  Loss/giou: 0.16814  ,  Loss/l1: 0.01529  ,  Loss/location: 0.27723  ,  IoU: 0.84396[train: 1, 117 / 117] FPS: 6.2 (101.1)  ,  DataTime: 1.643 (0.045)  ,  ForwardTime: 8.715  ,  TotalTime: 10.403  ,  Loss/total: 0.71800  ,  Loss/giou: 0.17096  ,  Loss/l1: 0.01518  ,  Loss/location: 0.30018  ,  IoU: 0.84048


[train: 1, 117 / 117] FPS: 6.2 (101.0)  ,  DataTime: 3.463 (0.041)  ,  ForwardTime: 6.898  ,  TotalTime: 10.402  ,  Loss/total: 0.71157  ,  Loss/giou: 0.17134  ,  Loss/l1: 0.01556  ,  Loss/location: 0.29109  ,  IoU: 0.84094
[train: 1, 117 / 117] FPS: 6.2 (100.9)  ,  DataTime: 0.669 (0.044)  ,  ForwardTime: 9.690  ,  TotalTime: 10.402  ,  Loss/total: 0.72537  ,  Loss/giou: 0.17354  ,  Loss/l1: 0.01601  ,  Loss/location: 0.29825  ,  IoU: 0.83942
[train: 1, 117 / 117] FPS: 6.2 (101.1)  ,  DataTime: 9.335 (0.041)  ,  ForwardTime: 1.027  ,  TotalTime: 10.402  ,  Loss/total: 0.70923  ,  Loss/giou: 0.16986  ,  Loss/l1: 0.01543  ,  Loss/location: 0.29235  ,  IoU: 0.84213
[train: 1, 117 / 117] FPS: 6.2 (101.0)  ,  DataTime: 3.279 (0.041)  ,  ForwardTime: 7.082  ,  TotalTime: 10.402  ,  Loss/total: 0.69042  ,  Loss/giou: 0.16564  ,  Loss/l1: 0.01453  ,  Loss/location: 0.28649  ,  IoU: 0.84515
[train: 1, 117 / 117] FPS: 6.2 (100.8)  ,  DataTime: 2.331 (0.040)  ,  ForwardTime: 8.032  ,  TotalTime: 10.402  ,  Loss/total: 0.68965  ,  Loss/giou: 0.16668  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28275  ,  IoU: 0.84389
Epoch Time: 0:20:17.085112
Avg Data Time: 0.33694
Avg GPU Trans Time: 0.04395
Avg Forward Time: 10.02155
Epoch Time: 0:20:17.093726
Avg Data Time: 1.64277
Avg GPU Trans Time: 0.04485
Avg Forward Time: 8.71489
Epoch Time: 0:20:17.087502
Epoch Time: 0:20:17.084863Avg Data Time: 0.66857

Avg GPU Trans Time: 0.04381
Avg Data Time: 0.75305Avg Forward Time: 9.69008

Avg GPU Trans Time: 0.04405
Avg Forward Time: 9.60533
Epoch Time: 0:20:17.082582
Avg Data Time: 9.33479
Avg GPU Trans Time: 0.04106
Avg Forward Time: 1.02656
Epoch Time: 0:20:17.088396
Avg Data Time: 2.33064
Avg GPU Trans Time: 0.03997
Avg Forward Time: 8.03186
Epoch Time: 0:20:17.075956
Avg Data Time: 3.46341
Avg GPU Trans Time: 0.04074
Avg Forward Time: 6.89821
Epoch Time: 0:20:17.069933
Avg Data Time: 3.27903
Avg GPU Trans Time: 0.04080
Avg Forward Time: 7.08247
[train: 2, 50 / 117] FPS: 6.5 (100.7)  ,  DataTime: 7.919 (0.044)  ,  ForwardTime: 1.946  ,  TotalTime: 9.909  ,  Loss/total: 0.66005  ,  Loss/giou: 0.15971  ,  Loss/l1: 0.01391  ,  Loss/location: 0.27107  ,  IoU: 0.84997
[train: 2, 50 / 117] FPS: 6.5 (100.7)  ,  DataTime: 4.063 (0.046)  ,  ForwardTime: 5.798  ,  TotalTime: 9.908  ,  Loss/total: 0.68360  ,  Loss/giou: 0.16704  ,  Loss/l1: 0.01480  ,  Loss/location: 0.27554  ,  IoU: 0.84354[train: 2, 50 / 117] FPS: 6.5 (100.8)  ,  DataTime: 6.454 (0.045)  ,  ForwardTime: 3.410  ,  TotalTime: 9.909  ,  Loss/total: 0.69002  ,  Loss/giou: 0.16837  ,  Loss/l1: 0.01549  ,  Loss/location: 0.27585  ,  IoU: 0.84495

[train: 2, 50 / 117] FPS: 6.5 (100.9)  ,  DataTime: 5.336 (0.042)  ,  ForwardTime: 4.530  ,  TotalTime: 9.908  ,  Loss/total: 0.68483  ,  Loss/giou: 0.16807  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27216  ,  IoU: 0.84480
[train: 2, 50 / 117] FPS: 6.5 (100.7)  ,  DataTime: 7.779 (0.046)  ,  ForwardTime: 2.084  ,  TotalTime: 9.909  ,  Loss/total: 0.68908  ,  Loss/giou: 0.16690  ,  Loss/l1: 0.01453  ,  Loss/location: 0.28264  ,  IoU: 0.84353
[train: 2, 50 / 117] FPS: 6.5 (100.8)  ,  DataTime: 6.943 (0.043)  ,  ForwardTime: 2.921  ,  TotalTime: 9.908  ,  Loss/total: 0.68310  ,  Loss/giou: 0.16767  ,  Loss/l1: 0.01491  ,  Loss/location: 0.27322  ,  IoU: 0.84420
[train: 2, 50 / 117] FPS: 6.5 (100.6)  ,  DataTime: 4.337 (0.047)  ,  ForwardTime: 5.525  ,  TotalTime: 9.909  ,  Loss/total: 0.72080  ,  Loss/giou: 0.17513  ,  Loss/l1: 0.01676  ,  Loss/location: 0.28673  ,  IoU: 0.83928
[train: 2, 50 / 117] FPS: 6.5 (100.7)  ,  DataTime: 6.166 (0.043)  ,  ForwardTime: 3.698  ,  TotalTime: 9.908  ,  Loss/total: 0.70490  ,  Loss/giou: 0.17427  ,  Loss/l1: 0.01587  ,  Loss/location: 0.27702  ,  IoU: 0.83870
[train: 2, 100 / 117] FPS: 7.5 (101.3)  ,  DataTime: 3.822 (0.046)  ,  ForwardTime: 4.630  ,  TotalTime: 8.499  ,  Loss/total: 0.68775  ,  Loss/giou: 0.16849  ,  Loss/l1: 0.01554  ,  Loss/location: 0.27305  ,  IoU: 0.84475
[train: 2, 100 / 117] FPS: 7.5 (101.3)  ,  DataTime: 2.055 (0.048)  ,  ForwardTime: 6.396  ,  TotalTime: 8.498  ,  Loss/total: 0.68810  ,  Loss/giou: 0.16735  ,  Loss/l1: 0.01475  ,  Loss/location: 0.27967  ,  IoU: 0.84337
[train: 2, 100 / 117] FPS: 7.5 (101.3)  ,  DataTime: 2.837 (0.042)  ,  ForwardTime: 5.619  ,  TotalTime: 8.498  ,  Loss/total: 0.70784  ,  Loss/giou: 0.17145  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28721  ,  IoU: 0.84129
[train: 2, 100 / 117] FPS: 7.5 (101.4)  ,  DataTime: 3.836 (0.046)  ,  ForwardTime: 4.616  ,  TotalTime: 8.498  ,  Loss/total: 0.70496  ,  Loss/giou: 0.17248  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28158  ,  IoU: 0.84017
[train: 2, 100 / 117] FPS: 7.5 (101.3)  ,  DataTime: 5.223 (0.044)  ,  ForwardTime: 3.231  ,  TotalTime: 8.498  ,  Loss/total: 0.67871  ,  Loss/giou: 0.16664  ,  Loss/l1: 0.01475  ,  Loss/location: 0.27167  ,  IoU: 0.84485
[train: 2, 100 / 117] FPS: 7.5 (101.2)  ,  DataTime: 4.469 (0.044)  ,  ForwardTime: 3.985  ,  TotalTime: 8.499  ,  Loss/total: 0.67725  ,  Loss/giou: 0.16493  ,  Loss/l1: 0.01475  ,  Loss/location: 0.27363  ,  IoU: 0.84645
[train: 2, 100 / 117] FPS: 7.5 (101.2)  ,  DataTime: 4.049 (0.047)  ,  ForwardTime: 4.403  ,  TotalTime: 8.499  ,  Loss/total: 0.69679  ,  Loss/giou: 0.16852  ,  Loss/l1: 0.01478  ,  Loss/location: 0.28585  ,  IoU: 0.84235
[train: 2, 100 / 117] FPS: 7.5 (101.2)  ,  DataTime: 3.665 (0.047)  ,  ForwardTime: 4.787  ,  TotalTime: 8.499  ,  Loss/total: 0.71340  ,  Loss/giou: 0.17291  ,  Loss/l1: 0.01623  ,  Loss/location: 0.28642  ,  IoU: 0.84047
[train: 2, 117 / 117] FPS: 8.2 (103.7)  ,  DataTime: 3.825 (0.044)  ,  ForwardTime: 3.918  ,  TotalTime: 7.788  ,  Loss/total: 0.68144  ,  Loss/giou: 0.16489  ,  Loss/l1: 0.01461  ,  Loss/location: 0.27864  ,  IoU: 0.84606[train: 2, 117 / 117] FPS: 8.2 (103.7)  ,  DataTime: 3.466 (0.047)  ,  ForwardTime: 4.274  ,  TotalTime: 7.788  ,  Loss/total: 0.69499  ,  Loss/giou: 0.16818  ,  Loss/l1: 0.01472  ,  Loss/location: 0.28503  ,  IoU: 0.84254

[train: 2, 117 / 117] FPS: 8.2 (103.7)  ,  DataTime: 3.485 (0.046)  ,  ForwardTime: 4.257  ,  TotalTime: 7.788  ,  Loss/total: 0.68711  ,  Loss/giou: 0.16764  ,  Loss/l1: 0.01551  ,  Loss/location: 0.27429  ,  IoU: 0.84556
[train: 2, 117 / 117] FPS: 8.2 (103.9)  ,  DataTime: 3.285 (0.045)  ,  ForwardTime: 4.457  ,  TotalTime: 7.787  ,  Loss/total: 0.70496  ,  Loss/giou: 0.17197  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28267  ,  IoU: 0.84077
[train: 2, 117 / 117] FPS: 8.2 (103.8)  ,  DataTime: 2.430 (0.042)  ,  ForwardTime: 5.315  ,  TotalTime: 7.787  ,  Loss/total: 0.70200  ,  Loss/giou: 0.17043  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28437  ,  IoU: 0.84195
[train: 2, 117 / 117] FPS: 8.2 (103.8)  ,  DataTime: 1.761 (0.047)  ,  ForwardTime: 5.978  ,  TotalTime: 7.787  ,  Loss/total: 0.68794  ,  Loss/giou: 0.16667  ,  Loss/l1: 0.01469  ,  Loss/location: 0.28114  ,  IoU: 0.84415
[train: 2, 117 / 117] FPS: 8.2 (103.8)  ,  DataTime: 4.879 (0.044)  ,  ForwardTime: 2.864  ,  TotalTime: 7.787  ,  Loss/total: 0.68566  ,  Loss/giou: 0.16783  ,  Loss/l1: 0.01506  ,  Loss/location: 0.27471  ,  IoU: 0.84418
[train: 2, 117 / 117] FPS: 8.2 (103.7)  ,  DataTime: 3.159 (0.047)  ,  ForwardTime: 4.582  ,  TotalTime: 7.788  ,  Loss/total: 0.71094  ,  Loss/giou: 0.17233  ,  Loss/l1: 0.01601  ,  Loss/location: 0.28620  ,  IoU: 0.84069
Epoch Time: 0:15:11.144189
Avg Data Time: 3.82515
Avg GPU Trans Time: 0.04430
Avg Forward Time: 3.91811
Epoch Time: 0:15:11.144234
Avg Data Time: 3.48492
Avg GPU Trans Time: 0.04567
Avg Forward Time: 4.25696
Epoch Time: 0:15:11.110884
Avg Data Time: 1.76141
Avg GPU Trans Time: 0.04739
Avg Forward Time: 5.97847
Epoch Time: 0:15:11.103927
Avg Data Time: 3.28481
Avg GPU Trans Time: 0.04545
Avg Forward Time: 4.45695
Epoch Time: 0:15:11.108349
Avg Data Time: 2.43034
Avg GPU Trans Time: 0.04206
Avg Forward Time: 5.31486
Epoch Time: 0:15:11.108902
Avg Data Time: 4.87915
Avg GPU Trans Time: 0.04387
Avg Forward Time: 2.86424
Epoch Time: 0:15:11.161061
Avg Data Time: 3.15942
Avg GPU Trans Time: 0.04677
Avg Forward Time: 4.58150
Epoch Time: 0:15:11.156074
Avg Data Time: 3.46616
Avg GPU Trans Time: 0.04743
Avg Forward Time: 4.27407
[train: 3, 50 / 117] FPS: 10.0 (99.7)  ,  DataTime: 2.345 (0.050)  ,  ForwardTime: 4.004  ,  TotalTime: 6.398  ,  Loss/total: 0.68316  ,  Loss/giou: 0.16557  ,  Loss/l1: 0.01424  ,  Loss/location: 0.28083  ,  IoU: 0.84476[train: 3, 50 / 117] FPS: 10.0 (99.9)  ,  DataTime: 1.685 (0.049)  ,  ForwardTime: 4.665  ,  TotalTime: 6.399  ,  Loss/total: 0.70399  ,  Loss/giou: 0.16901  ,  Loss/l1: 0.01507  ,  Loss/location: 0.29063  ,  IoU: 0.84268
[train: 3, 50 / 117] FPS: 10.0 (99.7)  ,  DataTime: 2.105 (0.050)  ,  ForwardTime: 4.244  ,  TotalTime: 6.399  ,  Loss/total: 0.69884  ,  Loss/giou: 0.17010  ,  Loss/l1: 0.01543  ,  Loss/location: 0.28149  ,  IoU: 0.84179

[train: 3, 50 / 117] FPS: 10.0 (99.7)  ,  DataTime: 3.469 (0.049)  ,  ForwardTime: 2.882  ,  TotalTime: 6.399  ,  Loss/total: 0.69436  ,  Loss/giou: 0.16912  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28009  ,  IoU: 0.84230
[train: 3, 50 / 117] FPS: 10.0 (99.7)  ,  DataTime: 2.109 (0.050)  ,  ForwardTime: 4.241  ,  TotalTime: 6.399  ,  Loss/total: 0.71346  ,  Loss/giou: 0.17115  ,  Loss/l1: 0.01571  ,  Loss/location: 0.29259  ,  IoU: 0.84117
[train: 3, 50 / 117] FPS: 10.0 (99.7)  ,  DataTime: 1.377 (0.048)  ,  ForwardTime: 4.974  ,  TotalTime: 6.399  ,  Loss/total: 0.71966  ,  Loss/giou: 0.17325  ,  Loss/l1: 0.01612  ,  Loss/location: 0.29253  ,  IoU: 0.83981
[train: 3, 50 / 117] FPS: 10.0 (99.8)  ,  DataTime: 3.951 (0.042)  ,  ForwardTime: 2.405  ,  TotalTime: 6.398  ,  Loss/total: 0.68565  ,  Loss/giou: 0.16669  ,  Loss/l1: 0.01500  ,  Loss/location: 0.27729  ,  IoU: 0.84521
[train: 3, 50 / 117] FPS: 10.0 (99.4)  ,  DataTime: 2.236 (0.044)  ,  ForwardTime: 4.119  ,  TotalTime: 6.399  ,  Loss/total: 0.70063  ,  Loss/giou: 0.16865  ,  Loss/l1: 0.01509  ,  Loss/location: 0.28788  ,  IoU: 0.84295
[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 1.150 (0.049)  ,  ForwardTime: 4.478  ,  TotalTime: 5.677  ,  Loss/total: 0.71647  ,  Loss/giou: 0.17332  ,  Loss/l1: 0.01607  ,  Loss/location: 0.28949  ,  IoU: 0.83997[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 1.194 (0.050)  ,  ForwardTime: 4.432  ,  TotalTime: 5.677  ,  Loss/total: 0.70046  ,  Loss/giou: 0.16778  ,  Loss/l1: 0.01485  ,  Loss/location: 0.29063  ,  IoU: 0.84341[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 0.929 (0.048)  ,  ForwardTime: 4.700  ,  TotalTime: 5.677  ,  Loss/total: 0.70549  ,  Loss/giou: 0.16976  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28857  ,  IoU: 0.84255
[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 2.003 (0.049)  ,  ForwardTime: 3.625  ,  TotalTime: 5.677  ,  Loss/total: 0.71166  ,  Loss/giou: 0.17162  ,  Loss/l1: 0.01549  ,  Loss/location: 0.29099  ,  IoU: 0.84032


[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 1.174 (0.050)  ,  ForwardTime: 4.454  ,  TotalTime: 5.677  ,  Loss/total: 0.68905  ,  Loss/giou: 0.16867  ,  Loss/l1: 0.01497  ,  Loss/location: 0.27688  ,  IoU: 0.84276[train: 3, 100 / 117] FPS: 11.3 (95.7)  ,  DataTime: 3.624 (0.042)  ,  ForwardTime: 2.011  ,  TotalTime: 5.677  ,  Loss/total: 0.68919  ,  Loss/giou: 0.16789  ,  Loss/l1: 0.01490  ,  Loss/location: 0.27890  ,  IoU: 0.84339

[train: 3, 100 / 117] FPS: 11.3 (95.8)  ,  DataTime: 1.337 (0.049)  ,  ForwardTime: 4.291  ,  TotalTime: 5.677  ,  Loss/total: 0.69850  ,  Loss/giou: 0.16799  ,  Loss/l1: 0.01482  ,  Loss/location: 0.28840  ,  IoU: 0.84313
[train: 3, 100 / 117] FPS: 11.3 (96.2)  ,  DataTime: 1.247 (0.045)  ,  ForwardTime: 4.386  ,  TotalTime: 5.677  ,  Loss/total: 0.68394  ,  Loss/giou: 0.16665  ,  Loss/l1: 0.01504  ,  Loss/location: 0.27546  ,  IoU: 0.84513
[train: 3, 117 / 117] FPS: 12.5 (100.4)  ,  DataTime: 1.072 (0.050)  ,  ForwardTime: 3.982  ,  TotalTime: 5.105  ,  Loss/total: 0.70351  ,  Loss/giou: 0.16813  ,  Loss/l1: 0.01496  ,  Loss/location: 0.29243  ,  IoU: 0.84315
[train: 3, 117 / 117] FPS: 12.5 (100.5)  ,  DataTime: 1.213 (0.049)  ,  ForwardTime: 3.843  ,  TotalTime: 5.105  ,  Loss/total: 0.69944  ,  Loss/giou: 0.16849  ,  Loss/l1: 0.01477  ,  Loss/location: 0.28861  ,  IoU: 0.84244
[train: 3, 117 / 117] FPS: 12.5 (100.4)  ,  DataTime: 1.009 (0.050)  ,  ForwardTime: 4.046  ,  TotalTime: 5.105  ,  Loss/total: 0.68501  ,  Loss/giou: 0.16752  ,  Loss/l1: 0.01486  ,  Loss/location: 0.27568  ,  IoU: 0.84362
[train: 3, 117 / 117] FPS: 12.5 (100.4)  ,  DataTime: 0.800 (0.047)  ,  ForwardTime: 4.257  ,  TotalTime: 5.105  ,  Loss/total: 0.70602  ,  Loss/giou: 0.16975  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28913  ,  IoU: 0.84255
[train: 3, 117 / 117] FPS: 12.5 (100.4)  ,  DataTime: 1.072 (0.045)  ,  ForwardTime: 3.988  ,  TotalTime: 5.105  ,  Loss/total: 0.68493  ,  Loss/giou: 0.16726  ,  Loss/l1: 0.01496  ,  Loss/location: 0.27559  ,  IoU: 0.84437
[train: 3, 117 / 117] FPS: 12.5 (100.5)  ,  DataTime: 1.006 (0.049)  ,  ForwardTime: 4.050  ,  TotalTime: 5.105  ,  Loss/total: 0.71430  ,  Loss/giou: 0.17305  ,  Loss/l1: 0.01601  ,  Loss/location: 0.28814  ,  IoU: 0.84022
[train: 3, 117 / 117] FPS: 12.5 (100.5)  ,  DataTime: 3.184 (0.042)  ,  ForwardTime: 1.878  ,  TotalTime: 5.105  ,  Loss/total: 0.68870  ,  Loss/giou: 0.16742  ,  Loss/l1: 0.01477  ,  Loss/location: 0.28001  ,  IoU: 0.84354
[train: 3, 117 / 117] FPS: 12.5 (100.4)  ,  DataTime: 1.757 (0.049)  ,  ForwardTime: 3.299  ,  TotalTime: 5.105  ,  Loss/total: 0.71087  ,  Loss/giou: 0.17177  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28907  ,  IoU: 0.84049
Epoch Time: 0:09:57.281860
Avg Data Time: 1.00892
Avg GPU Trans Time: 0.04973
Avg Forward Time: 4.04632
Epoch Time: 0:09:57.248905
Avg Data Time: 1.07211
Avg GPU Trans Time: 0.05048
Avg Forward Time: 3.98210
Epoch Time: 0:09:57.274242
Avg Data Time: 1.21316
Avg GPU Trans Time: 0.04891
Avg Forward Time: 3.84284
Epoch Time: 0:09:57.268290
Avg Data Time: 1.07224
Avg GPU Trans Time: 0.04485
Avg Forward Time: 3.98777
Epoch Time: 0:09:57.287116
Avg Data Time: 1.00623
Avg GPU Trans Time: 0.04877
Avg Forward Time: 4.05001
Epoch Time: 0:09:57.258067
Avg Data Time: 3.18437
Avg GPU Trans Time: 0.04204
Avg Forward Time: 1.87836
Epoch Time: 0:09:57.274105
Avg Data Time: 0.80001
Avg GPU Trans Time: 0.04743
Avg Forward Time: 4.25747
Epoch Time: 0:09:57.286717
Avg Data Time: 1.75718
Avg GPU Trans Time: 0.04863
Avg Forward Time: 3.29921
[train: 4, 50 / 117] FPS: 14.6 (96.3)  ,  DataTime: 2.403 (0.051)  ,  ForwardTime: 1.937  ,  TotalTime: 4.390  ,  Loss/total: 0.69577  ,  Loss/giou: 0.16590  ,  Loss/l1: 0.01441  ,  Loss/location: 0.29190  ,  IoU: 0.84410
[train: 4, 50 / 117] FPS: 14.6 (96.5)  ,  DataTime: 1.945 (0.051)  ,  ForwardTime: 2.394  ,  TotalTime: 4.390  ,  Loss/total: 0.67586  ,  Loss/giou: 0.16735  ,  Loss/l1: 0.01480  ,  Loss/location: 0.26719  ,  IoU: 0.84354
[train: 4, 50 / 117] FPS: 14.6 (96.5)  ,  DataTime: 1.381 (0.052)  ,  ForwardTime: 2.958  ,  TotalTime: 4.391  ,  Loss/total: 0.68246  ,  Loss/giou: 0.16565  ,  Loss/l1: 0.01502  ,  Loss/location: 0.27604  ,  IoU: 0.84605
[train: 4, 50 / 117] FPS: 14.6 (96.4)  ,  DataTime: 0.844 (0.050)  ,  ForwardTime: 3.496  ,  TotalTime: 4.391  ,  Loss/total: 0.70174  ,  Loss/giou: 0.16757  ,  Loss/l1: 0.01486  ,  Loss/location: 0.29230  ,  IoU: 0.84318
[train: 4, 50 / 117] FPS: 14.6 (96.4)  ,  DataTime: 3.078 (0.052)  ,  ForwardTime: 1.261  ,  TotalTime: 4.391  ,  Loss/total: 0.71759  ,  Loss/giou: 0.17344  ,  Loss/l1: 0.01576  ,  Loss/location: 0.29189  ,  IoU: 0.83891
[train: 4, 50 / 117] FPS: 14.6 (96.4)  ,  DataTime: 0.992 (0.049)  ,  ForwardTime: 3.349  ,  TotalTime: 4.390  ,  Loss/total: 0.68433  ,  Loss/giou: 0.16772  ,  Loss/l1: 0.01493  ,  Loss/location: 0.27424  ,  IoU: 0.84302
[train: 4, 50 / 117] FPS: 14.6 (96.4)  ,  DataTime: 1.318 (0.045)  ,  ForwardTime: 3.027  ,  TotalTime: 4.390  ,  Loss/total: 0.68528  ,  Loss/giou: 0.16737  ,  Loss/l1: 0.01481  ,  Loss/location: 0.27651  ,  IoU: 0.84438
[train: 4, 50 / 117] FPS: 14.6 (96.1)  ,  DataTime: 1.621 (0.049)  ,  ForwardTime: 2.720  ,  TotalTime: 4.390  ,  Loss/total: 0.69423  ,  Loss/giou: 0.17053  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27662  ,  IoU: 0.84115
[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 1.226 (0.051)  ,  ForwardTime: 2.768  ,  TotalTime: 4.046  ,  Loss/total: 0.70636  ,  Loss/giou: 0.16996  ,  Loss/l1: 0.01514  ,  Loss/location: 0.29073  ,  IoU: 0.84162
[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 0.864 (0.049)  ,  ForwardTime: 3.133  ,  TotalTime: 4.046  ,  Loss/total: 0.69039  ,  Loss/giou: 0.16822  ,  Loss/l1: 0.01493  ,  Loss/location: 0.27930  ,  IoU: 0.84289[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 1.714 (0.051)  ,  ForwardTime: 2.280  ,  TotalTime: 4.046  ,  Loss/total: 0.68759  ,  Loss/giou: 0.16867  ,  Loss/l1: 0.01511  ,  Loss/location: 0.27472  ,  IoU: 0.84274

[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 2.698 (0.052)  ,  ForwardTime: 1.297  ,  TotalTime: 4.046  ,  Loss/total: 0.71600  ,  Loss/giou: 0.17296  ,  Loss/l1: 0.01565  ,  Loss/location: 0.29181  ,  IoU: 0.83945
[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 0.712 (0.051)  ,  ForwardTime: 3.283  ,  TotalTime: 4.046  ,  Loss/total: 0.69254  ,  Loss/giou: 0.16815  ,  Loss/l1: 0.01525  ,  Loss/location: 0.27999  ,  IoU: 0.84396
[train: 4, 100 / 117] FPS: 15.8 (96.8)  ,  DataTime: 0.681 (0.047)  ,  ForwardTime: 3.319  ,  TotalTime: 4.046  ,  Loss/total: 0.69694  ,  Loss/giou: 0.16903  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28286  ,  IoU: 0.84317
[train: 4, 100 / 117] FPS: 15.8 (96.9)  ,  DataTime: 0.540 (0.051)  ,  ForwardTime: 3.456  ,  TotalTime: 4.046  ,  Loss/total: 0.71255  ,  Loss/giou: 0.17143  ,  Loss/l1: 0.01580  ,  Loss/location: 0.29067  ,  IoU: 0.84142
[train: 4, 100 / 117] FPS: 15.8 (96.8)  ,  DataTime: 0.559 (0.051)  ,  ForwardTime: 3.436  ,  TotalTime: 4.046  ,  Loss/total: 0.69217  ,  Loss/giou: 0.16908  ,  Loss/l1: 0.01524  ,  Loss/location: 0.27779  ,  IoU: 0.84261
[train: 4, 117 / 117] FPS: 17.7 (101.2)  ,  DataTime: 0.467 (0.050)  ,  ForwardTime: 3.099  ,  TotalTime: 3.617  ,  Loss/total: 0.71582  ,  Loss/giou: 0.17227  ,  Loss/l1: 0.01590  ,  Loss/location: 0.29180  ,  IoU: 0.84062
[train: 4, 117 / 117] FPS: 17.7 (101.1)  ,  DataTime: 0.744 (0.049)  ,  ForwardTime: 2.824  ,  TotalTime: 3.616  ,  Loss/total: 0.69610  ,  Loss/giou: 0.16897  ,  Loss/l1: 0.01509  ,  Loss/location: 0.28272  ,  IoU: 0.84250
[train: 4, 117 / 117] FPS: 17.7 (101.4)  ,  DataTime: 0.588 (0.047)  ,  ForwardTime: 2.982  ,  TotalTime: 3.616  ,  Loss/total: 0.70031  ,  Loss/giou: 0.16941  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28496  ,  IoU: 0.84276
[train: 4, 117 / 117] FPS: 17.7 (101.2)  ,  DataTime: 1.471 (0.051)  ,  ForwardTime: 2.094  ,  TotalTime: 3.616  ,  Loss/total: 0.68885  ,  Loss/giou: 0.16911  ,  Loss/l1: 0.01518  ,  Loss/location: 0.27476  ,  IoU: 0.84237
[train: 4, 117 / 117] FPS: 17.7 (101.1)  ,  DataTime: 0.484 (0.050)  ,  ForwardTime: 3.082  ,  TotalTime: 3.616  ,  Loss/total: 0.69562  ,  Loss/giou: 0.16925  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28127  ,  IoU: 0.84238
[train: 4, 117 / 117] FPS: 17.7 (101.1)  ,  DataTime: 1.054 (0.050)  ,  ForwardTime: 2.512  ,  TotalTime: 3.616  ,  Loss/total: 0.70089  ,  Loss/giou: 0.16866  ,  Loss/l1: 0.01500  ,  Loss/location: 0.28854  ,  IoU: 0.84278
[train: 4, 117 / 117] FPS: 17.7 (101.1)  ,  DataTime: 2.376 (0.051)  ,  ForwardTime: 1.189  ,  TotalTime: 3.617  ,  Loss/total: 0.71266  ,  Loss/giou: 0.17203  ,  Loss/l1: 0.01552  ,  Loss/location: 0.29097  ,  IoU: 0.84026
[train: 4, 117 / 117] FPS: 17.7 (100.8)  ,  DataTime: 0.614 (0.051)  ,  ForwardTime: 2.951  ,  TotalTime: 3.617  ,  Loss/total: 0.69339  ,  Loss/giou: 0.16761  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28287  ,  IoU: 0.84409
Epoch Time: 0:07:03.147109
Avg Data Time: 0.46700
Avg GPU Trans Time: 0.05035
Avg Forward Time: 3.09930
Epoch Time: 0:07:03.113654
Avg Data Time: 0.74396
Avg GPU Trans Time: 0.04865
Avg Forward Time: 2.82375
Epoch Time: 0:07:03.114610
Avg Data Time: 1.05351
Avg GPU Trans Time: 0.05041
Avg Forward Time: 2.51244
Epoch Time: 0:07:03.108944
Avg Data Time: 0.48368
Avg GPU Trans Time: 0.05016
Avg Forward Time: 3.08247
Epoch Time: 0:07:03.088684
Avg Data Time: 1.47120
Avg GPU Trans Time: 0.05071
Avg Forward Time: 2.09423
Epoch Time: 0:07:03.146134
Avg Data Time: 2.37594
Avg GPU Trans Time: 0.05130
Avg Forward Time: 1.18939
Epoch Time: 0:07:03.120832
Avg Data Time: 0.58804
Avg GPU Trans Time: 0.04657
Avg Forward Time: 2.98180
Epoch Time: 0:07:03.131385
Avg Data Time: 0.61443
Avg GPU Trans Time: 0.05073
Avg Forward Time: 2.95134
[train: 5, 50 / 117] FPS: 17.8 (94.2)  ,  DataTime: 0.748 (0.053)  ,  ForwardTime: 2.788  ,  TotalTime: 3.589  ,  Loss/total: 0.70747  ,  Loss/giou: 0.17010  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28911  ,  IoU: 0.84308
[train: 5, 50 / 117] FPS: 17.8 (94.3)  ,  DataTime: 0.769 (0.051)  ,  ForwardTime: 2.769  ,  TotalTime: 3.589  ,  Loss/total: 0.68564  ,  Loss/giou: 0.16633  ,  Loss/l1: 0.01513  ,  Loss/location: 0.27733  ,  IoU: 0.84569
[train: 5, 50 / 117] FPS: 17.8 (94.2)  ,  DataTime: 2.380 (0.054)  ,  ForwardTime: 1.155  ,  TotalTime: 3.588  ,  Loss/total: 0.69338  ,  Loss/giou: 0.16816  ,  Loss/l1: 0.01576  ,  Loss/location: 0.27824  ,  IoU: 0.84441
[train: 5, 50 / 117] FPS: 17.8 (94.0)  ,  DataTime: 0.859 (0.056)  ,  ForwardTime: 2.674  ,  TotalTime: 3.590  ,  Loss/total: 0.71672  ,  Loss/giou: 0.17334  ,  Loss/l1: 0.01536  ,  Loss/location: 0.29325  ,  IoU: 0.83831
[train: 5, 50 / 117] FPS: 17.8 (94.5)  ,  DataTime: 1.717 (0.050)  ,  ForwardTime: 1.821  ,  TotalTime: 3.588  ,  Loss/total: 0.72167  ,  Loss/giou: 0.17561  ,  Loss/l1: 0.01618  ,  Loss/location: 0.28952  ,  IoU: 0.83777
[train: 5, 50 / 117] FPS: 17.8 (94.3)  ,  DataTime: 0.454 (0.052)  ,  ForwardTime: 3.084  ,  TotalTime: 3.589  ,  Loss/total: 0.71880  ,  Loss/giou: 0.17217  ,  Loss/l1: 0.01611  ,  Loss/location: 0.29391  ,  IoU: 0.84159
[train: 5, 50 / 117] FPS: 17.8 (94.0)  ,  DataTime: 0.330 (0.053)  ,  ForwardTime: 3.206  ,  TotalTime: 3.589  ,  Loss/total: 0.69143  ,  Loss/giou: 0.16905  ,  Loss/l1: 0.01479  ,  Loss/location: 0.27936  ,  IoU: 0.84247[train: 5, 50 / 117] FPS: 17.8 (93.9)  ,  DataTime: 1.738 (0.054)  ,  ForwardTime: 1.796  ,  TotalTime: 3.588  ,  Loss/total: 0.70098  ,  Loss/giou: 0.16866  ,  Loss/l1: 0.01462  ,  Loss/location: 0.29055  ,  IoU: 0.84259

[train: 5, 100 / 117] FPS: 21.5 (95.1)  ,  DataTime: 0.453 (0.056)  ,  ForwardTime: 2.465  ,  TotalTime: 2.974  ,  Loss/total: 0.69142  ,  Loss/giou: 0.16790  ,  Loss/l1: 0.01464  ,  Loss/location: 0.28241  ,  IoU: 0.84258
[train: 5, 100 / 117] FPS: 21.5 (95.1)  ,  DataTime: 1.760 (0.054)  ,  ForwardTime: 1.159  ,  TotalTime: 2.973  ,  Loss/total: 0.67992  ,  Loss/giou: 0.16675  ,  Loss/l1: 0.01548  ,  Loss/location: 0.26900  ,  IoU: 0.84570
[train: 5, 100 / 117] FPS: 21.5 (95.1)  ,  DataTime: 0.396 (0.054)  ,  ForwardTime: 2.524  ,  TotalTime: 2.973  ,  Loss/total: 0.70674  ,  Loss/giou: 0.17069  ,  Loss/l1: 0.01552  ,  Loss/location: 0.28778  ,  IoU: 0.84203
[train: 5, 100 / 117] FPS: 21.5 (95.1)  ,  DataTime: 0.208 (0.053)  ,  ForwardTime: 2.713  ,  TotalTime: 2.974  ,  Loss/total: 0.69571  ,  Loss/giou: 0.16946  ,  Loss/l1: 0.01500  ,  Loss/location: 0.28180  ,  IoU: 0.84192
[train: 5, 100 / 117] FPS: 21.5 (95.1)  ,  DataTime: 1.096 (0.055)  ,  ForwardTime: 1.823  ,  TotalTime: 2.973  ,  Loss/total: 0.69520  ,  Loss/giou: 0.16821  ,  Loss/l1: 0.01481  ,  Loss/location: 0.28474  ,  IoU: 0.84321
[train: 5, 100 / 117] FPS: 21.5 (95.3)  ,  DataTime: 0.276 (0.051)  ,  ForwardTime: 2.646  ,  TotalTime: 2.974  ,  Loss/total: 0.70969  ,  Loss/giou: 0.16970  ,  Loss/l1: 0.01540  ,  Loss/location: 0.29328  ,  IoU: 0.84266
[train: 5, 100 / 117] FPS: 21.5 (95.4)  ,  DataTime: 0.883 (0.050)  ,  ForwardTime: 2.040  ,  TotalTime: 2.973  ,  Loss/total: 0.71611  ,  Loss/giou: 0.17333  ,  Loss/l1: 0.01576  ,  Loss/location: 0.29063  ,  IoU: 0.83937
[train: 5, 100 / 117] FPS: 21.5 (94.9)  ,  DataTime: 0.447 (0.053)  ,  ForwardTime: 2.473  ,  TotalTime: 2.974  ,  Loss/total: 0.69379  ,  Loss/giou: 0.16801  ,  Loss/l1: 0.01520  ,  Loss/location: 0.28178  ,  IoU: 0.84385
[train: 5, 117 / 117] FPS: 23.6 (104.3)  ,  DataTime: 0.184 (0.052)  ,  ForwardTime: 2.478  ,  TotalTime: 2.714  ,  Loss/total: 0.69580  ,  Loss/giou: 0.16897  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28259  ,  IoU: 0.84261[train: 5, 117 / 117] FPS: 23.6 (104.3)  ,  DataTime: 0.389 (0.053)  ,  ForwardTime: 2.272  ,  TotalTime: 2.714  ,  Loss/total: 0.70437  ,  Loss/giou: 0.16964  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28779  ,  IoU: 0.84250[train: 5, 117 / 117] FPS: 23.6 (104.2)  ,  DataTime: 0.242 (0.051)  ,  ForwardTime: 2.420  ,  TotalTime: 2.713  ,  Loss/total: 0.70346  ,  Loss/giou: 0.16937  ,  Loss/l1: 0.01532  ,  Loss/location: 0.28812  ,  IoU: 0.84288

[train: 5, 117 / 117] FPS: 23.6 (104.3)  ,  DataTime: 0.345 (0.054)  ,  ForwardTime: 2.314  ,  TotalTime: 2.713  ,  Loss/total: 0.70223  ,  Loss/giou: 0.16974  ,  Loss/l1: 0.01543  ,  Loss/location: 0.28561  ,  IoU: 0.84286

[train: 5, 117 / 117] FPS: 23.6 (104.2)  ,  DataTime: 0.762 (0.049)  ,  ForwardTime: 1.902  ,  TotalTime: 2.713  ,  Loss/total: 0.71170  ,  Loss/giou: 0.17170  ,  Loss/l1: 0.01545  ,  Loss/location: 0.29107  ,  IoU: 0.84053
[train: 5, 117 / 117] FPS: 23.6 (104.3)  ,  DataTime: 1.511 (0.053)  ,  ForwardTime: 1.149  ,  TotalTime: 2.713  ,  Loss/total: 0.67359  ,  Loss/giou: 0.16533  ,  Loss/l1: 0.01511  ,  Loss/location: 0.26739  ,  IoU: 0.84652
[train: 5, 117 / 117] FPS: 23.6 (104.2)  ,  DataTime: 0.393 (0.055)  ,  ForwardTime: 2.265  ,  TotalTime: 2.714  ,  Loss/total: 0.68705  ,  Loss/giou: 0.16683  ,  Loss/l1: 0.01463  ,  Loss/location: 0.28023  ,  IoU: 0.84371
[train: 5, 117 / 117] FPS: 23.6 (104.1)  ,  DataTime: 1.019 (0.054)  ,  ForwardTime: 1.641  ,  TotalTime: 2.713  ,  Loss/total: 0.69359  ,  Loss/giou: 0.16750  ,  Loss/l1: 0.01477  ,  Loss/location: 0.28474  ,  IoU: 0.84374
Epoch Time: 0:05:17.430284
Avg Data Time: 1.51055
Avg GPU Trans Time: 0.05311
Avg Forward Time: 1.14942
Epoch Time: 0:05:17.493151
Avg Data Time: 0.38856
Avg GPU Trans Time: 0.05327
Avg Forward Time: 2.27178
Epoch Time: 0:05:17.477995
Avg Data Time: 0.24227
Avg GPU Trans Time: 0.05100
Avg Forward Time: 2.42022
Epoch Time: 0:05:17.488413
Avg Data Time: 0.18379
Avg GPU Trans Time: 0.05209
Avg Forward Time: 2.47770
Epoch Time: 0:05:17.504376
Avg Data Time: 0.39316
Avg GPU Trans Time: 0.05543
Avg Forward Time: 2.26512
Epoch Time: 0:05:17.472455
Avg Data Time: 0.34549
Avg GPU Trans Time: 0.05363
Avg Forward Time: 2.31432
Epoch Time: 0:05:17.433712
Avg Data Time: 0.76172
Avg GPU Trans Time: 0.04943
Avg Forward Time: 1.90196
Epoch Time: 0:05:17.436511
Avg Data Time: 1.01854
Avg GPU Trans Time: 0.05388
Avg Forward Time: 1.64071
[val: 5, 50 / 78] FPS: 4.8 (181.9)  ,  DataTime: 12.999 (0.045)  ,  ForwardTime: 0.314  ,  TotalTime: 13.358  ,  Loss/total: 0.79035  ,  Loss/giou: 0.19284  ,  Loss/l1: 0.01984  ,  Loss/location: 0.30547  ,  IoU: 0.82681
[val: 5, 50 / 78] FPS: 4.8 (187.7)  ,  DataTime: 13.082 (0.039)  ,  ForwardTime: 0.255  ,  TotalTime: 13.376  ,  Loss/total: 0.81118  ,  Loss/giou: 0.19523  ,  Loss/l1: 0.02004  ,  Loss/location: 0.32053  ,  IoU: 0.82531
[val: 5, 50 / 78] FPS: 4.8 (189.2)  ,  DataTime: 12.906 (0.044)  ,  ForwardTime: 0.431  ,  TotalTime: 13.381  ,  Loss/total: 0.79359  ,  Loss/giou: 0.19018  ,  Loss/l1: 0.01993  ,  Loss/location: 0.31356  ,  IoU: 0.83027
[val: 5, 50 / 78] FPS: 4.7 (213.4)  ,  DataTime: 13.079 (0.037)  ,  ForwardTime: 0.458  ,  TotalTime: 13.574  ,  Loss/total: 0.80240  ,  Loss/giou: 0.19341  ,  Loss/l1: 0.02000  ,  Loss/location: 0.31560  ,  IoU: 0.82631
[val: 5, 50 / 78] FPS: 4.7 (187.0)  ,  DataTime: 12.866 (0.044)  ,  ForwardTime: 0.705  ,  TotalTime: 13.615  ,  Loss/total: 0.76235  ,  Loss/giou: 0.18781  ,  Loss/l1: 0.01927  ,  Loss/location: 0.29040  ,  IoU: 0.83211
[val: 5, 50 / 78] FPS: 4.7 (204.9)  ,  DataTime: 13.361 (0.041)  ,  ForwardTime: 0.258  ,  TotalTime: 13.660  ,  Loss/total: 0.79586  ,  Loss/giou: 0.19183  ,  Loss/l1: 0.01949  ,  Loss/location: 0.31476  ,  IoU: 0.82834
[val: 5, 50 / 78] FPS: 4.7 (208.6)  ,  DataTime: 13.416 (0.043)  ,  ForwardTime: 0.266  ,  TotalTime: 13.725  ,  Loss/total: 0.81852  ,  Loss/giou: 0.19847  ,  Loss/l1: 0.02051  ,  Loss/location: 0.31902  ,  IoU: 0.82343
[val: 5, 50 / 78] FPS: 4.7 (13.4)  ,  DataTime: 13.161 (0.038)  ,  ForwardTime: 0.556  ,  TotalTime: 13.755  ,  Loss/total: 0.80775  ,  Loss/giou: 0.19663  ,  Loss/l1: 0.02032  ,  Loss/location: 0.31289  ,  IoU: 0.82399
[val: 5, 78 / 78] FPS: 5.6 (191.2)  ,  DataTime: 11.054 (0.042)  ,  ForwardTime: 0.389  ,  TotalTime: 11.485  ,  Loss/total: 0.80295  ,  Loss/giou: 0.19367  ,  Loss/l1: 0.01989  ,  Loss/location: 0.31616  ,  IoU: 0.82627
Epoch Time: 0:14:55.797177
Avg Data Time: 11.05358
Avg GPU Trans Time: 0.04195
Avg Forward Time: 0.38905
[val: 5, 78 / 78] FPS: 5.5 (188.2)  ,  DataTime: 11.234 (0.043)  ,  ForwardTime: 0.260  ,  TotalTime: 11.537  ,  Loss/total: 0.82160  ,  Loss/giou: 0.19887  ,  Loss/l1: 0.02087  ,  Loss/location: 0.31949  ,  IoU: 0.82348
Epoch Time: 0:14:59.901555
Avg Data Time: 11.23441
Avg GPU Trans Time: 0.04326
Avg Forward Time: 0.25953
[val: 5, 78 / 78] FPS: 5.5 (198.1)  ,  DataTime: 11.096 (0.041)  ,  ForwardTime: 0.450  ,  TotalTime: 11.587  ,  Loss/total: 0.83276  ,  Loss/giou: 0.20194  ,  Loss/l1: 0.02121  ,  Loss/location: 0.32281  ,  IoU: 0.82056
Epoch Time: 0:15:03.761246
Avg Data Time: 11.09561
Avg GPU Trans Time: 0.04140
Avg Forward Time: 0.44966
[val: 5, 78 / 78] FPS: 5.5 (41.7)  ,  DataTime: 11.244 (0.047)  ,  ForwardTime: 0.298  ,  TotalTime: 11.589  ,  Loss/total: 0.78959  ,  Loss/giou: 0.19310  ,  Loss/l1: 0.01967  ,  Loss/location: 0.30501  ,  IoU: 0.82640
Epoch Time: 0:15:03.969640
Avg Data Time: 11.24445
Avg GPU Trans Time: 0.04739
Avg Forward Time: 0.29752
[val: 5, 78 / 78] FPS: 5.5 (193.2)  ,  DataTime: 11.288 (0.046)  ,  ForwardTime: 0.267  ,  TotalTime: 11.602  ,  Loss/total: 0.81707  ,  Loss/giou: 0.19785  ,  Loss/l1: 0.02054  ,  Loss/location: 0.31868  ,  IoU: 0.82413
Epoch Time: 0:15:04.921360
Avg Data Time: 11.28827
Avg GPU Trans Time: 0.04596
Avg Forward Time: 0.26733
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
directory doesn't exist. creating...
[val: 5, 78 / 78] FPS: 5.5 (15.4)  ,  DataTime: 11.349 (0.044)  ,  ForwardTime: 0.260  ,  TotalTime: 11.653  ,  Loss/total: 0.77982  ,  Loss/giou: 0.18854  ,  Loss/l1: 0.01907  ,  Loss/location: 0.30738  ,  IoU: 0.83105
Epoch Time: 0:15:08.904540
Avg Data Time: 11.34856
Avg GPU Trans Time: 0.04443
Avg Forward Time: 0.25963
[val: 5, 78 / 78] FPS: 5.5 (195.7)  ,  DataTime: 11.125 (0.045)  ,  ForwardTime: 0.546  ,  TotalTime: 11.716  ,  Loss/total: 0.78720  ,  Loss/giou: 0.19288  ,  Loss/l1: 0.02025  ,  Loss/location: 0.30020  ,  IoU: 0.82866
Epoch Time: 0:15:13.873620
Avg Data Time: 11.12497
Avg GPU Trans Time: 0.04529
Avg Forward Time: 0.54607
[val: 5, 78 / 78] FPS: 5.4 (198.7)  ,  DataTime: 11.360 (0.045)  ,  ForwardTime: 0.373  ,  TotalTime: 11.778  ,  Loss/total: 0.80579  ,  Loss/giou: 0.19415  ,  Loss/l1: 0.02054  ,  Loss/location: 0.31476  ,  IoU: 0.82704
Epoch Time: 0:15:18.651235
Avg Data Time: 11.35958
Avg GPU Trans Time: 0.04517
Avg Forward Time: 0.37283
[train: 6, 50 / 117] FPS: 19.5 (92.5)  ,  DataTime: 0.358 (0.057)  ,  ForwardTime: 2.866  ,  TotalTime: 3.280  ,  Loss/total: 0.70595  ,  Loss/giou: 0.17333  ,  Loss/l1: 0.01617  ,  Loss/location: 0.27846  ,  IoU: 0.84061
[train: 6, 50 / 117] FPS: 18.6 (92.2)  ,  DataTime: 0.219 (0.052)  ,  ForwardTime: 3.168  ,  TotalTime: 3.439  ,  Loss/total: 0.70759  ,  Loss/giou: 0.17186  ,  Loss/l1: 0.01551  ,  Loss/location: 0.28631  ,  IoU: 0.84038
[train: 6, 50 / 117] FPS: 20.1 (91.9)  ,  DataTime: 0.350 (0.050)  ,  ForwardTime: 2.777  ,  TotalTime: 3.176  ,  Loss/total: 0.70198  ,  Loss/giou: 0.16992  ,  Loss/l1: 0.01499  ,  Loss/location: 0.28719  ,  IoU: 0.84151
[train: 6, 50 / 117] FPS: 19.5 (92.0)  ,  DataTime: 0.587 (0.055)  ,  ForwardTime: 2.634  ,  TotalTime: 3.275  ,  Loss/total: 0.66833  ,  Loss/giou: 0.16248  ,  Loss/l1: 0.01396  ,  Loss/location: 0.27358  ,  IoU: 0.84712
[train: 6, 50 / 117] FPS: 19.1 (92.1)  ,  DataTime: 0.229 (0.054)  ,  ForwardTime: 3.074  ,  TotalTime: 3.357  ,  Loss/total: 0.68124  ,  Loss/giou: 0.16667  ,  Loss/l1: 0.01492  ,  Loss/location: 0.27327  ,  IoU: 0.84476
[train: 6, 50 / 117] FPS: 19.7 (91.9)  ,  DataTime: 0.442 (0.058)  ,  ForwardTime: 2.741  ,  TotalTime: 3.241  ,  Loss/total: 0.69773  ,  Loss/giou: 0.16841  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28413  ,  IoU: 0.84424
[train: 6, 50 / 117] FPS: 20.8 (91.9)  ,  DataTime: 0.875 (0.052)  ,  ForwardTime: 2.151  ,  TotalTime: 3.078  ,  Loss/total: 0.66616  ,  Loss/giou: 0.16267  ,  Loss/l1: 0.01406  ,  Loss/location: 0.27052  ,  IoU: 0.84785
[train: 6, 50 / 117] FPS: 21.5 (91.6)  ,  DataTime: 2.070 (0.053)  ,  ForwardTime: 0.860  ,  TotalTime: 2.983  ,  Loss/total: 0.70741  ,  Loss/giou: 0.17000  ,  Loss/l1: 0.01494  ,  Loss/location: 0.29271  ,  IoU: 0.84140
[train: 6, 100 / 117] FPS: 22.7 (94.3)  ,  DataTime: 0.459 (0.053)  ,  ForwardTime: 2.310  ,  TotalTime: 2.821  ,  Loss/total: 0.68072  ,  Loss/giou: 0.16539  ,  Loss/l1: 0.01440  ,  Loss/location: 0.27793  ,  IoU: 0.84565
[train: 6, 100 / 117] FPS: 21.3 (94.3)  ,  DataTime: 0.130 (0.053)  ,  ForwardTime: 2.819  ,  TotalTime: 3.002  ,  Loss/total: 0.69307  ,  Loss/giou: 0.16832  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28170  ,  IoU: 0.84280
[train: 6, 100 / 117] FPS: 21.9 (94.4)  ,  DataTime: 0.316 (0.055)  ,  ForwardTime: 2.549  ,  TotalTime: 2.920  ,  Loss/total: 0.66265  ,  Loss/giou: 0.16134  ,  Loss/l1: 0.01383  ,  Loss/location: 0.27080  ,  IoU: 0.84805
[train: 6, 100 / 117] FPS: 22.3 (94.4)  ,  DataTime: 0.443 (0.050)  ,  ForwardTime: 2.377  ,  TotalTime: 2.871  ,  Loss/total: 0.69699  ,  Loss/giou: 0.16992  ,  Loss/l1: 0.01532  ,  Loss/location: 0.28054  ,  IoU: 0.84249
[train: 6, 100 / 117] FPS: 21.9 (94.6)  ,  DataTime: 0.202 (0.057)  ,  ForwardTime: 2.663  ,  TotalTime: 2.923  ,  Loss/total: 0.69004  ,  Loss/giou: 0.16858  ,  Loss/l1: 0.01526  ,  Loss/location: 0.27660  ,  IoU: 0.84362
[train: 6, 100 / 117] FPS: 21.6 (94.4)  ,  DataTime: 0.138 (0.054)  ,  ForwardTime: 2.770  ,  TotalTime: 2.961  ,  Loss/total: 0.68609  ,  Loss/giou: 0.16837  ,  Loss/l1: 0.01495  ,  Loss/location: 0.27461  ,  IoU: 0.84277
[train: 6, 100 / 117] FPS: 22.0 (94.2)  ,  DataTime: 0.460 (0.057)  ,  ForwardTime: 2.385  ,  TotalTime: 2.903  ,  Loss/total: 0.69663  ,  Loss/giou: 0.16854  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28225  ,  IoU: 0.84417
[train: 6, 100 / 117] FPS: 23.1 (94.4)  ,  DataTime: 1.531 (0.051)  ,  ForwardTime: 1.192  ,  TotalTime: 2.774  ,  Loss/total: 0.69032  ,  Loss/giou: 0.16713  ,  Loss/l1: 0.01481  ,  Loss/location: 0.28200  ,  IoU: 0.84429
[train: 6, 117 / 117] FPS: 24.5 (102.4)  ,  DataTime: 0.276 (0.054)  ,  ForwardTime: 2.287  ,  TotalTime: 2.617  ,  Loss/total: 0.66944  ,  Loss/giou: 0.16291  ,  Loss/l1: 0.01416  ,  Loss/location: 0.27283  ,  IoU: 0.84692[train: 6, 117 / 117] FPS: 25.3 (102.5)  ,  DataTime: 0.398 (0.052)  ,  ForwardTime: 2.083  ,  TotalTime: 2.533  ,  Loss/total: 0.67883  ,  Loss/giou: 0.16470  ,  Loss/l1: 0.01432  ,  Loss/location: 0.27783  ,  IoU: 0.84609

[train: 6, 117 / 117] FPS: 24.9 (102.4)  ,  DataTime: 0.384 (0.050)  ,  ForwardTime: 2.141  ,  TotalTime: 2.575  ,  Loss/total: 0.69219  ,  Loss/giou: 0.16834  ,  Loss/l1: 0.01507  ,  Loss/location: 0.28018  ,  IoU: 0.84362
[train: 6, 117 / 117] FPS: 24.4 (102.4)  ,  DataTime: 0.179 (0.056)  ,  ForwardTime: 2.385  ,  TotalTime: 2.620  ,  Loss/total: 0.69098  ,  Loss/giou: 0.16904  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27622  ,  IoU: 0.84335
[train: 6, 117 / 117] FPS: 24.6 (102.4)  ,  DataTime: 0.399 (0.056)  ,  ForwardTime: 2.148  ,  TotalTime: 2.603  ,  Loss/total: 0.69406  ,  Loss/giou: 0.16747  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28309  ,  IoU: 0.84475
[train: 6, 117 / 117] FPS: 24.1 (102.4)  ,  DataTime: 0.123 (0.052)  ,  ForwardTime: 2.477  ,  TotalTime: 2.652  ,  Loss/total: 0.68775  ,  Loss/giou: 0.16844  ,  Loss/l1: 0.01504  ,  Loss/location: 0.27565  ,  IoU: 0.84286
[train: 6, 117 / 117] FPS: 23.8 (102.2)  ,  DataTime: 0.117 (0.052)  ,  ForwardTime: 2.518  ,  TotalTime: 2.687  ,  Loss/total: 0.68799  ,  Loss/giou: 0.16691  ,  Loss/l1: 0.01470  ,  Loss/location: 0.28069  ,  IoU: 0.84384
[train: 6, 117 / 117] FPS: 25.7 (102.1)  ,  DataTime: 1.341 (0.049)  ,  ForwardTime: 1.102  ,  TotalTime: 2.492  ,  Loss/total: 0.69317  ,  Loss/giou: 0.16828  ,  Loss/l1: 0.01501  ,  Loss/location: 0.28155  ,  IoU: 0.84352
Epoch Time: 0:05:06.485750
Avg Data Time: 0.17858
Avg GPU Trans Time: 0.05574
Avg Forward Time: 2.38522
Epoch Time: 0:05:06.231476
Avg Data Time: 0.27574
Avg GPU Trans Time: 0.05420
Avg Forward Time: 2.28742
Epoch Time: 0:05:01.287223
Avg Data Time: 0.38366
Avg GPU Trans Time: 0.05000
Avg Forward Time: 2.14144
Epoch Time: 0:04:56.378117
Avg Data Time: 0.39815
Avg GPU Trans Time: 0.05230
Avg Forward Time: 2.08269
Epoch Time: 0:05:14.419354
Avg Data Time: 0.11665
Avg GPU Trans Time: 0.05228
Avg Forward Time: 2.51841
Epoch Time: 0:04:51.603627
Avg Data Time: 1.34115
Avg GPU Trans Time: 0.04948
Avg Forward Time: 1.10171
Epoch Time: 0:05:10.316523
Avg Data Time: 0.12349
Avg GPU Trans Time: 0.05228
Avg Forward Time: 2.47650
Epoch Time: 0:05:04.523078
Avg Data Time: 0.39929
Avg GPU Trans Time: 0.05583
Avg Forward Time: 2.14765
[train: 7, 50 / 117] FPS: 23.8 (94.5)  ,  DataTime: 0.926 (0.057)  ,  ForwardTime: 1.707  ,  TotalTime: 2.691  ,  Loss/total: 0.67001  ,  Loss/giou: 0.16425  ,  Loss/l1: 0.01438  ,  Loss/location: 0.26962  ,  IoU: 0.84632[train: 7, 50 / 117] FPS: 23.8 (94.5)  ,  DataTime: 0.609 (0.051)  ,  ForwardTime: 2.031  ,  TotalTime: 2.691  ,  Loss/total: 0.69908  ,  Loss/giou: 0.16810  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28931  ,  IoU: 0.84296

[train: 7, 50 / 117] FPS: 23.8 (94.5)  ,  DataTime: 0.830 (0.050)  ,  ForwardTime: 1.812  ,  TotalTime: 2.691  ,  Loss/total: 0.70158  ,  Loss/giou: 0.17002  ,  Loss/l1: 0.01597  ,  Loss/location: 0.28168  ,  IoU: 0.84376
[train: 7, 50 / 117] FPS: 23.8 (94.5)  ,  DataTime: 0.471 (0.058)  ,  ForwardTime: 2.162  ,  TotalTime: 2.691  ,  Loss/total: 0.68280  ,  Loss/giou: 0.16621  ,  Loss/l1: 0.01482  ,  Loss/location: 0.27628  ,  IoU: 0.84524
[train: 7, 50 / 117] FPS: 23.8 (94.6)  ,  DataTime: 1.358 (0.056)  ,  ForwardTime: 1.277  ,  TotalTime: 2.691  ,  Loss/total: 0.70098  ,  Loss/giou: 0.17103  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28310  ,  IoU: 0.84092
[train: 7, 50 / 117] FPS: 23.8 (94.6)  ,  DataTime: 0.894 (0.054)  ,  ForwardTime: 1.742  ,  TotalTime: 2.691  ,  Loss/total: 0.69164  ,  Loss/giou: 0.16568  ,  Loss/l1: 0.01433  ,  Loss/location: 0.28864  ,  IoU: 0.84457
[train: 7, 50 / 117] FPS: 23.8 (94.4)  ,  DataTime: 0.653 (0.053)  ,  ForwardTime: 1.986  ,  TotalTime: 2.691  ,  Loss/total: 0.71511  ,  Loss/giou: 0.17458  ,  Loss/l1: 0.01610  ,  Loss/location: 0.28545  ,  IoU: 0.83914
[train: 7, 50 / 117] FPS: 23.8 (94.2)  ,  DataTime: 0.585 (0.053)  ,  ForwardTime: 2.053  ,  TotalTime: 2.691  ,  Loss/total: 0.69799  ,  Loss/giou: 0.16803  ,  Loss/l1: 0.01482  ,  Loss/location: 0.28785  ,  IoU: 0.84330
[train: 7, 100 / 117] FPS: 27.2 (89.1)  ,  DataTime: 0.484 (0.059)  ,  ForwardTime: 1.808  ,  TotalTime: 2.351  ,  Loss/total: 0.66158  ,  Loss/giou: 0.16152  ,  Loss/l1: 0.01401  ,  Loss/location: 0.26849  ,  IoU: 0.84835[train: 7, 100 / 117] FPS: 27.2 (89.1)  ,  DataTime: 0.347 (0.056)  ,  ForwardTime: 1.948  ,  TotalTime: 2.351  ,  Loss/total: 0.71165  ,  Loss/giou: 0.17299  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28652  ,  IoU: 0.84020

[train: 7, 100 / 117] FPS: 27.2 (89.1)  ,  DataTime: 0.451 (0.052)  ,  ForwardTime: 1.849  ,  TotalTime: 2.351  ,  Loss/total: 0.68704  ,  Loss/giou: 0.16688  ,  Loss/l1: 0.01507  ,  Loss/location: 0.27795  ,  IoU: 0.84493
[train: 7, 100 / 117] FPS: 27.2 (89.2)  ,  DataTime: 0.468 (0.056)  ,  ForwardTime: 1.827  ,  TotalTime: 2.351  ,  Loss/total: 0.70141  ,  Loss/giou: 0.16851  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28913  ,  IoU: 0.84287
[train: 7, 100 / 117] FPS: 27.2 (89.1)  ,  DataTime: 1.247 (0.058)  ,  ForwardTime: 1.046  ,  TotalTime: 2.351  ,  Loss/total: 0.68445  ,  Loss/giou: 0.16734  ,  Loss/l1: 0.01472  ,  Loss/location: 0.27618  ,  IoU: 0.84379
[train: 7, 100 / 117] FPS: 27.2 (89.4)  ,  DataTime: 0.258 (0.059)  ,  ForwardTime: 2.035  ,  TotalTime: 2.351  ,  Loss/total: 0.68268  ,  Loss/giou: 0.16570  ,  Loss/l1: 0.01461  ,  Loss/location: 0.27824  ,  IoU: 0.84517
[train: 7, 100 / 117] FPS: 27.2 (89.0)  ,  DataTime: 0.419 (0.056)  ,  ForwardTime: 1.876  ,  TotalTime: 2.351  ,  Loss/total: 0.69505  ,  Loss/giou: 0.16723  ,  Loss/l1: 0.01487  ,  Loss/location: 0.28626  ,  IoU: 0.84394
[train: 7, 100 / 117] FPS: 27.2 (89.1)  ,  DataTime: 0.324 (0.055)  ,  ForwardTime: 1.971  ,  TotalTime: 2.351  ,  Loss/total: 0.70231  ,  Loss/giou: 0.16923  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28859  ,  IoU: 0.84252
[train: 7, 117 / 117] FPS: 29.8 (98.3)  ,  DataTime: 0.420 (0.058)  ,  ForwardTime: 1.673  ,  TotalTime: 2.151  ,  Loss/total: 0.67379  ,  Loss/giou: 0.16403  ,  Loss/l1: 0.01440  ,  Loss/location: 0.27375  ,  IoU: 0.84644
[train: 7, 117 / 117] FPS: 29.8 (98.4)  ,  DataTime: 0.226 (0.058)  ,  ForwardTime: 1.867  ,  TotalTime: 2.151  ,  Loss/total: 0.68296  ,  Loss/giou: 0.16579  ,  Loss/l1: 0.01462  ,  Loss/location: 0.27826  ,  IoU: 0.84513
[train: 7, 117 / 117] FPS: 29.8 (98.4)  ,  DataTime: 1.096 (0.057)  ,  ForwardTime: 0.998  ,  TotalTime: 2.151  ,  Loss/total: 0.68007  ,  Loss/giou: 0.16633  ,  Loss/l1: 0.01460  ,  Loss/location: 0.27443  ,  IoU: 0.84458
[train: 7, 117 / 117] FPS: 29.8 (98.5)  ,  DataTime: 0.365 (0.055)  ,  ForwardTime: 1.732  ,  TotalTime: 2.151  ,  Loss/total: 0.69525  ,  Loss/giou: 0.16735  ,  Loss/l1: 0.01488  ,  Loss/location: 0.28616  ,  IoU: 0.84387
[train: 7, 117 / 117] FPS: 29.8 (98.3)  ,  DataTime: 0.297 (0.055)  ,  ForwardTime: 1.799  ,  TotalTime: 2.151  ,  Loss/total: 0.70148  ,  Loss/giou: 0.16913  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28755  ,  IoU: 0.84284
[train: 7, 117 / 117] FPS: 29.8 (98.3)  ,  DataTime: 0.397 (0.051)  ,  ForwardTime: 1.702  ,  TotalTime: 2.151  ,  Loss/total: 0.69090  ,  Loss/giou: 0.16768  ,  Loss/l1: 0.01509  ,  Loss/location: 0.28007  ,  IoU: 0.84409
[train: 7, 117 / 117] FPS: 29.8 (98.4)  ,  DataTime: 0.405 (0.056)  ,  ForwardTime: 1.690  ,  TotalTime: 2.151  ,  Loss/total: 0.69935  ,  Loss/giou: 0.16770  ,  Loss/l1: 0.01492  ,  Loss/location: 0.28936  ,  IoU: 0.84350
[train: 7, 117 / 117] FPS: 29.8 (98.2)  ,  DataTime: 0.303 (0.055)  ,  ForwardTime: 1.793  ,  TotalTime: 2.151  ,  Loss/total: 0.71140  ,  Loss/giou: 0.17319  ,  Loss/l1: 0.01581  ,  Loss/location: 0.28596  ,  IoU: 0.83982
Epoch Time: 0:04:11.621454
Avg Data Time: 0.41975
Avg GPU Trans Time: 0.05802
Avg Forward Time: 1.67284
Epoch Time: 0:04:11.639554
Avg Data Time: 0.39707
Avg GPU Trans Time: 0.05122
Avg Forward Time: 1.70248
Epoch Time: 0:04:11.637144
Avg Data Time: 0.30275
Avg GPU Trans Time: 0.05471
Avg Forward Time: 1.79329
Epoch Time: 0:04:11.619974
Avg Data Time: 0.22595
Avg GPU Trans Time: 0.05759
Avg Forward Time: 1.86706
Epoch Time: 0:04:11.619377
Avg Data Time: 0.36450
Avg GPU Trans Time: 0.05455
Avg Forward Time: 1.73154
Epoch Time: 0:04:11.612168
Avg Data Time: 1.09567
Avg GPU Trans Time: 0.05708
Avg Forward Time: 0.99778
Epoch Time: 0:04:11.628512
Avg Data Time: 0.29699
Avg GPU Trans Time: 0.05459
Avg Forward Time: 1.79909
Epoch Time: 0:04:11.611393
Avg Data Time: 0.40510
Avg GPU Trans Time: 0.05566
Avg Forward Time: 1.68977
[train: 8, 50 / 117] FPS: 28.3 (92.9)  ,  DataTime: 0.305 (0.054)  ,  ForwardTime: 1.899  ,  TotalTime: 2.258  ,  Loss/total: 0.69547  ,  Loss/giou: 0.16641  ,  Loss/l1: 0.01466  ,  Loss/location: 0.28932  ,  IoU: 0.84437
[train: 8, 50 / 117] FPS: 28.3 (92.7)  ,  DataTime: 1.046 (0.052)  ,  ForwardTime: 1.160  ,  TotalTime: 2.258  ,  Loss/total: 0.68791  ,  Loss/giou: 0.16922  ,  Loss/l1: 0.01515  ,  Loss/location: 0.27372  ,  IoU: 0.84260
[train: 8, 50 / 117] FPS: 28.3 (92.7)  ,  DataTime: 0.748 (0.056)  ,  ForwardTime: 1.455  ,  TotalTime: 2.258  ,  Loss/total: 0.68510  ,  Loss/giou: 0.16581  ,  Loss/l1: 0.01469  ,  Loss/location: 0.28002  ,  IoU: 0.84549[train: 8, 50 / 117] FPS: 28.3 (92.9)  ,  DataTime: 0.367 (0.056)  ,  ForwardTime: 1.834  ,  TotalTime: 2.258  ,  Loss/total: 0.69270  ,  Loss/giou: 0.16823  ,  Loss/l1: 0.01509  ,  Loss/location: 0.28082  ,  IoU: 0.84268

[train: 8, 50 / 117] FPS: 28.3 (92.8)  ,  DataTime: 0.434 (0.057)  ,  ForwardTime: 1.768  ,  TotalTime: 2.259  ,  Loss/total: 0.70655  ,  Loss/giou: 0.17294  ,  Loss/l1: 0.01604  ,  Loss/location: 0.28046  ,  IoU: 0.84055
[train: 8, 50 / 117] FPS: 28.3 (92.7)  ,  DataTime: 1.513 (0.052)  ,  ForwardTime: 0.694  ,  TotalTime: 2.259  ,  Loss/total: 0.69934  ,  Loss/giou: 0.16932  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28432  ,  IoU: 0.84236
[train: 8, 50 / 117] FPS: 28.3 (92.6)  ,  DataTime: 0.257 (0.056)  ,  ForwardTime: 1.945  ,  TotalTime: 2.258  ,  Loss/total: 0.69866  ,  Loss/giou: 0.16988  ,  Loss/l1: 0.01539  ,  Loss/location: 0.28195  ,  IoU: 0.84217
[train: 8, 50 / 117] FPS: 28.3 (93.0)  ,  DataTime: 0.356 (0.050)  ,  ForwardTime: 1.852  ,  TotalTime: 2.258  ,  Loss/total: 0.71169  ,  Loss/giou: 0.17350  ,  Loss/l1: 0.01609  ,  Loss/location: 0.28423  ,  IoU: 0.84027
[train: 8, 100 / 117] FPS: 29.6 (92.4)  ,  DataTime: 0.600 (0.055)  ,  ForwardTime: 1.506  ,  TotalTime: 2.161  ,  Loss/total: 0.69606  ,  Loss/giou: 0.16950  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28008  ,  IoU: 0.84332
[train: 8, 100 / 117] FPS: 29.6 (92.2)  ,  DataTime: 0.562 (0.053)  ,  ForwardTime: 1.546  ,  TotalTime: 2.161  ,  Loss/total: 0.69060  ,  Loss/giou: 0.16899  ,  Loss/l1: 0.01536  ,  Loss/location: 0.27583  ,  IoU: 0.84327[train: 8, 100 / 117] FPS: 29.6 (92.2)  ,  DataTime: 0.534 (0.055)  ,  ForwardTime: 1.572  ,  TotalTime: 2.161  ,  Loss/total: 0.69858  ,  Loss/giou: 0.17085  ,  Loss/l1: 0.01543  ,  Loss/location: 0.27975  ,  IoU: 0.84116

[train: 8, 100 / 117] FPS: 29.6 (92.2)  ,  DataTime: 0.155 (0.056)  ,  ForwardTime: 1.950  ,  TotalTime: 2.161  ,  Loss/total: 0.68304  ,  Loss/giou: 0.16715  ,  Loss/l1: 0.01489  ,  Loss/location: 0.27428  ,  IoU: 0.84416[train: 8, 100 / 117] FPS: 29.6 (92.0)  ,  DataTime: 0.205 (0.055)  ,  ForwardTime: 1.901  ,  TotalTime: 2.161  ,  Loss/total: 0.69956  ,  Loss/giou: 0.16878  ,  Loss/l1: 0.01494  ,  Loss/location: 0.28731  ,  IoU: 0.84258

[train: 8, 100 / 117] FPS: 29.6 (92.1)  ,  DataTime: 0.257 (0.056)  ,  ForwardTime: 1.849  ,  TotalTime: 2.161  ,  Loss/total: 0.70043  ,  Loss/giou: 0.17055  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28075  ,  IoU: 0.84250
[train: 8, 100 / 117] FPS: 29.6 (92.0)  ,  DataTime: 0.985 (0.051)  ,  ForwardTime: 1.125  ,  TotalTime: 2.161  ,  Loss/total: 0.70299  ,  Loss/giou: 0.17031  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28503  ,  IoU: 0.84212
[train: 8, 100 / 117] FPS: 29.6 (92.3)  ,  DataTime: 0.198 (0.049)  ,  ForwardTime: 1.914  ,  TotalTime: 2.161  ,  Loss/total: 0.70169  ,  Loss/giou: 0.17043  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28340  ,  IoU: 0.84219
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.486 (0.053)  ,  ForwardTime: 1.450  ,  TotalTime: 1.989  ,  Loss/total: 0.69625  ,  Loss/giou: 0.16971  ,  Loss/l1: 0.01545  ,  Loss/location: 0.27957  ,  IoU: 0.84262
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.181 (0.054)  ,  ForwardTime: 1.753  ,  TotalTime: 1.989  ,  Loss/total: 0.69812  ,  Loss/giou: 0.16847  ,  Loss/l1: 0.01488  ,  Loss/location: 0.28679  ,  IoU: 0.84284
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.138 (0.055)  ,  ForwardTime: 1.796  ,  TotalTime: 1.989  ,  Loss/total: 0.68132  ,  Loss/giou: 0.16678  ,  Loss/l1: 0.01487  ,  Loss/location: 0.27340  ,  IoU: 0.84447
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.510 (0.054)  ,  ForwardTime: 1.425  ,  TotalTime: 1.989  ,  Loss/total: 0.69482  ,  Loss/giou: 0.16969  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27878  ,  IoU: 0.84233
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.225 (0.055)  ,  ForwardTime: 1.709  ,  TotalTime: 1.989  ,  Loss/total: 0.70725  ,  Loss/giou: 0.17232  ,  Loss/l1: 0.01598  ,  Loss/location: 0.28269  ,  IoU: 0.84100
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.848 (0.050)  ,  ForwardTime: 1.091  ,  TotalTime: 1.989  ,  Loss/total: 0.69602  ,  Loss/giou: 0.16896  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28186  ,  IoU: 0.84312
[train: 8, 117 / 117] FPS: 32.2 (102.3)  ,  DataTime: 0.520 (0.054)  ,  ForwardTime: 1.415  ,  TotalTime: 1.989  ,  Loss/total: 0.69496  ,  Loss/giou: 0.16909  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28023  ,  IoU: 0.84338
[train: 8, 117 / 117] FPS: 32.2 (102.2)  ,  DataTime: 0.174 (0.048)  ,  ForwardTime: 1.766  ,  TotalTime: 1.988  ,  Loss/total: 0.70208  ,  Loss/giou: 0.17058  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28316  ,  IoU: 0.84217
Epoch Time: 0:03:52.670348
Avg Data Time: 0.48613
Avg GPU Trans Time: 0.05253
Avg Forward Time: 1.44998
Epoch Time: 0:03:52.692593
Avg Data Time: 0.22520
Avg GPU Trans Time: 0.05494
Avg Forward Time: 1.70868
Epoch Time: 0:03:52.640168
Avg Data Time: 0.17401
Avg GPU Trans Time: 0.04832
Avg Forward Time: 1.76605
Epoch Time: 0:03:52.662957
Avg Data Time: 0.50981
Avg GPU Trans Time: 0.05384
Avg Forward Time: 1.42493
Epoch Time: 0:03:52.661325
Avg Data Time: 0.18126
Avg GPU Trans Time: 0.05388
Avg Forward Time: 1.75342
Epoch Time: 0:03:52.681936
Avg Data Time: 0.51988
Avg GPU Trans Time: 0.05431
Avg Forward Time: 1.41454
Epoch Time: 0:03:52.663054
Avg Data Time: 0.13776
Avg GPU Trans Time: 0.05478
Avg Forward Time: 1.79604
Epoch Time: 0:03:52.698399
Avg Data Time: 0.84787
Avg GPU Trans Time: 0.04990
Avg Forward Time: 1.09111
[train: 9, 50 / 117] FPS: 29.8 (90.2)  ,  DataTime: 0.783 (0.055)  ,  ForwardTime: 1.306  ,  TotalTime: 2.144  ,  Loss/total: 0.71479  ,  Loss/giou: 0.17366  ,  Loss/l1: 0.01586  ,  Loss/location: 0.28814  ,  IoU: 0.83906
[train: 9, 50 / 117] FPS: 29.8 (90.4)  ,  DataTime: 0.808 (0.058)  ,  ForwardTime: 1.279  ,  TotalTime: 2.145  ,  Loss/total: 0.67409  ,  Loss/giou: 0.16482  ,  Loss/l1: 0.01468  ,  Loss/location: 0.27105  ,  IoU: 0.84619
[train: 9, 50 / 117] FPS: 29.8 (90.0)  ,  DataTime: 0.341 (0.058)  ,  ForwardTime: 1.745  ,  TotalTime: 2.144  ,  Loss/total: 0.70734  ,  Loss/giou: 0.16931  ,  Loss/l1: 0.01524  ,  Loss/location: 0.29253  ,  IoU: 0.84273
[train: 9, 50 / 117] FPS: 29.8 (90.0)  ,  DataTime: 0.311 (0.060)  ,  ForwardTime: 1.774  ,  TotalTime: 2.144  ,  Loss/total: 0.72696  ,  Loss/giou: 0.17476  ,  Loss/l1: 0.01647  ,  Loss/location: 0.29508  ,  IoU: 0.83899
[train: 9, 50 / 117] FPS: 29.8 (90.1)  ,  DataTime: 0.451 (0.059)  ,  ForwardTime: 1.635  ,  TotalTime: 2.145  ,  Loss/total: 0.70481  ,  Loss/giou: 0.17253  ,  Loss/l1: 0.01595  ,  Loss/location: 0.27999  ,  IoU: 0.84088
[train: 9, 50 / 117] FPS: 29.8 (90.1)  ,  DataTime: 0.544 (0.060)  ,  ForwardTime: 1.542  ,  TotalTime: 2.146  ,  Loss/total: 0.69043  ,  Loss/giou: 0.16738  ,  Loss/l1: 0.01493  ,  Loss/location: 0.28103  ,  IoU: 0.84405
[train: 9, 50 / 117] FPS: 29.8 (90.1)  ,  DataTime: 0.621 (0.060)  ,  ForwardTime: 1.463  ,  TotalTime: 2.145  ,  Loss/total: 0.66702  ,  Loss/giou: 0.16142  ,  Loss/l1: 0.01351  ,  Loss/location: 0.27665  ,  IoU: 0.84742
[train: 9, 50 / 117] FPS: 29.8 (89.6)  ,  DataTime: 0.710 (0.057)  ,  ForwardTime: 1.378  ,  TotalTime: 2.144  ,  Loss/total: 0.72137  ,  Loss/giou: 0.17499  ,  Loss/l1: 0.01612  ,  Loss/location: 0.29080  ,  IoU: 0.83786
[train: 9, 100 / 117] FPS: 33.9 (90.5)  ,  DataTime: 0.413 (0.056)  ,  ForwardTime: 1.417  ,  TotalTime: 1.885  ,  Loss/total: 0.69985  ,  Loss/giou: 0.17072  ,  Loss/l1: 0.01562  ,  Loss/location: 0.28030  ,  IoU: 0.84197
[train: 9, 100 / 117] FPS: 33.9 (90.5)  ,  DataTime: 0.334 (0.059)  ,  ForwardTime: 1.492  ,  TotalTime: 1.885  ,  Loss/total: 0.69642  ,  Loss/giou: 0.16850  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28376  ,  IoU: 0.84286
[train: 9, 100 / 117] FPS: 33.9 (90.3)  ,  DataTime: 0.183 (0.058)  ,  ForwardTime: 1.644  ,  TotalTime: 1.885  ,  Loss/total: 0.71444  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01584  ,  Loss/location: 0.29192  ,  IoU: 0.84107
[train: 9, 100 / 117] FPS: 33.9 (90.3)  ,  DataTime: 0.329 (0.059)  ,  ForwardTime: 1.498  ,  TotalTime: 1.886  ,  Loss/total: 0.68469  ,  Loss/giou: 0.16529  ,  Loss/l1: 0.01457  ,  Loss/location: 0.28128  ,  IoU: 0.84567
[train: 9, 100 / 117] FPS: 33.9 (90.5)  ,  DataTime: 0.411 (0.056)  ,  ForwardTime: 1.419  ,  TotalTime: 1.885  ,  Loss/total: 0.70732  ,  Loss/giou: 0.17133  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28727  ,  IoU: 0.84077
[train: 9, 100 / 117] FPS: 33.9 (90.2)  ,  DataTime: 0.426 (0.059)  ,  ForwardTime: 1.401  ,  TotalTime: 1.885  ,  Loss/total: 0.68203  ,  Loss/giou: 0.16684  ,  Loss/l1: 0.01486  ,  Loss/location: 0.27407  ,  IoU: 0.84453
[train: 9, 100 / 117] FPS: 33.9 (90.5)  ,  DataTime: 0.642 (0.059)  ,  ForwardTime: 1.185  ,  TotalTime: 1.885  ,  Loss/total: 0.71378  ,  Loss/giou: 0.17241  ,  Loss/l1: 0.01557  ,  Loss/location: 0.29112  ,  IoU: 0.83990
[train: 9, 100 / 117] FPS: 33.9 (90.2)  ,  DataTime: 0.214 (0.058)  ,  ForwardTime: 1.612  ,  TotalTime: 1.885  ,  Loss/total: 0.68670  ,  Loss/giou: 0.16650  ,  Loss/l1: 0.01482  ,  Loss/location: 0.27961  ,  IoU: 0.84499
[train: 9, 117 / 117] FPS: 37.2 (102.0)  ,  DataTime: 0.298 (0.058)  ,  ForwardTime: 1.362  ,  TotalTime: 1.719  ,  Loss/total: 0.68670  ,  Loss/giou: 0.16572  ,  Loss/l1: 0.01462  ,  Loss/location: 0.28215  ,  IoU: 0.84536[train: 9, 117 / 117] FPS: 37.2 (102.0)  ,  DataTime: 0.162 (0.057)  ,  ForwardTime: 1.499  ,  TotalTime: 1.718  ,  Loss/total: 0.71442  ,  Loss/giou: 0.17155  ,  Loss/l1: 0.01584  ,  Loss/location: 0.29213  ,  IoU: 0.84111

[train: 9, 117 / 117] FPS: 37.2 (102.2)  ,  DataTime: 0.370 (0.058)  ,  ForwardTime: 1.290  ,  TotalTime: 1.718  ,  Loss/total: 0.68948  ,  Loss/giou: 0.16841  ,  Loss/l1: 0.01503  ,  Loss/location: 0.27753  ,  IoU: 0.84328[train: 9, 117 / 117] FPS: 37.2 (102.1)  ,  DataTime: 0.554 (0.058)  ,  ForwardTime: 1.106  ,  TotalTime: 1.718  ,  Loss/total: 0.71158  ,  Loss/giou: 0.17175  ,  Loss/l1: 0.01534  ,  Loss/location: 0.29137  ,  IoU: 0.84024

[train: 9, 117 / 117] FPS: 37.2 (102.2)  ,  DataTime: 0.357 (0.055)  ,  ForwardTime: 1.306  ,  TotalTime: 1.718  ,  Loss/total: 0.70284  ,  Loss/giou: 0.17011  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28658  ,  IoU: 0.84159
[train: 9, 117 / 117] FPS: 37.2 (101.9)  ,  DataTime: 0.189 (0.057)  ,  ForwardTime: 1.472  ,  TotalTime: 1.718  ,  Loss/total: 0.69112  ,  Loss/giou: 0.16796  ,  Loss/l1: 0.01505  ,  Loss/location: 0.27993  ,  IoU: 0.84388
[train: 9, 117 / 117] FPS: 37.2 (102.1)  ,  DataTime: 0.292 (0.058)  ,  ForwardTime: 1.368  ,  TotalTime: 1.718  ,  Loss/total: 0.70028  ,  Loss/giou: 0.16939  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28469  ,  IoU: 0.84248
[train: 9, 117 / 117] FPS: 37.2 (101.7)  ,  DataTime: 0.358 (0.055)  ,  ForwardTime: 1.306  ,  TotalTime: 1.718  ,  Loss/total: 0.69499  ,  Loss/giou: 0.16925  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27993  ,  IoU: 0.84295
Epoch Time: 0:03:21.029606
Avg Data Time: 0.35691
Avg GPU Trans Time: 0.05495
Avg Forward Time: 1.30634
Epoch Time: 0:03:21.032561
Avg Data Time: 0.16178
Avg GPU Trans Time: 0.05704
Avg Forward Time: 1.49941
Epoch Time: 0:03:21.058651
Avg Data Time: 0.37014
Avg GPU Trans Time: 0.05782
Avg Forward Time: 1.29049
Epoch Time: 0:03:21.035114
Avg Data Time: 0.35794
Avg GPU Trans Time: 0.05456
Avg Forward Time: 1.30575
Epoch Time: 0:03:21.057407
Avg Data Time: 0.55443
Avg GPU Trans Time: 0.05848
Avg Forward Time: 1.10553
Epoch Time: 0:03:21.084154
Avg Data Time: 0.29850
Avg GPU Trans Time: 0.05801
Avg Forward Time: 1.36216
Epoch Time: 0:03:21.034616
Avg Data Time: 0.29152
Avg GPU Trans Time: 0.05848
Avg Forward Time: 1.36824
Epoch Time: 0:03:21.025021
Avg Data Time: 0.18936
Avg GPU Trans Time: 0.05678
Avg Forward Time: 1.47202
[train: 10, 50 / 117] FPS: 33.1 (92.5)  ,  DataTime: 0.301 (0.061)  ,  ForwardTime: 1.574  ,  TotalTime: 1.936  ,  Loss/total: 0.72664  ,  Loss/giou: 0.17727  ,  Loss/l1: 0.01682  ,  Loss/location: 0.28799  ,  IoU: 0.83763[train: 10, 50 / 117] FPS: 33.1 (92.4)  ,  DataTime: 0.494 (0.054)  ,  ForwardTime: 1.388  ,  TotalTime: 1.936  ,  Loss/total: 0.71689  ,  Loss/giou: 0.17336  ,  Loss/l1: 0.01595  ,  Loss/location: 0.29045  ,  IoU: 0.83962

[train: 10, 50 / 117] FPS: 33.1 (92.7)  ,  DataTime: 0.294 (0.057)  ,  ForwardTime: 1.585  ,  TotalTime: 1.936  ,  Loss/total: 0.71065  ,  Loss/giou: 0.17099  ,  Loss/l1: 0.01506  ,  Loss/location: 0.29335  ,  IoU: 0.83969[train: 10, 50 / 117] FPS: 33.1 (92.4)  ,  DataTime: 0.396 (0.059)  ,  ForwardTime: 1.481  ,  TotalTime: 1.936  ,  Loss/total: 0.71060  ,  Loss/giou: 0.17116  ,  Loss/l1: 0.01580  ,  Loss/location: 0.28931  ,  IoU: 0.84158

[train: 10, 50 / 117] FPS: 33.1 (92.3)  ,  DataTime: 1.094 (0.061)  ,  ForwardTime: 0.781  ,  TotalTime: 1.936  ,  Loss/total: 0.69682  ,  Loss/giou: 0.16935  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28167  ,  IoU: 0.84298
[train: 10, 50 / 117] FPS: 33.1 (92.6)  ,  DataTime: 0.317 (0.058)  ,  ForwardTime: 1.562  ,  TotalTime: 1.936  ,  Loss/total: 0.69659  ,  Loss/giou: 0.17207  ,  Loss/l1: 0.01566  ,  Loss/location: 0.27418  ,  IoU: 0.84105
[train: 10, 50 / 117] FPS: 33.1 (92.2)  ,  DataTime: 0.301 (0.063)  ,  ForwardTime: 1.571  ,  TotalTime: 1.936  ,  Loss/total: 0.67162  ,  Loss/giou: 0.16278  ,  Loss/l1: 0.01406  ,  Loss/location: 0.27576  ,  IoU: 0.84713
[train: 10, 50 / 117] FPS: 33.0 (92.1)  ,  DataTime: 0.397 (0.062)  ,  ForwardTime: 1.477  ,  TotalTime: 1.937  ,  Loss/total: 0.70143  ,  Loss/giou: 0.16881  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28728  ,  IoU: 0.84333
[train: 10, 100 / 117] FPS: 39.2 (88.1)  ,  DataTime: 0.175 (0.065)  ,  ForwardTime: 1.393  ,  TotalTime: 1.632  ,  Loss/total: 0.70943  ,  Loss/giou: 0.16983  ,  Loss/l1: 0.01521  ,  Loss/location: 0.29371  ,  IoU: 0.84224
[train: 10, 100 / 117] FPS: 39.2 (88.0)  ,  DataTime: 0.170 (0.059)  ,  ForwardTime: 1.404  ,  TotalTime: 1.633  ,  Loss/total: 0.71013  ,  Loss/giou: 0.17090  ,  Loss/l1: 0.01524  ,  Loss/location: 0.29212  ,  IoU: 0.84063
[train: 10, 100 / 117] FPS: 39.2 (88.0)  ,  DataTime: 0.223 (0.061)  ,  ForwardTime: 1.349  ,  TotalTime: 1.633  ,  Loss/total: 0.69863  ,  Loss/giou: 0.16842  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28538  ,  IoU: 0.84392
[train: 10, 100 / 117] FPS: 39.2 (87.7)  ,  DataTime: 0.213 (0.059)  ,  ForwardTime: 1.360  ,  TotalTime: 1.633  ,  Loss/total: 0.68220  ,  Loss/giou: 0.16843  ,  Loss/l1: 0.01513  ,  Loss/location: 0.26968  ,  IoU: 0.84361
[train: 10, 100 / 117] FPS: 39.2 (87.9)  ,  DataTime: 0.833 (0.062)  ,  ForwardTime: 0.738  ,  TotalTime: 1.633  ,  Loss/total: 0.68879  ,  Loss/giou: 0.16849  ,  Loss/l1: 0.01498  ,  Loss/location: 0.27694  ,  IoU: 0.84297
[train: 10, 100 / 117] FPS: 39.2 (87.7)  ,  DataTime: 0.221 (0.062)  ,  ForwardTime: 1.350  ,  TotalTime: 1.633  ,  Loss/total: 0.70351  ,  Loss/giou: 0.16902  ,  Loss/l1: 0.01534  ,  Loss/location: 0.28874  ,  IoU: 0.84299
[train: 10, 100 / 117] FPS: 39.2 (87.8)  ,  DataTime: 0.284 (0.055)  ,  ForwardTime: 1.293  ,  TotalTime: 1.633  ,  Loss/total: 0.70164  ,  Loss/giou: 0.17097  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28168  ,  IoU: 0.84174
[train: 10, 100 / 117] FPS: 39.2 (87.5)  ,  DataTime: 0.174 (0.063)  ,  ForwardTime: 1.396  ,  TotalTime: 1.633  ,  Loss/total: 0.70382  ,  Loss/giou: 0.17141  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28221  ,  IoU: 0.84155
[train: 10, 117 / 117] FPS: 42.1 (102.0)  ,  DataTime: 0.188 (0.058)  ,  ForwardTime: 1.274  ,  TotalTime: 1.520  ,  Loss/total: 0.68479  ,  Loss/giou: 0.16854  ,  Loss/l1: 0.01513  ,  Loss/location: 0.27205  ,  IoU: 0.84341[train: 10, 117 / 117] FPS: 42.1 (102.0)  ,  DataTime: 0.195 (0.060)  ,  ForwardTime: 1.265  ,  TotalTime: 1.520  ,  Loss/total: 0.70339  ,  Loss/giou: 0.16936  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28762  ,  IoU: 0.84282[train: 10, 117 / 117] FPS: 42.1 (102.2)  ,  DataTime: 0.155 (0.061)  ,  ForwardTime: 1.305  ,  TotalTime: 1.520  ,  Loss/total: 0.69706  ,  Loss/giou: 0.17012  ,  Loss/l1: 0.01553  ,  Loss/location: 0.27918  ,  IoU: 0.84260


[train: 10, 117 / 117] FPS: 42.1 (101.9)  ,  DataTime: 0.151 (0.057)  ,  ForwardTime: 1.312  ,  TotalTime: 1.520  ,  Loss/total: 0.71175  ,  Loss/giou: 0.17185  ,  Loss/l1: 0.01548  ,  Loss/location: 0.29065  ,  IoU: 0.84007
[train: 10, 117 / 117] FPS: 42.1 (101.9)  ,  DataTime: 0.741 (0.060)  ,  ForwardTime: 0.719  ,  TotalTime: 1.520  ,  Loss/total: 0.69038  ,  Loss/giou: 0.16913  ,  Loss/l1: 0.01514  ,  Loss/location: 0.27641  ,  IoU: 0.84252
[train: 10, 117 / 117] FPS: 42.1 (101.5)  ,  DataTime: 0.156 (0.063)  ,  ForwardTime: 1.302  ,  TotalTime: 1.520  ,  Loss/total: 0.70417  ,  Loss/giou: 0.16966  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28900  ,  IoU: 0.84255
[train: 10, 117 / 117] FPS: 42.1 (101.7)  ,  DataTime: 0.204 (0.059)  ,  ForwardTime: 1.258  ,  TotalTime: 1.520  ,  Loss/total: 0.70137  ,  Loss/giou: 0.16927  ,  Loss/l1: 0.01539  ,  Loss/location: 0.28586  ,  IoU: 0.84321
[train: 10, 117 / 117] FPS: 42.1 (101.0)  ,  DataTime: 0.249 (0.054)  ,  ForwardTime: 1.217  ,  TotalTime: 1.520  ,  Loss/total: 0.69469  ,  Loss/giou: 0.16929  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27947  ,  IoU: 0.84304
Epoch Time: 0:02:57.858799
Avg Data Time: 0.15467
Avg GPU Trans Time: 0.06096
Avg Forward Time: 1.30453
Epoch Time: 0:02:57.864214
Avg Data Time: 0.19503
Avg GPU Trans Time: 0.06009
Avg Forward Time: 1.26509
Epoch Time: 0:02:57.871432
Avg Data Time: 0.20381
Avg GPU Trans Time: 0.05886
Avg Forward Time: 1.25760
Epoch Time: 0:02:57.855963
Avg Data Time: 0.74122
Avg GPU Trans Time: 0.06011
Avg Forward Time: 0.71881
Epoch Time: 0:02:57.865343
Avg Data Time: 0.18767
Avg GPU Trans Time: 0.05815
Avg Forward Time: 1.27439
Epoch Time: 0:02:57.855287
Avg Data Time: 0.15110
Avg GPU Trans Time: 0.05745
Avg Forward Time: 1.31158
Epoch Time: 0:02:57.861166
Avg Data Time: 0.24893
Avg GPU Trans Time: 0.05438
Avg Forward Time: 1.21687
Epoch Time: 0:02:57.845647
Avg Data Time: 0.15561
Avg GPU Trans Time: 0.06273
Avg Forward Time: 1.30172
[val: 10, 50 / 78] FPS: 8.2 (28.7)  ,  DataTime: 7.480 (0.049)  ,  ForwardTime: 0.256  ,  TotalTime: 7.785  ,  Loss/total: 0.76728  ,  Loss/giou: 0.18509  ,  Loss/l1: 0.01914  ,  Loss/location: 0.30141  ,  IoU: 0.83387
[val: 10, 50 / 78] FPS: 8.1 (199.0)  ,  DataTime: 7.257 (0.049)  ,  ForwardTime: 0.591  ,  TotalTime: 7.897  ,  Loss/total: 0.79508  ,  Loss/giou: 0.19448  ,  Loss/l1: 0.02022  ,  Loss/location: 0.30503  ,  IoU: 0.82654
[val: 10, 50 / 78] FPS: 8.1 (191.2)  ,  DataTime: 7.626 (0.047)  ,  ForwardTime: 0.257  ,  TotalTime: 7.930  ,  Loss/total: 0.82477  ,  Loss/giou: 0.19896  ,  Loss/l1: 0.02121  ,  Loss/location: 0.32082  ,  IoU: 0.82316
[val: 10, 50 / 78] FPS: 8.0 (191.1)  ,  DataTime: 7.381 (0.046)  ,  ForwardTime: 0.618  ,  TotalTime: 8.046  ,  Loss/total: 0.75761  ,  Loss/giou: 0.18347  ,  Loss/l1: 0.01830  ,  Loss/location: 0.29919  ,  IoU: 0.83459
[val: 10, 50 / 78] FPS: 7.9 (191.1)  ,  DataTime: 7.437 (0.048)  ,  ForwardTime: 0.592  ,  TotalTime: 8.076  ,  Loss/total: 0.81254  ,  Loss/giou: 0.19811  ,  Loss/l1: 0.02103  ,  Loss/location: 0.31118  ,  IoU: 0.82471
[val: 10, 50 / 78] FPS: 7.8 (188.8)  ,  DataTime: 7.904 (0.049)  ,  ForwardTime: 0.252  ,  TotalTime: 8.205  ,  Loss/total: 0.77200  ,  Loss/giou: 0.18734  ,  Loss/l1: 0.01940  ,  Loss/location: 0.30033  ,  IoU: 0.83172
[val: 10, 50 / 78] FPS: 7.8 (185.0)  ,  DataTime: 7.880 (0.048)  ,  ForwardTime: 0.314  ,  TotalTime: 8.242  ,  Loss/total: 0.77530  ,  Loss/giou: 0.18738  ,  Loss/l1: 0.01881  ,  Loss/location: 0.30650  ,  IoU: 0.83144
[val: 10, 50 / 78] FPS: 7.5 (179.9)  ,  DataTime: 8.148 (0.048)  ,  ForwardTime: 0.329  ,  TotalTime: 8.524  ,  Loss/total: 0.79646  ,  Loss/giou: 0.19383  ,  Loss/l1: 0.02005  ,  Loss/location: 0.30857  ,  IoU: 0.82644
[val: 10, 78 / 78] FPS: 9.3 (24.9)  ,  DataTime: 6.335 (0.047)  ,  ForwardTime: 0.488  ,  TotalTime: 6.869  ,  Loss/total: 0.77145  ,  Loss/giou: 0.18779  ,  Loss/l1: 0.01885  ,  Loss/location: 0.30163  ,  IoU: 0.83057
Epoch Time: 0:08:55.779692
Avg Data Time: 6.33455
Avg GPU Trans Time: 0.04664
Avg Forward Time: 0.48778
[val: 10, 78 / 78] FPS: 9.3 (44.0)  ,  DataTime: 6.529 (0.048)  ,  ForwardTime: 0.303  ,  TotalTime: 6.880  ,  Loss/total: 0.80242  ,  Loss/giou: 0.19429  ,  Loss/l1: 0.02011  ,  Loss/location: 0.31329  ,  IoU: 0.82576
Epoch Time: 0:08:56.671479
Avg Data Time: 6.52948
Avg GPU Trans Time: 0.04771
Avg Forward Time: 0.30321
[val: 10, 78 / 78] FPS: 9.3 (202.1)  ,  DataTime: 6.587 (0.049)  ,  ForwardTime: 0.256  ,  TotalTime: 6.891  ,  Loss/total: 0.78906  ,  Loss/giou: 0.19048  ,  Loss/l1: 0.01988  ,  Loss/location: 0.30871  ,  IoU: 0.82954
[val: 10, 78 / 78] FPS: 9.3 (203.6)  ,  DataTime: 6.372 (0.048)  ,  ForwardTime: 0.472  ,  TotalTime: 6.892  ,  Loss/total: 0.80793  ,  Loss/giou: 0.19754  ,  Loss/l1: 0.02067  ,  Loss/location: 0.30951  ,  IoU: 0.82481
Epoch Time: 0:08:57.510379
Avg Data Time: 6.58656
Avg GPU Trans Time: 0.04893
Avg Forward Time: 0.25568
Epoch Time: 0:08:57.555797
Avg Data Time: 6.37160
Avg GPU Trans Time: 0.04845
Avg Forward Time: 0.47169
[val: 10, 78 / 78] FPS: 9.3 (196.9)  ,  DataTime: 6.593 (0.049)  ,  ForwardTime: 0.258  ,  TotalTime: 6.900  ,  Loss/total: 0.81660  ,  Loss/giou: 0.19668  ,  Loss/l1: 0.02087  ,  Loss/location: 0.31889  ,  IoU: 0.82512
[val: 10, 78 / 78] FPS: 9.3 (189.5)  ,  DataTime: 6.383 (0.049)  ,  ForwardTime: 0.470  ,  TotalTime: 6.902  ,  Loss/total: 0.80996  ,  Loss/giou: 0.19708  ,  Loss/l1: 0.02055  ,  Loss/location: 0.31304  ,  IoU: 0.82444
Epoch Time: 0:08:58.212933
Avg Data Time: 6.59336
Avg GPU Trans Time: 0.04884
Avg Forward Time: 0.25796
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
Epoch Time: 0:08:58.322530
Avg Data Time: 6.38259
Avg GPU Trans Time: 0.04891
Avg Forward Time: 0.47007
[val: 10, 78 / 78] FPS: 9.3 (186.8)  ,  DataTime: 6.609 (0.050)  ,  ForwardTime: 0.252  ,  TotalTime: 6.910  ,  Loss/total: 0.77361  ,  Loss/giou: 0.18825  ,  Loss/l1: 0.01939  ,  Loss/location: 0.30016  ,  IoU: 0.83083
Epoch Time: 0:08:59.007263
Avg Data Time: 6.60866
Avg GPU Trans Time: 0.04952
Avg Forward Time: 0.25218
[val: 10, 78 / 78] FPS: 9.3 (198.5)  ,  DataTime: 6.574 (0.049)  ,  ForwardTime: 0.295  ,  TotalTime: 6.918  ,  Loss/total: 0.79745  ,  Loss/giou: 0.19329  ,  Loss/l1: 0.01988  ,  Loss/location: 0.31150  ,  IoU: 0.82724
Epoch Time: 0:08:59.565471
Avg Data Time: 6.57395
Avg GPU Trans Time: 0.04877
Avg Forward Time: 0.29479
[train: 11, 50 / 117] FPS: 31.6 (91.4)  ,  DataTime: 0.261 (0.055)  ,  ForwardTime: 1.712  ,  TotalTime: 2.027  ,  Loss/total: 0.67041  ,  Loss/giou: 0.16376  ,  Loss/l1: 0.01439  ,  Loss/location: 0.27093  ,  IoU: 0.84696[train: 11, 50 / 117] FPS: 30.8 (91.4)  ,  DataTime: 0.174 (0.051)  ,  ForwardTime: 1.853  ,  TotalTime: 2.077  ,  Loss/total: 0.69217  ,  Loss/giou: 0.16955  ,  Loss/l1: 0.01563  ,  Loss/location: 0.27490  ,  IoU: 0.84350[train: 11, 50 / 117] FPS: 31.3 (91.4)  ,  DataTime: 0.215 (0.057)  ,  ForwardTime: 1.770  ,  TotalTime: 2.042  ,  Loss/total: 0.73508  ,  Loss/giou: 0.17660  ,  Loss/l1: 0.01694  ,  Loss/location: 0.29719  ,  IoU: 0.83868


[train: 11, 50 / 117] FPS: 31.1 (91.4)  ,  DataTime: 0.259 (0.058)  ,  ForwardTime: 1.742  ,  TotalTime: 2.060  ,  Loss/total: 0.68740  ,  Loss/giou: 0.16669  ,  Loss/l1: 0.01496  ,  Loss/location: 0.27923  ,  IoU: 0.84472
[train: 11, 50 / 117] FPS: 31.8 (91.4)  ,  DataTime: 0.253 (0.057)  ,  ForwardTime: 1.703  ,  TotalTime: 2.014  ,  Loss/total: 0.69698  ,  Loss/giou: 0.17141  ,  Loss/l1: 0.01519  ,  Loss/location: 0.27821  ,  IoU: 0.84008
[train: 11, 50 / 117] FPS: 31.3 (91.3)  ,  DataTime: 0.324 (0.056)  ,  ForwardTime: 1.664  ,  TotalTime: 2.044  ,  Loss/total: 0.70457  ,  Loss/giou: 0.17124  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28466  ,  IoU: 0.84134
[train: 11, 50 / 117] FPS: 31.8 (91.6)  ,  DataTime: 1.138 (0.056)  ,  ForwardTime: 0.819  ,  TotalTime: 2.013  ,  Loss/total: 0.66949  ,  Loss/giou: 0.16240  ,  Loss/l1: 0.01415  ,  Loss/location: 0.27391  ,  IoU: 0.84753
[train: 11, 50 / 117] FPS: 32.0 (91.6)  ,  DataTime: 0.763 (0.052)  ,  ForwardTime: 1.187  ,  TotalTime: 2.003  ,  Loss/total: 0.68279  ,  Loss/giou: 0.16849  ,  Loss/l1: 0.01486  ,  Loss/location: 0.27153  ,  IoU: 0.84229
[train: 11, 100 / 117] FPS: 30.1 (96.1)  ,  DataTime: 0.152 (0.058)  ,  ForwardTime: 1.917  ,  TotalTime: 2.128  ,  Loss/total: 0.69458  ,  Loss/giou: 0.16913  ,  Loss/l1: 0.01529  ,  Loss/location: 0.27986  ,  IoU: 0.84279
[train: 11, 100 / 117] FPS: 30.5 (95.9)  ,  DataTime: 0.472 (0.054)  ,  ForwardTime: 1.573  ,  TotalTime: 2.099  ,  Loss/total: 0.69330  ,  Loss/giou: 0.16940  ,  Loss/l1: 0.01521  ,  Loss/location: 0.27847  ,  IoU: 0.84213
[train: 11, 100 / 117] FPS: 30.2 (95.9)  ,  DataTime: 0.238 (0.055)  ,  ForwardTime: 1.826  ,  TotalTime: 2.119  ,  Loss/total: 0.71097  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01593  ,  Loss/location: 0.28802  ,  IoU: 0.84182
[train: 11, 100 / 117] FPS: 30.3 (95.9)  ,  DataTime: 0.152 (0.054)  ,  ForwardTime: 1.905  ,  TotalTime: 2.111  ,  Loss/total: 0.69375  ,  Loss/giou: 0.16870  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28054  ,  IoU: 0.84298
[train: 11, 100 / 117] FPS: 30.4 (95.8)  ,  DataTime: 0.238 (0.057)  ,  ForwardTime: 1.810  ,  TotalTime: 2.105  ,  Loss/total: 0.69895  ,  Loss/giou: 0.17095  ,  Loss/l1: 0.01533  ,  Loss/location: 0.28042  ,  IoU: 0.84108
[train: 11, 100 / 117] FPS: 30.4 (96.3)  ,  DataTime: 1.064 (0.054)  ,  ForwardTime: 0.987  ,  TotalTime: 2.104  ,  Loss/total: 0.68731  ,  Loss/giou: 0.16731  ,  Loss/l1: 0.01497  ,  Loss/location: 0.27781  ,  IoU: 0.84434
[train: 11, 100 / 117] FPS: 30.0 (95.6)  ,  DataTime: 0.131 (0.048)  ,  ForwardTime: 1.957  ,  TotalTime: 2.137  ,  Loss/total: 0.69349  ,  Loss/giou: 0.17036  ,  Loss/l1: 0.01567  ,  Loss/location: 0.27442  ,  IoU: 0.84245
[train: 11, 100 / 117] FPS: 30.2 (96.4)  ,  DataTime: 0.197 (0.056)  ,  ForwardTime: 1.867  ,  TotalTime: 2.120  ,  Loss/total: 0.69813  ,  Loss/giou: 0.16898  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28392  ,  IoU: 0.84314
[train: 11, 117 / 117] FPS: 33.2 (105.5)  ,  DataTime: 0.955 (0.053)  ,  ForwardTime: 0.923  ,  TotalTime: 1.930  ,  Loss/total: 0.68631  ,  Loss/giou: 0.16703  ,  Loss/l1: 0.01496  ,  Loss/location: 0.27743  ,  IoU: 0.84458
[train: 11, 117 / 117] FPS: 32.8 (105.5)  ,  DataTime: 0.136 (0.057)  ,  ForwardTime: 1.757  ,  TotalTime: 1.950  ,  Loss/total: 0.69536  ,  Loss/giou: 0.16916  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28100  ,  IoU: 0.84263
[train: 11, 117 / 117] FPS: 32.9 (105.5)  ,  DataTime: 0.174 (0.055)  ,  ForwardTime: 1.715  ,  TotalTime: 1.943  ,  Loss/total: 0.69044  ,  Loss/giou: 0.16751  ,  Loss/l1: 0.01499  ,  Loss/location: 0.28049  ,  IoU: 0.84426
[train: 11, 117 / 117] FPS: 32.9 (105.5)  ,  DataTime: 0.209 (0.054)  ,  ForwardTime: 1.680  ,  TotalTime: 1.943  ,  Loss/total: 0.71050  ,  Loss/giou: 0.17190  ,  Loss/l1: 0.01592  ,  Loss/location: 0.28710  ,  IoU: 0.84144
[train: 11, 117 / 117] FPS: 32.7 (105.4)  ,  DataTime: 0.118 (0.047)  ,  ForwardTime: 1.793  ,  TotalTime: 1.958  ,  Loss/total: 0.69234  ,  Loss/giou: 0.17037  ,  Loss/l1: 0.01567  ,  Loss/location: 0.27326  ,  IoU: 0.84256
[train: 11, 117 / 117] FPS: 33.2 (105.5)  ,  DataTime: 0.209 (0.055)  ,  ForwardTime: 1.666  ,  TotalTime: 1.931  ,  Loss/total: 0.69973  ,  Loss/giou: 0.17167  ,  Loss/l1: 0.01549  ,  Loss/location: 0.27896  ,  IoU: 0.84077
[train: 11, 117 / 117] FPS: 33.2 (105.5)  ,  DataTime: 0.409 (0.054)  ,  ForwardTime: 1.463  ,  TotalTime: 1.926  ,  Loss/total: 0.68745  ,  Loss/giou: 0.16765  ,  Loss/l1: 0.01491  ,  Loss/location: 0.27758  ,  IoU: 0.84340
[train: 11, 117 / 117] FPS: 33.1 (105.6)  ,  DataTime: 0.135 (0.053)  ,  ForwardTime: 1.747  ,  TotalTime: 1.936  ,  Loss/total: 0.69818  ,  Loss/giou: 0.16970  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28234  ,  IoU: 0.84230
Epoch Time: 0:03:46.544529
Avg Data Time: 0.13531
Avg GPU Trans Time: 0.05348
Avg Forward Time: 1.74748
Epoch Time: 0:03:45.861149
Avg Data Time: 0.95481
Avg GPU Trans Time: 0.05272
Avg Forward Time: 0.92291
Epoch Time: 0:03:47.301644
Avg Data Time: 0.20917
Avg GPU Trans Time: 0.05405
Avg Forward Time: 1.67953
Epoch Time: 0:03:45.868998
Avg Data Time: 0.20929
Avg GPU Trans Time: 0.05549
Avg Forward Time: 1.66572
Epoch Time: 0:03:47.377071
Avg Data Time: 0.17370
Avg GPU Trans Time: 0.05459
Avg Forward Time: 1.71511
Epoch Time: 0:03:49.064112
Avg Data Time: 0.11767
Avg GPU Trans Time: 0.04720
Avg Forward Time: 1.79294
Epoch Time: 0:03:45.326060
Avg Data Time: 0.40900
Avg GPU Trans Time: 0.05367
Avg Forward Time: 1.46320
Epoch Time: 0:03:48.181219
Avg Data Time: 0.13620
Avg GPU Trans Time: 0.05668
Avg Forward Time: 1.75738
[train: 12, 50 / 117] FPS: 28.9 (94.1)  ,  DataTime: 0.464 (0.052)  ,  ForwardTime: 1.697  ,  TotalTime: 2.212  ,  Loss/total: 0.71501  ,  Loss/giou: 0.17521  ,  Loss/l1: 0.01652  ,  Loss/location: 0.28200  ,  IoU: 0.83977
[train: 12, 50 / 117] FPS: 28.9 (94.1)  ,  DataTime: 0.977 (0.053)  ,  ForwardTime: 1.182  ,  TotalTime: 2.212  ,  Loss/total: 0.70211  ,  Loss/giou: 0.17121  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28131  ,  IoU: 0.84142
[train: 12, 50 / 117] FPS: 28.9 (94.0)  ,  DataTime: 0.640 (0.056)  ,  ForwardTime: 1.516  ,  TotalTime: 2.212  ,  Loss/total: 0.66802  ,  Loss/giou: 0.16355  ,  Loss/l1: 0.01445  ,  Loss/location: 0.26870  ,  IoU: 0.84730
[train: 12, 50 / 117] FPS: 28.9 (94.0)  ,  DataTime: 0.472 (0.050)  ,  ForwardTime: 1.690  ,  TotalTime: 2.212  ,  Loss/total: 0.69813  ,  Loss/giou: 0.17088  ,  Loss/l1: 0.01573  ,  Loss/location: 0.27771  ,  IoU: 0.84217[train: 12, 50 / 117] FPS: 28.9 (94.2)  ,  DataTime: 0.322 (0.054)  ,  ForwardTime: 1.836  ,  TotalTime: 2.212  ,  Loss/total: 0.71200  ,  Loss/giou: 0.17261  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28886  ,  IoU: 0.83929

[train: 12, 50 / 117] FPS: 28.9 (94.0)  ,  DataTime: 0.475 (0.052)  ,  ForwardTime: 1.685  ,  TotalTime: 2.212  ,  Loss/total: 0.70745  ,  Loss/giou: 0.17000  ,  Loss/l1: 0.01541  ,  Loss/location: 0.29038  ,  IoU: 0.84170
[train: 12, 50 / 117] FPS: 28.9 (93.7)  ,  DataTime: 0.455 (0.051)  ,  ForwardTime: 1.706  ,  TotalTime: 2.212  ,  Loss/total: 0.73027  ,  Loss/giou: 0.17719  ,  Loss/l1: 0.01623  ,  Loss/location: 0.29475  ,  IoU: 0.83559
[train: 12, 50 / 117] FPS: 28.9 (93.8)  ,  DataTime: 1.184 (0.053)  ,  ForwardTime: 0.974  ,  TotalTime: 2.212  ,  Loss/total: 0.69385  ,  Loss/giou: 0.16973  ,  Loss/l1: 0.01528  ,  Loss/location: 0.27800  ,  IoU: 0.84233
[train: 12, 100 / 117] FPS: 34.6 (96.0)  ,  DataTime: 0.282 (0.054)  ,  ForwardTime: 1.516  ,  TotalTime: 1.851  ,  Loss/total: 0.70885  ,  Loss/giou: 0.17160  ,  Loss/l1: 0.01579  ,  Loss/location: 0.28671  ,  IoU: 0.84160
[train: 12, 100 / 117] FPS: 34.6 (95.9)  ,  DataTime: 0.696 (0.055)  ,  ForwardTime: 1.099  ,  TotalTime: 1.851  ,  Loss/total: 0.69407  ,  Loss/giou: 0.16909  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28011  ,  IoU: 0.84281
[train: 12, 100 / 117] FPS: 34.6 (96.0)  ,  DataTime: 0.449 (0.056)  ,  ForwardTime: 1.346  ,  TotalTime: 1.851  ,  Loss/total: 0.67928  ,  Loss/giou: 0.16487  ,  Loss/l1: 0.01477  ,  Loss/location: 0.27569  ,  IoU: 0.84641
[train: 12, 100 / 117] FPS: 34.6 (95.8)  ,  DataTime: 0.260 (0.052)  ,  ForwardTime: 1.539  ,  TotalTime: 1.851  ,  Loss/total: 0.70663  ,  Loss/giou: 0.17052  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28754  ,  IoU: 0.84161
[train: 12, 100 / 117] FPS: 34.6 (95.8)  ,  DataTime: 0.328 (0.052)  ,  ForwardTime: 1.472  ,  TotalTime: 1.851  ,  Loss/total: 0.70869  ,  Loss/giou: 0.17248  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28487  ,  IoU: 0.84047[train: 12, 100 / 117] FPS: 34.6 (95.9)  ,  DataTime: 0.246 (0.053)  ,  ForwardTime: 1.552  ,  TotalTime: 1.851  ,  Loss/total: 0.72533  ,  Loss/giou: 0.17665  ,  Loss/l1: 0.01625  ,  Loss/location: 0.29079  ,  IoU: 0.83654

[train: 12, 100 / 117] FPS: 34.6 (95.5)  ,  DataTime: 0.691 (0.054)  ,  ForwardTime: 1.107  ,  TotalTime: 1.851  ,  Loss/total: 0.70329  ,  Loss/giou: 0.17070  ,  Loss/l1: 0.01550  ,  Loss/location: 0.28441  ,  IoU: 0.84163
[train: 12, 100 / 117] FPS: 34.6 (95.3)  ,  DataTime: 0.181 (0.056)  ,  ForwardTime: 1.614  ,  TotalTime: 1.851  ,  Loss/total: 0.71439  ,  Loss/giou: 0.17323  ,  Loss/l1: 0.01573  ,  Loss/location: 0.28930  ,  IoU: 0.83934
[train: 12, 117 / 117] FPS: 37.5 (102.8)  ,  DataTime: 0.215 (0.052)  ,  ForwardTime: 1.440  ,  TotalTime: 1.708  ,  Loss/total: 0.72352  ,  Loss/giou: 0.17599  ,  Loss/l1: 0.01616  ,  Loss/location: 0.29074  ,  IoU: 0.83704[train: 12, 117 / 117] FPS: 37.5 (102.9)  ,  DataTime: 0.285 (0.051)  ,  ForwardTime: 1.371  ,  TotalTime: 1.708  ,  Loss/total: 0.70510  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28389  ,  IoU: 0.84106

[train: 12, 117 / 117] FPS: 37.5 (102.9)  ,  DataTime: 0.279 (0.053)  ,  ForwardTime: 1.376  ,  TotalTime: 1.708  ,  Loss/total: 0.70610  ,  Loss/giou: 0.17119  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28548  ,  IoU: 0.84177
[train: 12, 117 / 117] FPS: 37.5 (103.0)  ,  DataTime: 0.161 (0.054)  ,  ForwardTime: 1.493  ,  TotalTime: 1.708  ,  Loss/total: 0.71614  ,  Loss/giou: 0.17393  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28936  ,  IoU: 0.83873
[train: 12, 117 / 117] FPS: 37.5 (102.9)  ,  DataTime: 0.228 (0.051)  ,  ForwardTime: 1.428  ,  TotalTime: 1.708  ,  Loss/total: 0.70404  ,  Loss/giou: 0.16980  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28739  ,  IoU: 0.84219
[train: 12, 117 / 117] FPS: 37.5 (102.9)  ,  DataTime: 0.600 (0.054)  ,  ForwardTime: 1.053  ,  TotalTime: 1.707  ,  Loss/total: 0.69512  ,  Loss/giou: 0.16892  ,  Loss/l1: 0.01511  ,  Loss/location: 0.28170  ,  IoU: 0.84274
[train: 12, 117 / 117] FPS: 37.5 (102.8)  ,  DataTime: 0.390 (0.055)  ,  ForwardTime: 1.264  ,  TotalTime: 1.708  ,  Loss/total: 0.68934  ,  Loss/giou: 0.16740  ,  Loss/l1: 0.01516  ,  Loss/location: 0.27874  ,  IoU: 0.84443
[train: 12, 117 / 117] FPS: 37.5 (102.8)  ,  DataTime: 0.596 (0.053)  ,  ForwardTime: 1.059  ,  TotalTime: 1.707  ,  Loss/total: 0.69792  ,  Loss/giou: 0.16965  ,  Loss/l1: 0.01534  ,  Loss/location: 0.28191  ,  IoU: 0.84250
Epoch Time: 0:03:19.781420
Avg Data Time: 0.21532
Avg GPU Trans Time: 0.05184
Avg Forward Time: 1.44038
Epoch Time: 0:03:19.806044
Avg Data Time: 0.22828
Avg GPU Trans Time: 0.05130
Avg Forward Time: 1.42817
Epoch Time: 0:03:19.772398
Avg Data Time: 0.60046
Avg GPU Trans Time: 0.05423
Avg Forward Time: 1.05276
Epoch Time: 0:03:19.796102
Avg Data Time: 0.38951
Avg GPU Trans Time: 0.05460
Avg Forward Time: 1.26355
Epoch Time: 0:03:19.776945
Avg Data Time: 0.59635
Avg GPU Trans Time: 0.05265
Avg Forward Time: 1.05850
Epoch Time: 0:03:19.807253
Avg Data Time: 0.27947
Avg GPU Trans Time: 0.05273
Avg Forward Time: 1.37555
Epoch Time: 0:03:19.794332
Avg Data Time: 0.16065
Avg GPU Trans Time: 0.05437
Avg Forward Time: 1.49262
Epoch Time: 0:03:19.799054
Avg Data Time: 0.28520
Avg GPU Trans Time: 0.05117
Avg Forward Time: 1.37131
[train: 13, 50 / 117] FPS: 36.3 (88.9)  ,  DataTime: 0.236 (0.059)  ,  ForwardTime: 1.470  ,  TotalTime: 1.765  ,  Loss/total: 0.68798  ,  Loss/giou: 0.16621  ,  Loss/l1: 0.01487  ,  Loss/location: 0.28122  ,  IoU: 0.84533[train: 13, 50 / 117] FPS: 36.3 (88.5)  ,  DataTime: 0.582 (0.059)  ,  ForwardTime: 1.124  ,  TotalTime: 1.764  ,  Loss/total: 0.69548  ,  Loss/giou: 0.17040  ,  Loss/l1: 0.01549  ,  Loss/location: 0.27723  ,  IoU: 0.84228

[train: 13, 50 / 117] FPS: 36.3 (88.6)  ,  DataTime: 0.748 (0.057)  ,  ForwardTime: 0.960  ,  TotalTime: 1.765  ,  Loss/total: 0.68989  ,  Loss/giou: 0.16932  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27461  ,  IoU: 0.84232
[train: 13, 50 / 117] FPS: 36.3 (88.5)  ,  DataTime: 0.254 (0.059)  ,  ForwardTime: 1.451  ,  TotalTime: 1.765  ,  Loss/total: 0.71499  ,  Loss/giou: 0.17335  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28976  ,  IoU: 0.83929
[train: 13, 50 / 117] FPS: 36.3 (88.9)  ,  DataTime: 0.572 (0.060)  ,  ForwardTime: 1.133  ,  TotalTime: 1.765  ,  Loss/total: 0.67788  ,  Loss/giou: 0.16402  ,  Loss/l1: 0.01421  ,  Loss/location: 0.27877  ,  IoU: 0.84642
[train: 13, 50 / 117] FPS: 36.3 (88.2)  ,  DataTime: 0.292 (0.058)  ,  ForwardTime: 1.414  ,  TotalTime: 1.765  ,  Loss/total: 0.70281  ,  Loss/giou: 0.17145  ,  Loss/l1: 0.01554  ,  Loss/location: 0.28219  ,  IoU: 0.84096
[train: 13, 50 / 117] FPS: 36.3 (88.4)  ,  DataTime: 0.290 (0.059)  ,  ForwardTime: 1.416  ,  TotalTime: 1.765  ,  Loss/total: 0.69576  ,  Loss/giou: 0.16692  ,  Loss/l1: 0.01447  ,  Loss/location: 0.28959  ,  IoU: 0.84310
[train: 13, 50 / 117] FPS: 36.3 (88.3)  ,  DataTime: 0.341 (0.061)  ,  ForwardTime: 1.363  ,  TotalTime: 1.765  ,  Loss/total: 0.68037  ,  Loss/giou: 0.16747  ,  Loss/l1: 0.01472  ,  Loss/location: 0.27181  ,  IoU: 0.84358
[train: 13, 100 / 117] FPS: 43.8 (86.1)  ,  DataTime: 0.196 (0.059)  ,  ForwardTime: 1.207  ,  TotalTime: 1.462  ,  Loss/total: 0.69767  ,  Loss/giou: 0.16996  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28099  ,  IoU: 0.84219[train: 13, 100 / 117] FPS: 43.8 (86.1)  ,  DataTime: 0.405 (0.059)  ,  ForwardTime: 0.999  ,  TotalTime: 1.463  ,  Loss/total: 0.69745  ,  Loss/giou: 0.17091  ,  Loss/l1: 0.01574  ,  Loss/location: 0.27691  ,  IoU: 0.84162[train: 13, 100 / 117] FPS: 43.8 (86.3)  ,  DataTime: 0.356 (0.061)  ,  ForwardTime: 1.046  ,  TotalTime: 1.462  ,  Loss/total: 0.68213  ,  Loss/giou: 0.16692  ,  Loss/l1: 0.01477  ,  Loss/location: 0.27443  ,  IoU: 0.84425


[train: 13, 100 / 117] FPS: 43.8 (86.5)  ,  DataTime: 0.149 (0.061)  ,  ForwardTime: 1.252  ,  TotalTime: 1.462  ,  Loss/total: 0.70478  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28571  ,  IoU: 0.84094
[train: 13, 100 / 117] FPS: 43.8 (86.3)  ,  DataTime: 0.202 (0.063)  ,  ForwardTime: 1.197  ,  TotalTime: 1.462  ,  Loss/total: 0.68509  ,  Loss/giou: 0.16853  ,  Loss/l1: 0.01492  ,  Loss/location: 0.27344  ,  IoU: 0.84271
[train: 13, 100 / 117] FPS: 43.8 (86.4)  ,  DataTime: 0.331 (0.061)  ,  ForwardTime: 1.070  ,  TotalTime: 1.463  ,  Loss/total: 0.68455  ,  Loss/giou: 0.16680  ,  Loss/l1: 0.01478  ,  Loss/location: 0.27706  ,  IoU: 0.84452
[train: 13, 100 / 117] FPS: 43.8 (86.2)  ,  DataTime: 0.139 (0.061)  ,  ForwardTime: 1.263  ,  TotalTime: 1.462  ,  Loss/total: 0.69055  ,  Loss/giou: 0.16768  ,  Loss/l1: 0.01493  ,  Loss/location: 0.28053  ,  IoU: 0.84372
[train: 13, 100 / 117] FPS: 43.8 (85.9)  ,  DataTime: 0.338 (0.060)  ,  ForwardTime: 1.065  ,  TotalTime: 1.462  ,  Loss/total: 0.68233  ,  Loss/giou: 0.16537  ,  Loss/l1: 0.01433  ,  Loss/location: 0.27994  ,  IoU: 0.84468
[train: 13, 117 / 117] FPS: 46.9 (103.7)  ,  DataTime: 0.309 (0.059)  ,  ForwardTime: 0.997  ,  TotalTime: 1.365  ,  Loss/total: 0.68761  ,  Loss/giou: 0.16800  ,  Loss/l1: 0.01500  ,  Loss/location: 0.27663  ,  IoU: 0.84362
[train: 13, 117 / 117] FPS: 46.9 (103.5)  ,  DataTime: 0.133 (0.060)  ,  ForwardTime: 1.172  ,  TotalTime: 1.365  ,  Loss/total: 0.70607  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01557  ,  Loss/location: 0.28491  ,  IoU: 0.84070
[train: 13, 117 / 117] FPS: 46.9 (103.6)  ,  DataTime: 0.289 (0.059)  ,  ForwardTime: 1.017  ,  TotalTime: 1.365  ,  Loss/total: 0.69053  ,  Loss/giou: 0.16840  ,  Loss/l1: 0.01502  ,  Loss/location: 0.27864  ,  IoU: 0.84340
[train: 13, 117 / 117] FPS: 46.9 (103.6)  ,  DataTime: 0.124 (0.059)  ,  ForwardTime: 1.181  ,  TotalTime: 1.365  ,  Loss/total: 0.69598  ,  Loss/giou: 0.16813  ,  Loss/l1: 0.01501  ,  Loss/location: 0.28469  ,  IoU: 0.84352
[train: 13, 117 / 117] FPS: 46.9 (103.6)  ,  DataTime: 0.172 (0.058)  ,  ForwardTime: 1.134  ,  TotalTime: 1.365  ,  Loss/total: 0.69930  ,  Loss/giou: 0.17033  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28164  ,  IoU: 0.84195
[train: 13, 117 / 117] FPS: 46.9 (103.4)  ,  DataTime: 0.317 (0.058)  ,  ForwardTime: 0.990  ,  TotalTime: 1.365  ,  Loss/total: 0.69197  ,  Loss/giou: 0.16762  ,  Loss/l1: 0.01484  ,  Loss/location: 0.28254  ,  IoU: 0.84336
[train: 13, 117 / 117] FPS: 46.9 (103.4)  ,  DataTime: 0.178 (0.062)  ,  ForwardTime: 1.125  ,  TotalTime: 1.365  ,  Loss/total: 0.68662  ,  Loss/giou: 0.16846  ,  Loss/l1: 0.01490  ,  Loss/location: 0.27518  ,  IoU: 0.84279
[train: 13, 117 / 117] FPS: 46.9 (103.4)  ,  DataTime: 0.351 (0.058)  ,  ForwardTime: 0.956  ,  TotalTime: 1.365  ,  Loss/total: 0.69355  ,  Loss/giou: 0.16968  ,  Loss/l1: 0.01560  ,  Loss/location: 0.27618  ,  IoU: 0.84260
Epoch Time: 0:02:39.678565
Avg Data Time: 0.13277
Avg GPU Trans Time: 0.06041
Avg Forward Time: 1.17160
Epoch Time: 0:02:39.717536
Avg Data Time: 0.35140
Avg GPU Trans Time: 0.05808
Avg Forward Time: 0.95563
Epoch Time: 0:02:39.697017
Avg Data Time: 0.28898
Avg GPU Trans Time: 0.05931
Avg Forward Time: 1.01664
Epoch Time: 0:02:39.673119
Avg Data Time: 0.30903
Avg GPU Trans Time: 0.05895
Avg Forward Time: 0.99675
Epoch Time: 0:02:39.675817
Avg Data Time: 0.17246
Avg GPU Trans Time: 0.05835
Avg Forward Time: 1.13393
Epoch Time: 0:02:39.675699
Avg Data Time: 0.12400
Avg GPU Trans Time: 0.05933
Avg Forward Time: 1.18142
Epoch Time: 0:02:39.677963
Avg Data Time: 0.17774
Avg GPU Trans Time: 0.06180
Avg Forward Time: 1.12524
Epoch Time: 0:02:39.680206
Avg Data Time: 0.31673
Avg GPU Trans Time: 0.05804
Avg Forward Time: 0.99002
[train: 14, 50 / 117] FPS: 47.3 (85.6)  ,  DataTime: 0.275 (0.059)  ,  ForwardTime: 1.020  ,  TotalTime: 1.354  ,  Loss/total: 0.71154  ,  Loss/giou: 0.17255  ,  Loss/l1: 0.01562  ,  Loss/location: 0.28835  ,  IoU: 0.83952[train: 14, 50 / 117] FPS: 47.2 (85.4)  ,  DataTime: 0.377 (0.062)  ,  ForwardTime: 0.915  ,  TotalTime: 1.355  ,  Loss/total: 0.68915  ,  Loss/giou: 0.16907  ,  Loss/l1: 0.01472  ,  Loss/location: 0.27740  ,  IoU: 0.84204
[train: 14, 50 / 117] FPS: 47.2 (85.6)  ,  DataTime: 0.356 (0.063)  ,  ForwardTime: 0.937  ,  TotalTime: 1.356  ,  Loss/total: 0.74488  ,  Loss/giou: 0.17927  ,  Loss/l1: 0.01669  ,  Loss/location: 0.30290  ,  IoU: 0.83435

[train: 14, 50 / 117] FPS: 47.3 (85.7)  ,  DataTime: 0.288 (0.064)  ,  ForwardTime: 1.002  ,  TotalTime: 1.354  ,  Loss/total: 0.70542  ,  Loss/giou: 0.17117  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28572  ,  IoU: 0.84119
[train: 14, 50 / 117] FPS: 47.2 (85.6)  ,  DataTime: 0.255 (0.063)  ,  ForwardTime: 1.036  ,  TotalTime: 1.355  ,  Loss/total: 0.66255  ,  Loss/giou: 0.16117  ,  Loss/l1: 0.01390  ,  Loss/location: 0.27073  ,  IoU: 0.84888
[train: 14, 50 / 117] FPS: 47.2 (85.7)  ,  DataTime: 0.186 (0.062)  ,  ForwardTime: 1.107  ,  TotalTime: 1.355  ,  Loss/total: 0.72265  ,  Loss/giou: 0.17509  ,  Loss/l1: 0.01625  ,  Loss/location: 0.29122  ,  IoU: 0.83821
[train: 14, 50 / 117] FPS: 47.2 (85.2)  ,  DataTime: 0.461 (0.064)  ,  ForwardTime: 0.830  ,  TotalTime: 1.355  ,  Loss/total: 0.69713  ,  Loss/giou: 0.16924  ,  Loss/l1: 0.01508  ,  Loss/location: 0.28323  ,  IoU: 0.84296
[train: 14, 50 / 117] FPS: 47.2 (85.3)  ,  DataTime: 0.231 (0.065)  ,  ForwardTime: 1.059  ,  TotalTime: 1.355  ,  Loss/total: 0.70557  ,  Loss/giou: 0.16991  ,  Loss/l1: 0.01543  ,  Loss/location: 0.28857  ,  IoU: 0.84180
[train: 14, 100 / 117] FPS: 54.2 (88.9)  ,  DataTime: 0.254 (0.068)  ,  ForwardTime: 0.858  ,  TotalTime: 1.180  ,  Loss/total: 0.70129  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28306  ,  IoU: 0.84211[train: 14, 100 / 117] FPS: 54.2 (89.3)  ,  DataTime: 0.289 (0.064)  ,  ForwardTime: 0.827  ,  TotalTime: 1.180  ,  Loss/total: 0.68285  ,  Loss/giou: 0.16628  ,  Loss/l1: 0.01471  ,  Loss/location: 0.27673  ,  IoU: 0.84505

[train: 14, 100 / 117] FPS: 54.2 (88.8)  ,  DataTime: 0.161 (0.064)  ,  ForwardTime: 0.955  ,  TotalTime: 1.180  ,  Loss/total: 0.71847  ,  Loss/giou: 0.17556  ,  Loss/l1: 0.01615  ,  Loss/location: 0.28659  ,  IoU: 0.83767
[train: 14, 100 / 117] FPS: 54.2 (88.8)  ,  DataTime: 0.200 (0.066)  ,  ForwardTime: 0.915  ,  TotalTime: 1.181  ,  Loss/total: 0.73889  ,  Loss/giou: 0.17776  ,  Loss/l1: 0.01658  ,  Loss/location: 0.30046  ,  IoU: 0.83589
[train: 14, 100 / 117] FPS: 54.2 (88.9)  ,  DataTime: 0.116 (0.068)  ,  ForwardTime: 0.997  ,  TotalTime: 1.180  ,  Loss/total: 0.72995  ,  Loss/giou: 0.17585  ,  Loss/l1: 0.01625  ,  Loss/location: 0.29698  ,  IoU: 0.83762[train: 14, 100 / 117] FPS: 54.2 (89.0)  ,  DataTime: 0.211 (0.064)  ,  ForwardTime: 0.905  ,  TotalTime: 1.180  ,  Loss/total: 0.71464  ,  Loss/giou: 0.17399  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28837  ,  IoU: 0.83828

[train: 14, 100 / 117] FPS: 54.2 (88.6)  ,  DataTime: 0.138 (0.068)  ,  ForwardTime: 0.974  ,  TotalTime: 1.180  ,  Loss/total: 0.69122  ,  Loss/giou: 0.16779  ,  Loss/l1: 0.01496  ,  Loss/location: 0.28082  ,  IoU: 0.84358
[train: 14, 100 / 117] FPS: 54.2 (88.8)  ,  DataTime: 0.168 (0.068)  ,  ForwardTime: 0.944  ,  TotalTime: 1.180  ,  Loss/total: 0.70838  ,  Loss/giou: 0.17142  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28864  ,  IoU: 0.84097
[train: 14, 117 / 117] FPS: 57.9 (108.2)  ,  DataTime: 0.252 (0.062)  ,  ForwardTime: 0.792  ,  TotalTime: 1.106  ,  Loss/total: 0.68477  ,  Loss/giou: 0.16691  ,  Loss/l1: 0.01489  ,  Loss/location: 0.27652  ,  IoU: 0.84467
[train: 14, 117 / 117] FPS: 57.8 (108.2)  ,  DataTime: 0.176 (0.064)  ,  ForwardTime: 0.866  ,  TotalTime: 1.107  ,  Loss/total: 0.73253  ,  Loss/giou: 0.17612  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29934  ,  IoU: 0.83698
[train: 14, 117 / 117] FPS: 57.9 (108.2)  ,  DataTime: 0.124 (0.066)  ,  ForwardTime: 0.916  ,  TotalTime: 1.106  ,  Loss/total: 0.68995  ,  Loss/giou: 0.16785  ,  Loss/l1: 0.01491  ,  Loss/location: 0.27970  ,  IoU: 0.84335[train: 14, 117 / 117] FPS: 57.9 (108.2)  ,  DataTime: 0.186 (0.063)  ,  ForwardTime: 0.858  ,  TotalTime: 1.106  ,  Loss/total: 0.70983  ,  Loss/giou: 0.17291  ,  Loss/l1: 0.01550  ,  Loss/location: 0.28652  ,  IoU: 0.83918[train: 14, 117 / 117] FPS: 57.9 (108.1)  ,  DataTime: 0.104 (0.065)  ,  ForwardTime: 0.937  ,  TotalTime: 1.106  ,  Loss/total: 0.72714  ,  Loss/giou: 0.17550  ,  Loss/l1: 0.01618  ,  Loss/location: 0.29523  ,  IoU: 0.83769
[train: 14, 117 / 117] FPS: 57.9 (108.2)  ,  DataTime: 0.143 (0.062)  ,  ForwardTime: 0.901  ,  TotalTime: 1.106  ,  Loss/total: 0.71111  ,  Loss/giou: 0.17329  ,  Loss/l1: 0.01584  ,  Loss/location: 0.28534  ,  IoU: 0.83952


[train: 14, 117 / 117] FPS: 57.9 (108.5)  ,  DataTime: 0.149 (0.066)  ,  ForwardTime: 0.891  ,  TotalTime: 1.106  ,  Loss/total: 0.70560  ,  Loss/giou: 0.17152  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28529  ,  IoU: 0.84099
[train: 14, 117 / 117] FPS: 57.9 (108.2)  ,  DataTime: 0.228 (0.065)  ,  ForwardTime: 0.813  ,  TotalTime: 1.106  ,  Loss/total: 0.69600  ,  Loss/giou: 0.16983  ,  Loss/l1: 0.01545  ,  Loss/location: 0.27907  ,  IoU: 0.84290
Epoch Time: 0:02:09.419473
Avg Data Time: 0.25221
Avg GPU Trans Time: 0.06204
Avg Forward Time: 0.79189
Epoch Time: 0:02:09.478634
Avg Data Time: 0.17639
Avg GPU Trans Time: 0.06395
Avg Forward Time: 0.86632
Epoch Time: 0:02:09.421893
Avg Data Time: 0.12372
Avg GPU Trans Time: 0.06598
Avg Forward Time: 0.91647
Epoch Time: 0:02:09.391866
Avg Data Time: 0.14295
Avg GPU Trans Time: 0.06192
Avg Forward Time: 0.90104
Epoch Time: 0:02:09.428357
Avg Data Time: 0.10444
Avg GPU Trans Time: 0.06520
Avg Forward Time: 0.93658
Epoch Time: 0:02:09.379950Epoch Time: 0:02:09.429631

Avg Data Time: 0.14889Avg Data Time: 0.22788

Avg GPU Trans Time: 0.06520Avg GPU Trans Time: 0.06611

Avg Forward Time: 0.81315Avg Forward Time: 0.89080

Epoch Time: 0:02:09.408393
Avg Data Time: 0.18584
Avg GPU Trans Time: 0.06260
Avg Forward Time: 0.85762
[train: 15, 50 / 117] FPS: 46.8 (88.7)  ,  DataTime: 0.223 (0.060)  ,  ForwardTime: 1.084  ,  TotalTime: 1.368  ,  Loss/total: 0.70240  ,  Loss/giou: 0.16907  ,  Loss/l1: 0.01476  ,  Loss/location: 0.29044  ,  IoU: 0.84227
[train: 15, 50 / 117] FPS: 46.8 (88.6)  ,  DataTime: 0.283 (0.072)  ,  ForwardTime: 1.014  ,  TotalTime: 1.368  ,  Loss/total: 0.69590  ,  Loss/giou: 0.16929  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28254  ,  IoU: 0.84230[train: 15, 50 / 117] FPS: 46.8 (88.8)  ,  DataTime: 0.177 (0.067)  ,  ForwardTime: 1.124  ,  TotalTime: 1.368  ,  Loss/total: 0.68930  ,  Loss/giou: 0.16744  ,  Loss/l1: 0.01473  ,  Loss/location: 0.28075  ,  IoU: 0.84321

[train: 15, 50 / 117] FPS: 46.8 (88.7)  ,  DataTime: 0.560 (0.063)  ,  ForwardTime: 0.745  ,  TotalTime: 1.368  ,  Loss/total: 0.67378  ,  Loss/giou: 0.16179  ,  Loss/l1: 0.01389  ,  Loss/location: 0.28074  ,  IoU: 0.84803
[train: 15, 50 / 117] FPS: 46.8 (88.7)  ,  DataTime: 0.317 (0.066)  ,  ForwardTime: 0.985  ,  TotalTime: 1.368  ,  Loss/total: 0.69384  ,  Loss/giou: 0.16766  ,  Loss/l1: 0.01447  ,  Loss/location: 0.28618  ,  IoU: 0.84265
[train: 15, 50 / 117] FPS: 46.8 (88.2)  ,  DataTime: 0.323 (0.066)  ,  ForwardTime: 0.978  ,  TotalTime: 1.368  ,  Loss/total: 0.68810  ,  Loss/giou: 0.16568  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28151  ,  IoU: 0.84582[train: 15, 50 / 117] FPS: 46.8 (88.2)  ,  DataTime: 0.248 (0.067)  ,  ForwardTime: 1.052  ,  TotalTime: 1.368  ,  Loss/total: 0.69857  ,  Loss/giou: 0.16962  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28312  ,  IoU: 0.84226

[train: 15, 50 / 117] FPS: 46.8 (88.5)  ,  DataTime: 0.151 (0.064)  ,  ForwardTime: 1.152  ,  TotalTime: 1.368  ,  Loss/total: 0.74053  ,  Loss/giou: 0.17927  ,  Loss/l1: 0.01712  ,  Loss/location: 0.29639  ,  IoU: 0.83574
[train: 15, 100 / 117] FPS: 56.6 (86.0)  ,  DataTime: 0.135 (0.063)  ,  ForwardTime: 0.933  ,  TotalTime: 1.130  ,  Loss/total: 0.70379  ,  Loss/giou: 0.17031  ,  Loss/l1: 0.01508  ,  Loss/location: 0.28777  ,  IoU: 0.84169
[train: 15, 100 / 117] FPS: 56.6 (85.6)  ,  DataTime: 0.382 (0.067)  ,  ForwardTime: 0.681  ,  TotalTime: 1.131  ,  Loss/total: 0.68230  ,  Loss/giou: 0.16435  ,  Loss/l1: 0.01435  ,  Loss/location: 0.28187  ,  IoU: 0.84592
[train: 15, 100 / 117] FPS: 56.6 (85.7)  ,  DataTime: 0.183 (0.067)  ,  ForwardTime: 0.880  ,  TotalTime: 1.130  ,  Loss/total: 0.68722  ,  Loss/giou: 0.16644  ,  Loss/l1: 0.01445  ,  Loss/location: 0.28210  ,  IoU: 0.84404
[train: 15, 100 / 117] FPS: 56.6 (85.5)  ,  DataTime: 0.168 (0.074)  ,  ForwardTime: 0.889  ,  TotalTime: 1.131  ,  Loss/total: 0.69487  ,  Loss/giou: 0.16956  ,  Loss/l1: 0.01496  ,  Loss/location: 0.28093  ,  IoU: 0.84181
[train: 15, 100 / 117] FPS: 56.6 (85.5)  ,  DataTime: 0.149 (0.068)  ,  ForwardTime: 0.913  ,  TotalTime: 1.130  ,  Loss/total: 0.70668  ,  Loss/giou: 0.17154  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28615  ,  IoU: 0.84078
[train: 15, 100 / 117] FPS: 56.6 (85.7)  ,  DataTime: 0.187 (0.070)  ,  ForwardTime: 0.873  ,  TotalTime: 1.130  ,  Loss/total: 0.70180  ,  Loss/giou: 0.16990  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28381  ,  IoU: 0.84273
[train: 15, 100 / 117] FPS: 56.6 (85.6)  ,  DataTime: 0.101 (0.066)  ,  ForwardTime: 0.963  ,  TotalTime: 1.130  ,  Loss/total: 0.72849  ,  Loss/giou: 0.17676  ,  Loss/l1: 0.01639  ,  Loss/location: 0.29300  ,  IoU: 0.83706
[train: 15, 100 / 117] FPS: 56.6 (85.5)  ,  DataTime: 0.114 (0.071)  ,  ForwardTime: 0.945  ,  TotalTime: 1.130  ,  Loss/total: 0.70973  ,  Loss/giou: 0.17288  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28609  ,  IoU: 0.83948
[train: 15, 117 / 117] FPS: 60.4 (110.8)  ,  DataTime: 0.103 (0.068)  ,  ForwardTime: 0.889  ,  TotalTime: 1.060  ,  Loss/total: 0.71040  ,  Loss/giou: 0.17303  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28590  ,  IoU: 0.83937
[train: 15, 117 / 117] FPS: 60.4 (110.7)  ,  DataTime: 0.162 (0.065)  ,  ForwardTime: 0.833  ,  TotalTime: 1.060  ,  Loss/total: 0.68585  ,  Loss/giou: 0.16652  ,  Loss/l1: 0.01443  ,  Loss/location: 0.28067  ,  IoU: 0.84392
[train: 15, 117 / 117] FPS: 60.4 (110.7)  ,  DataTime: 0.149 (0.071)  ,  ForwardTime: 0.841  ,  TotalTime: 1.060  ,  Loss/total: 0.69516  ,  Loss/giou: 0.16984  ,  Loss/l1: 0.01497  ,  Loss/location: 0.28064  ,  IoU: 0.84166
[train: 15, 117 / 117] FPS: 60.4 (110.8)  ,  DataTime: 0.094 (0.064)  ,  ForwardTime: 0.903  ,  TotalTime: 1.060  ,  Loss/total: 0.71956  ,  Loss/giou: 0.17481  ,  Loss/l1: 0.01613  ,  Loss/location: 0.28929  ,  IoU: 0.83853
[train: 15, 117 / 117] FPS: 60.4 (111.0)  ,  DataTime: 0.133 (0.066)  ,  ForwardTime: 0.861  ,  TotalTime: 1.060  ,  Loss/total: 0.70585  ,  Loss/giou: 0.17109  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28679  ,  IoU: 0.84090
[train: 15, 117 / 117] FPS: 60.4 (110.6)  ,  DataTime: 0.121 (0.061)  ,  ForwardTime: 0.879  ,  TotalTime: 1.060  ,  Loss/total: 0.70093  ,  Loss/giou: 0.17002  ,  Loss/l1: 0.01504  ,  Loss/location: 0.28567  ,  IoU: 0.84183
[train: 15, 117 / 117] FPS: 60.4 (110.8)  ,  DataTime: 0.166 (0.068)  ,  ForwardTime: 0.827  ,  TotalTime: 1.060  ,  Loss/total: 0.69945  ,  Loss/giou: 0.16915  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28393  ,  IoU: 0.84326
[train: 15, 117 / 117] FPS: 60.4 (110.7)  ,  DataTime: 0.333 (0.065)  ,  ForwardTime: 0.662  ,  TotalTime: 1.060  ,  Loss/total: 0.68476  ,  Loss/giou: 0.16527  ,  Loss/l1: 0.01449  ,  Loss/location: 0.28174  ,  IoU: 0.84533
Epoch Time: 0:02:04.040713
Avg Data Time: 0.12068
Avg GPU Trans Time: 0.06071
Avg Forward Time: 0.87879
Epoch Time: 0:02:04.038833
Avg Data Time: 0.16553
Avg GPU Trans Time: 0.06756
Avg Forward Time: 0.82707
Epoch Time: 0:02:04.060586
Avg Data Time: 0.33300
Avg GPU Trans Time: 0.06502
Avg Forward Time: 0.66232
Epoch Time: 0:02:04.034323
Avg Data Time: 0.16185
Avg GPU Trans Time: 0.06524
Avg Forward Time: 0.83304
Epoch Time: 0:02:04.040104
Avg Data Time: 0.13344
Avg GPU Trans Time: 0.06601
Avg Forward Time: 0.86072
Epoch Time: 0:02:04.033228
Avg Data Time: 0.09356
Avg GPU Trans Time: 0.06391
Avg Forward Time: 0.90264
Epoch Time: 0:02:04.057252
Avg Data Time: 0.14876
Avg GPU Trans Time: 0.07106
Avg Forward Time: 0.84050
Epoch Time: 0:02:04.034309
Avg Data Time: 0.10295
Avg GPU Trans Time: 0.06832
Avg Forward Time: 0.88885
[val: 15, 50 / 78] FPS: 8.8 (200.9)  ,  DataTime: 6.913 (0.046)  ,  ForwardTime: 0.307  ,  TotalTime: 7.266  ,  Loss/total: 0.88720  ,  Loss/giou: 0.21415  ,  Loss/l1: 0.02338  ,  Loss/location: 0.34198  ,  IoU: 0.81141
[val: 15, 50 / 78] FPS: 8.5 (187.1)  ,  DataTime: 7.095 (0.048)  ,  ForwardTime: 0.386  ,  TotalTime: 7.529  ,  Loss/total: 0.82699  ,  Loss/giou: 0.20107  ,  Loss/l1: 0.02080  ,  Loss/location: 0.32086  ,  IoU: 0.82006
[val: 15, 50 / 78] FPS: 8.5 (198.5)  ,  DataTime: 7.272 (0.050)  ,  ForwardTime: 0.239  ,  TotalTime: 7.561  ,  Loss/total: 0.81962  ,  Loss/giou: 0.19587  ,  Loss/l1: 0.02044  ,  Loss/location: 0.32569  ,  IoU: 0.82452
[val: 15, 50 / 78] FPS: 8.4 (243.0)  ,  DataTime: 7.304 (0.048)  ,  ForwardTime: 0.235  ,  TotalTime: 7.587  ,  Loss/total: 0.86418  ,  Loss/giou: 0.20842  ,  Loss/l1: 0.02286  ,  Loss/location: 0.33303  ,  IoU: 0.81743
[val: 15, 50 / 78] FPS: 8.4 (237.2)  ,  DataTime: 7.211 (0.046)  ,  ForwardTime: 0.350  ,  TotalTime: 7.606  ,  Loss/total: 0.80227  ,  Loss/giou: 0.19549  ,  Loss/l1: 0.01980  ,  Loss/location: 0.31227  ,  IoU: 0.82426
[val: 15, 50 / 78] FPS: 8.3 (205.2)  ,  DataTime: 7.278 (0.045)  ,  ForwardTime: 0.351  ,  TotalTime: 7.674  ,  Loss/total: 0.83735  ,  Loss/giou: 0.20509  ,  Loss/l1: 0.02214  ,  Loss/location: 0.31648  ,  IoU: 0.81892
[val: 15, 50 / 78] FPS: 8.3 (7.0)  ,  DataTime: 7.333 (0.043)  ,  ForwardTime: 0.358  ,  TotalTime: 7.734  ,  Loss/total: 0.80168  ,  Loss/giou: 0.19412  ,  Loss/l1: 0.01943  ,  Loss/location: 0.31629  ,  IoU: 0.82458
[val: 15, 50 / 78] FPS: 8.3 (197.9)  ,  DataTime: 7.257 (0.050)  ,  ForwardTime: 0.432  ,  TotalTime: 7.738  ,  Loss/total: 0.82822  ,  Loss/giou: 0.20047  ,  Loss/l1: 0.02192  ,  Loss/location: 0.31767  ,  IoU: 0.82336
[val: 15, 78 / 78] FPS: 9.8 (28.1)  ,  DataTime: 6.173 (0.047)  ,  ForwardTime: 0.281  ,  TotalTime: 6.501  ,  Loss/total: 0.87258  ,  Loss/giou: 0.21093  ,  Loss/l1: 0.02311  ,  Loss/location: 0.33516  ,  IoU: 0.81392
Epoch Time: 0:08:27.052279
Avg Data Time: 6.17283
Avg GPU Trans Time: 0.04700
Avg Forward Time: 0.28084
[val: 15, 78 / 78] FPS: 9.8 (200.7)  ,  DataTime: 6.253 (0.049)  ,  ForwardTime: 0.236  ,  TotalTime: 6.539  ,  Loss/total: 0.84822  ,  Loss/giou: 0.20540  ,  Loss/l1: 0.02196  ,  Loss/location: 0.32761  ,  IoU: 0.81885
Epoch Time: 0:08:30.030281
Avg Data Time: 6.25326
Avg GPU Trans Time: 0.04927
Avg Forward Time: 0.23632
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 15, 78 / 78] FPS: 9.8 (196.2)  ,  DataTime: 6.256 (0.050)  ,  ForwardTime: 0.239  ,  TotalTime: 6.545  ,  Loss/total: 0.83172  ,  Loss/giou: 0.20000  ,  Loss/l1: 0.02114  ,  Loss/location: 0.32601  ,  IoU: 0.82168
Epoch Time: 0:08:30.499183
Avg Data Time: 6.25575
Avg GPU Trans Time: 0.04981
Avg Forward Time: 0.23930
[val: 15, 78 / 78] FPS: 9.8 (187.2)  ,  DataTime: 6.195 (0.048)  ,  ForwardTime: 0.309  ,  TotalTime: 6.553  ,  Loss/total: 0.81695  ,  Loss/giou: 0.19748  ,  Loss/l1: 0.02036  ,  Loss/location: 0.32021  ,  IoU: 0.82327
[val: 15, 78 / 78] FPS: 9.8 (186.4)  ,  DataTime: 6.172 (0.048)  ,  ForwardTime: 0.333  ,  TotalTime: 6.554  ,  Loss/total: 0.80869  ,  Loss/giou: 0.19702  ,  Loss/l1: 0.02028  ,  Loss/location: 0.31323  ,  IoU: 0.82326
Epoch Time: 0:08:31.126873
Avg Data Time: 6.19544
Avg GPU Trans Time: 0.04820
Avg Forward Time: 0.30927
Epoch Time: 0:08:31.188632
Avg Data Time: 6.17225
Avg GPU Trans Time: 0.04848
Avg Forward Time: 0.33296
[val: 15, 78 / 78] FPS: 9.8 (176.0)  ,  DataTime: 6.148 (0.051)  ,  ForwardTime: 0.362  ,  TotalTime: 6.562  ,  Loss/total: 0.82138  ,  Loss/giou: 0.19902  ,  Loss/l1: 0.02119  ,  Loss/location: 0.31741  ,  IoU: 0.82359
Epoch Time: 0:08:31.800768
Avg Data Time: 6.14825
Avg GPU Trans Time: 0.05127
Avg Forward Time: 0.36203
[val: 15, 78 / 78] FPS: 9.7 (184.6)  ,  DataTime: 6.214 (0.046)  ,  ForwardTime: 0.309  ,  TotalTime: 6.568  ,  Loss/total: 0.85621  ,  Loss/giou: 0.20826  ,  Loss/l1: 0.02272  ,  Loss/location: 0.32609  ,  IoU: 0.81674
Epoch Time: 0:08:32.341118
Avg Data Time: 6.21368
Avg GPU Trans Time: 0.04571
Avg Forward Time: 0.30908
[val: 15, 78 / 78] FPS: 9.7 (178.0)  ,  DataTime: 6.214 (0.045)  ,  ForwardTime: 0.315  ,  TotalTime: 6.574  ,  Loss/total: 0.80043  ,  Loss/giou: 0.19513  ,  Loss/l1: 0.02021  ,  Loss/location: 0.30913  ,  IoU: 0.82470
Epoch Time: 0:08:32.782774
Avg Data Time: 6.21364
Avg GPU Trans Time: 0.04522
Avg Forward Time: 0.31527
[train: 16, 50 / 117] FPS: 47.8 (103.6)  ,  DataTime: 0.227 (0.077)  ,  ForwardTime: 1.034  ,  TotalTime: 1.338  ,  Loss/total: 0.71170  ,  Loss/giou: 0.17270  ,  Loss/l1: 0.01539  ,  Loss/location: 0.28935  ,  IoU: 0.83956[train: 16, 50 / 117] FPS: 47.3 (103.5)  ,  DataTime: 0.229 (0.070)  ,  ForwardTime: 1.053  ,  TotalTime: 1.352  ,  Loss/total: 0.73040  ,  Loss/giou: 0.17818  ,  Loss/l1: 0.01640  ,  Loss/location: 0.29204  ,  IoU: 0.83564[train: 16, 50 / 117] FPS: 44.6 (103.8)  ,  DataTime: 0.126 (0.069)  ,  ForwardTime: 1.239  ,  TotalTime: 1.434  ,  Loss/total: 0.70527  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28411  ,  IoU: 0.83923

[train: 16, 50 / 117] FPS: 46.9 (103.5)  ,  DataTime: 0.447 (0.060)  ,  ForwardTime: 0.858  ,  TotalTime: 1.365  ,  Loss/total: 0.71267  ,  Loss/giou: 0.17282  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28889  ,  IoU: 0.83975

[train: 16, 50 / 117] FPS: 48.2 (103.5)  ,  DataTime: 0.239 (0.072)  ,  ForwardTime: 1.016  ,  TotalTime: 1.328  ,  Loss/total: 0.68733  ,  Loss/giou: 0.16663  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28050  ,  IoU: 0.84404
[train: 16, 50 / 117] FPS: 48.6 (103.6)  ,  DataTime: 0.461 (0.065)  ,  ForwardTime: 0.791  ,  TotalTime: 1.318  ,  Loss/total: 0.71304  ,  Loss/giou: 0.17383  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28704  ,  IoU: 0.83950
[train: 16, 50 / 117] FPS: 47.3 (103.8)  ,  DataTime: 0.206 (0.072)  ,  ForwardTime: 1.075  ,  TotalTime: 1.352  ,  Loss/total: 0.71118  ,  Loss/giou: 0.17255  ,  Loss/l1: 0.01581  ,  Loss/location: 0.28703  ,  IoU: 0.83995
[train: 16, 50 / 117] FPS: 47.2 (103.3)  ,  DataTime: 0.250 (0.073)  ,  ForwardTime: 1.032  ,  TotalTime: 1.355  ,  Loss/total: 0.71731  ,  Loss/giou: 0.17507  ,  Loss/l1: 0.01597  ,  Loss/location: 0.28734  ,  IoU: 0.83769
[train: 16, 100 / 117] FPS: 45.4 (96.3)  ,  DataTime: 0.225 (0.063)  ,  ForwardTime: 1.123  ,  TotalTime: 1.411  ,  Loss/total: 0.72184  ,  Loss/giou: 0.17606  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28959  ,  IoU: 0.83672
[train: 16, 100 / 117] FPS: 44.1 (96.1)  ,  DataTime: 0.086 (0.062)  ,  ForwardTime: 1.304  ,  TotalTime: 1.452  ,  Loss/total: 0.71062  ,  Loss/giou: 0.17322  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28530  ,  IoU: 0.83871
[train: 16, 100 / 117] FPS: 45.8 (96.3)  ,  DataTime: 0.323 (0.064)  ,  ForwardTime: 1.012  ,  TotalTime: 1.399  ,  Loss/total: 0.69464  ,  Loss/giou: 0.16865  ,  Loss/l1: 0.01482  ,  Loss/location: 0.28325  ,  IoU: 0.84212
[train: 16, 100 / 117] FPS: 45.2 (96.0)  ,  DataTime: 0.261 (0.056)  ,  ForwardTime: 1.101  ,  TotalTime: 1.417  ,  Loss/total: 0.69882  ,  Loss/giou: 0.16907  ,  Loss/l1: 0.01486  ,  Loss/location: 0.28639  ,  IoU: 0.84199[train: 16, 100 / 117] FPS: 45.4 (96.2)  ,  DataTime: 0.125 (0.063)  ,  ForwardTime: 1.222  ,  TotalTime: 1.411  ,  Loss/total: 0.72512  ,  Loss/giou: 0.17586  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29245  ,  IoU: 0.83742

[train: 16, 100 / 117] FPS: 45.6 (96.0)  ,  DataTime: 0.174 (0.067)  ,  ForwardTime: 1.162  ,  TotalTime: 1.404  ,  Loss/total: 0.70588  ,  Loss/giou: 0.17283  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28220  ,  IoU: 0.83958
[train: 16, 100 / 117] FPS: 45.3 (96.1)  ,  DataTime: 0.240 (0.065)  ,  ForwardTime: 1.108  ,  TotalTime: 1.412  ,  Loss/total: 0.72212  ,  Loss/giou: 0.17505  ,  Loss/l1: 0.01602  ,  Loss/location: 0.29191  ,  IoU: 0.83787
[train: 16, 100 / 117] FPS: 45.9 (95.7)  ,  DataTime: 0.254 (0.060)  ,  ForwardTime: 1.080  ,  TotalTime: 1.394  ,  Loss/total: 0.70154  ,  Loss/giou: 0.17144  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28153  ,  IoU: 0.84121
[train: 16, 117 / 117] FPS: 49.0 (104.6)  ,  DataTime: 0.228 (0.056)  ,  ForwardTime: 1.023  ,  TotalTime: 1.306  ,  Loss/total: 0.70377  ,  Loss/giou: 0.17010  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28795  ,  IoU: 0.84141
[train: 16, 117 / 117] FPS: 49.2 (104.6)  ,  DataTime: 0.211 (0.064)  ,  ForwardTime: 1.028  ,  TotalTime: 1.302  ,  Loss/total: 0.71870  ,  Loss/giou: 0.17383  ,  Loss/l1: 0.01578  ,  Loss/location: 0.29212  ,  IoU: 0.83870
[train: 16, 117 / 117] FPS: 49.2 (104.6)  ,  DataTime: 0.113 (0.062)  ,  ForwardTime: 1.127  ,  TotalTime: 1.301  ,  Loss/total: 0.72757  ,  Loss/giou: 0.17608  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29449  ,  IoU: 0.83719
[train: 16, 117 / 117] FPS: 49.2 (104.7)  ,  DataTime: 0.198 (0.062)  ,  ForwardTime: 1.041  ,  TotalTime: 1.301  ,  Loss/total: 0.72466  ,  Loss/giou: 0.17592  ,  Loss/l1: 0.01603  ,  Loss/location: 0.29264  ,  IoU: 0.83692
[train: 16, 117 / 117] FPS: 47.9 (104.7)  ,  DataTime: 0.079 (0.061)  ,  ForwardTime: 1.196  ,  TotalTime: 1.336  ,  Loss/total: 0.70386  ,  Loss/giou: 0.17182  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28205  ,  IoU: 0.84012
[train: 16, 117 / 117] FPS: 49.8 (104.6)  ,  DataTime: 0.224 (0.059)  ,  ForwardTime: 1.003  ,  TotalTime: 1.286  ,  Loss/total: 0.70320  ,  Loss/giou: 0.17234  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28010  ,  IoU: 0.84077
[train: 16, 117 / 117] FPS: 49.4 (104.5)  ,  DataTime: 0.155 (0.065)  ,  ForwardTime: 1.075  ,  TotalTime: 1.295  ,  Loss/total: 0.70357  ,  Loss/giou: 0.17255  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28049  ,  IoU: 0.83999
[train: 16, 117 / 117] FPS: 49.6 (104.3)  ,  DataTime: 0.285 (0.061)  ,  ForwardTime: 0.944  ,  TotalTime: 1.290  ,  Loss/total: 0.69964  ,  Loss/giou: 0.17030  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28339  ,  IoU: 0.84125
Epoch Time: 0:02:32.851853
Avg Data Time: 0.22781
Avg GPU Trans Time: 0.05575
Avg Forward Time: 1.02286
Epoch Time: 0:02:32.203927
Avg Data Time: 0.11254
Avg GPU Trans Time: 0.06185
Avg Forward Time: 1.12651
Epoch Time: 0:02:32.339305
Avg Data Time: 0.21063
Avg GPU Trans Time: 0.06375
Avg Forward Time: 1.02767
Epoch Time: 0:02:30.477097
Avg Data Time: 0.22356
Avg GPU Trans Time: 0.05934
Epoch Time: 0:02:32.181666Avg Forward Time: 1.00323

Avg Data Time: 0.19785
Avg GPU Trans Time: 0.06211
Avg Forward Time: 1.04074
Epoch Time: 0:02:36.302990
Avg Data Time: 0.07910
Avg GPU Trans Time: 0.06069
Avg Forward Time: 1.19613
Epoch Time: 0:02:31.475677
Avg Data Time: 0.15466
Avg GPU Trans Time: 0.06538
Avg Forward Time: 1.07462
Epoch Time: 0:02:30.980337
Avg Data Time: 0.28473
Avg GPU Trans Time: 0.06130
Avg Forward Time: 0.94440
[train: 17, 50 / 117] FPS: 37.8 (86.1)  ,  DataTime: 0.471 (0.057)  ,  ForwardTime: 1.163  ,  TotalTime: 1.691  ,  Loss/total: 0.71805  ,  Loss/giou: 0.17486  ,  Loss/l1: 0.01601  ,  Loss/location: 0.28827  ,  IoU: 0.83792
[train: 17, 50 / 117] FPS: 37.8 (86.1)  ,  DataTime: 0.467 (0.052)  ,  ForwardTime: 1.173  ,  TotalTime: 1.692  ,  Loss/total: 0.72069  ,  Loss/giou: 0.17286  ,  Loss/l1: 0.01590  ,  Loss/location: 0.29548  ,  IoU: 0.83975
[train: 17, 50 / 117] FPS: 37.9 (86.1)  ,  DataTime: 0.978 (0.054)  ,  ForwardTime: 0.659  ,  TotalTime: 1.690  ,  Loss/total: 0.71909  ,  Loss/giou: 0.17230  ,  Loss/l1: 0.01549  ,  Loss/location: 0.29703  ,  IoU: 0.83992
[train: 17, 50 / 117] FPS: 37.9 (86.2)  ,  DataTime: 0.394 (0.058)  ,  ForwardTime: 1.238  ,  TotalTime: 1.691  ,  Loss/total: 0.69777  ,  Loss/giou: 0.16848  ,  Loss/l1: 0.01462  ,  Loss/location: 0.28769  ,  IoU: 0.84187
[train: 17, 50 / 117] FPS: 37.8 (86.2)  ,  DataTime: 0.321 (0.054)  ,  ForwardTime: 1.316  ,  TotalTime: 1.691  ,  Loss/total: 0.71464  ,  Loss/giou: 0.17244  ,  Loss/l1: 0.01530  ,  Loss/location: 0.29327  ,  IoU: 0.83826
[train: 17, 50 / 117] FPS: 37.8 (85.9)  ,  DataTime: 0.275 (0.059)  ,  ForwardTime: 1.357  ,  TotalTime: 1.691  ,  Loss/total: 0.71738  ,  Loss/giou: 0.17497  ,  Loss/l1: 0.01600  ,  Loss/location: 0.28746  ,  IoU: 0.83767[train: 17, 50 / 117] FPS: 37.8 (85.9)  ,  DataTime: 0.371 (0.055)  ,  ForwardTime: 1.266  ,  TotalTime: 1.692  ,  Loss/total: 0.71582  ,  Loss/giou: 0.17439  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28875  ,  IoU: 0.83759

[train: 17, 50 / 117] FPS: 37.8 (86.2)  ,  DataTime: 0.540 (0.055)  ,  ForwardTime: 1.096  ,  TotalTime: 1.691  ,  Loss/total: 0.70387  ,  Loss/giou: 0.17191  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28242  ,  IoU: 0.84058
[train: 17, 100 / 117] FPS: 44.4 (100.2)  ,  DataTime: 0.160 (0.059)  ,  ForwardTime: 1.224  ,  TotalTime: 1.443  ,  Loss/total: 0.68847  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01491  ,  Loss/location: 0.27672  ,  IoU: 0.84256[train: 17, 100 / 117] FPS: 44.3 (100.4)  ,  DataTime: 0.273 (0.056)  ,  ForwardTime: 1.113  ,  TotalTime: 1.443  ,  Loss/total: 0.70094  ,  Loss/giou: 0.17122  ,  Loss/l1: 0.01526  ,  Loss/location: 0.28218  ,  IoU: 0.84040

[train: 17, 100 / 117] FPS: 44.4 (100.1)  ,  DataTime: 0.258 (0.053)  ,  ForwardTime: 1.132  ,  TotalTime: 1.443  ,  Loss/total: 0.72417  ,  Loss/giou: 0.17460  ,  Loss/l1: 0.01594  ,  Loss/location: 0.29527  ,  IoU: 0.83803
[train: 17, 100 / 117] FPS: 44.4 (100.1)  ,  DataTime: 0.702 (0.053)  ,  ForwardTime: 0.687  ,  TotalTime: 1.442  ,  Loss/total: 0.70672  ,  Loss/giou: 0.17040  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28982  ,  IoU: 0.84123
[train: 17, 100 / 117] FPS: 44.4 (100.5)  ,  DataTime: 0.183 (0.054)  ,  ForwardTime: 1.206  ,  TotalTime: 1.443  ,  Loss/total: 0.71084  ,  Loss/giou: 0.17340  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28667  ,  IoU: 0.83824
[train: 17, 100 / 117] FPS: 44.4 (100.6)  ,  DataTime: 0.221 (0.059)  ,  ForwardTime: 1.163  ,  TotalTime: 1.442  ,  Loss/total: 0.71193  ,  Loss/giou: 0.17228  ,  Loss/l1: 0.01535  ,  Loss/location: 0.29060  ,  IoU: 0.83921
[train: 17, 100 / 117] FPS: 44.4 (100.1)  ,  DataTime: 0.257 (0.056)  ,  ForwardTime: 1.130  ,  TotalTime: 1.443  ,  Loss/total: 0.71223  ,  Loss/giou: 0.17268  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28846  ,  IoU: 0.83958
[train: 17, 100 / 117] FPS: 44.4 (100.1)  ,  DataTime: 0.290 (0.055)  ,  ForwardTime: 1.098  ,  TotalTime: 1.443  ,  Loss/total: 0.71445  ,  Loss/giou: 0.17425  ,  Loss/l1: 0.01608  ,  Loss/location: 0.28554  ,  IoU: 0.83897
[train: 17, 117 / 117] FPS: 48.1 (112.5)  ,  DataTime: 0.225 (0.056)  ,  ForwardTime: 1.049  ,  TotalTime: 1.329  ,  Loss/total: 0.71453  ,  Loss/giou: 0.17313  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28997  ,  IoU: 0.83918
[train: 17, 117 / 117] FPS: 48.2 (112.2)  ,  DataTime: 0.194 (0.057)  ,  ForwardTime: 1.078  ,  TotalTime: 1.329  ,  Loss/total: 0.71456  ,  Loss/giou: 0.17362  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28909  ,  IoU: 0.83849
[train: 17, 117 / 117] FPS: 48.2 (112.3)  ,  DataTime: 0.143 (0.057)  ,  ForwardTime: 1.129  ,  TotalTime: 1.329  ,  Loss/total: 0.69266  ,  Loss/giou: 0.16880  ,  Loss/l1: 0.01490  ,  Loss/location: 0.28055  ,  IoU: 0.84221
[train: 17, 117 / 117] FPS: 48.1 (112.4)  ,  DataTime: 0.231 (0.053)  ,  ForwardTime: 1.045  ,  TotalTime: 1.329  ,  Loss/total: 0.71930  ,  Loss/giou: 0.17346  ,  Loss/l1: 0.01567  ,  Loss/location: 0.29404  ,  IoU: 0.83864
[train: 17, 117 / 117] FPS: 48.1 (112.2)  ,  DataTime: 0.253 (0.054)  ,  ForwardTime: 1.022  ,  TotalTime: 1.329  ,  Loss/total: 0.71277  ,  Loss/giou: 0.17393  ,  Loss/l1: 0.01594  ,  Loss/location: 0.28522  ,  IoU: 0.83889
[train: 17, 117 / 117] FPS: 48.2 (112.2)  ,  DataTime: 0.605 (0.053)  ,  ForwardTime: 0.671  ,  TotalTime: 1.329  ,  Loss/total: 0.70794  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01524  ,  Loss/location: 0.29075  ,  IoU: 0.84116
[train: 17, 117 / 117] FPS: 48.1 (112.2)  ,  DataTime: 0.239 (0.056)  ,  ForwardTime: 1.035  ,  TotalTime: 1.330  ,  Loss/total: 0.70236  ,  Loss/giou: 0.17142  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28313  ,  IoU: 0.84030
[train: 17, 117 / 117] FPS: 48.1 (111.7)  ,  DataTime: 0.162 (0.053)  ,  ForwardTime: 1.114  ,  TotalTime: 1.329  ,  Loss/total: 0.71116  ,  Loss/giou: 0.17350  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28692  ,  IoU: 0.83825
Epoch Time: 0:02:35.524545
Avg Data Time: 0.25279
Avg GPU Trans Time: 0.05428
Avg Forward Time: 1.02219
Epoch Time: 0:02:35.555498
Avg Data Time: 0.23865
Avg GPU Trans Time: 0.05555
Avg Forward Time: 1.03533
Epoch Time: 0:02:35.523149
Avg Data Time: 0.22476
Avg GPU Trans Time: 0.05561
Avg Forward Time: 1.04890
Epoch Time: 0:02:35.531482
Avg Data Time: 0.23148
Avg GPU Trans Time: 0.05261
Avg Forward Time: 1.04524
Epoch Time: 0:02:35.499679
Avg Data Time: 0.14250
Avg GPU Trans Time: 0.05746
Avg Forward Time: 1.12910
Epoch Time: 0:02:35.481914
Avg Data Time: 0.19375
Avg GPU Trans Time: 0.05729
Avg Forward Time: 1.07786
Epoch Time: 0:02:35.526343
Avg Data Time: 0.16168
Avg GPU Trans Time: 0.05334
Avg Forward Time: 1.11426
Epoch Time: 0:02:35.476669
Avg Data Time: 0.60498
Avg GPU Trans Time: 0.05291
Avg Forward Time: 0.67097
[train: 18, 50 / 117] FPS: 47.7 (96.7)  ,  DataTime: 0.212 (0.061)  ,  ForwardTime: 1.069  ,  TotalTime: 1.341  ,  Loss/total: 0.73727  ,  Loss/giou: 0.17995  ,  Loss/l1: 0.01698  ,  Loss/location: 0.29246  ,  IoU: 0.83404
[train: 18, 50 / 117] FPS: 47.7 (96.6)  ,  DataTime: 0.248 (0.061)  ,  ForwardTime: 1.032  ,  TotalTime: 1.341  ,  Loss/total: 0.72502  ,  Loss/giou: 0.17634  ,  Loss/l1: 0.01607  ,  Loss/location: 0.29199  ,  IoU: 0.83733
[train: 18, 50 / 117] FPS: 47.7 (96.5)  ,  DataTime: 0.203 (0.066)  ,  ForwardTime: 1.071  ,  TotalTime: 1.341  ,  Loss/total: 0.70611  ,  Loss/giou: 0.17124  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28690  ,  IoU: 0.84048
[train: 18, 50 / 117] FPS: 47.7 (96.5)  ,  DataTime: 0.398 (0.068)  ,  ForwardTime: 0.876  ,  TotalTime: 1.342  ,  Loss/total: 0.69975  ,  Loss/giou: 0.17233  ,  Loss/l1: 0.01569  ,  Loss/location: 0.27664  ,  IoU: 0.84030
[train: 18, 50 / 117] FPS: 47.7 (96.7)  ,  DataTime: 0.338 (0.057)  ,  ForwardTime: 0.946  ,  TotalTime: 1.341  ,  Loss/total: 0.70567  ,  Loss/giou: 0.17265  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28154  ,  IoU: 0.84005
[train: 18, 50 / 117] FPS: 47.7 (96.6)  ,  DataTime: 0.240 (0.063)  ,  ForwardTime: 1.039  ,  TotalTime: 1.341  ,  Loss/total: 0.72861  ,  Loss/giou: 0.17891  ,  Loss/l1: 0.01642  ,  Loss/location: 0.28868  ,  IoU: 0.83484
[train: 18, 50 / 117] FPS: 47.7 (96.4)  ,  DataTime: 0.518 (0.061)  ,  ForwardTime: 0.762  ,  TotalTime: 1.341  ,  Loss/total: 0.73818  ,  Loss/giou: 0.18007  ,  Loss/l1: 0.01687  ,  Loss/location: 0.29371  ,  IoU: 0.83430
[train: 18, 50 / 117] FPS: 47.6 (96.4)  ,  DataTime: 0.240 (0.068)  ,  ForwardTime: 1.036  ,  TotalTime: 1.343  ,  Loss/total: 0.71034  ,  Loss/giou: 0.17348  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28634  ,  IoU: 0.83863
[train: 18, 100 / 117] FPS: 53.6 (89.5)  ,  DataTime: 0.256 (0.069)  ,  ForwardTime: 0.869  ,  TotalTime: 1.193  ,  Loss/total: 0.70280  ,  Loss/giou: 0.17191  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28119  ,  IoU: 0.84033
[train: 18, 100 / 117] FPS: 53.6 (89.2)  ,  DataTime: 0.149 (0.062)  ,  ForwardTime: 0.982  ,  TotalTime: 1.193  ,  Loss/total: 0.72065  ,  Loss/giou: 0.17614  ,  Loss/l1: 0.01612  ,  Loss/location: 0.28780  ,  IoU: 0.83725
[train: 18, 100 / 117] FPS: 53.6 (89.0)  ,  DataTime: 0.194 (0.061)  ,  ForwardTime: 0.938  ,  TotalTime: 1.193  ,  Loss/total: 0.70992  ,  Loss/giou: 0.17255  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28701  ,  IoU: 0.83974
[train: 18, 100 / 117] FPS: 53.6 (89.1)  ,  DataTime: 0.130 (0.061)  ,  ForwardTime: 1.002  ,  TotalTime: 1.193  ,  Loss/total: 0.72391  ,  Loss/giou: 0.17539  ,  Loss/l1: 0.01604  ,  Loss/location: 0.29296  ,  IoU: 0.83730
[train: 18, 100 / 117] FPS: 53.6 (89.3)  ,  DataTime: 0.401 (0.066)  ,  ForwardTime: 0.726  ,  TotalTime: 1.193  ,  Loss/total: 0.72106  ,  Loss/giou: 0.17582  ,  Loss/l1: 0.01642  ,  Loss/location: 0.28732  ,  IoU: 0.83813
[train: 18, 100 / 117] FPS: 53.6 (88.9)  ,  DataTime: 0.169 (0.064)  ,  ForwardTime: 0.959  ,  TotalTime: 1.193  ,  Loss/total: 0.73098  ,  Loss/giou: 0.17805  ,  Loss/l1: 0.01650  ,  Loss/location: 0.29236  ,  IoU: 0.83552
[train: 18, 100 / 117] FPS: 53.7 (89.0)  ,  DataTime: 0.127 (0.064)  ,  ForwardTime: 1.002  ,  TotalTime: 1.193  ,  Loss/total: 0.70836  ,  Loss/giou: 0.17199  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28691  ,  IoU: 0.83991
[train: 18, 100 / 117] FPS: 53.6 (89.1)  ,  DataTime: 0.146 (0.069)  ,  ForwardTime: 0.979  ,  TotalTime: 1.194  ,  Loss/total: 0.70884  ,  Loss/giou: 0.17271  ,  Loss/l1: 0.01543  ,  Loss/location: 0.28624  ,  IoU: 0.83919
[train: 18, 117 / 117] FPS: 57.7 (107.6)  ,  DataTime: 0.171 (0.059)  ,  ForwardTime: 0.879  ,  TotalTime: 1.109  ,  Loss/total: 0.70772  ,  Loss/giou: 0.17246  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28480  ,  IoU: 0.83999
[train: 18, 117 / 117] FPS: 57.7 (107.7)  ,  DataTime: 0.114 (0.061)  ,  ForwardTime: 0.934  ,  TotalTime: 1.109  ,  Loss/total: 0.70288  ,  Loss/giou: 0.17096  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28406  ,  IoU: 0.84086
[train: 18, 117 / 117] FPS: 57.7 (107.6)  ,  DataTime: 0.130 (0.066)  ,  ForwardTime: 0.914  ,  TotalTime: 1.110  ,  Loss/total: 0.70437  ,  Loss/giou: 0.17149  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28514  ,  IoU: 0.84016[train: 18, 117 / 117] FPS: 57.7 (107.8)  ,  DataTime: 0.117 (0.059)  ,  ForwardTime: 0.933  ,  TotalTime: 1.109  ,  Loss/total: 0.71955  ,  Loss/giou: 0.17402  ,  Loss/l1: 0.01582  ,  Loss/location: 0.29242  ,  IoU: 0.83818

[train: 18, 117 / 117] FPS: 57.7 (107.5)  ,  DataTime: 0.150 (0.062)  ,  ForwardTime: 0.897  ,  TotalTime: 1.109  ,  Loss/total: 0.72345  ,  Loss/giou: 0.17592  ,  Loss/l1: 0.01613  ,  Loss/location: 0.29095  ,  IoU: 0.83696
[train: 18, 117 / 117] FPS: 57.7 (107.6)  ,  DataTime: 0.133 (0.059)  ,  ForwardTime: 0.917  ,  TotalTime: 1.109  ,  Loss/total: 0.71641  ,  Loss/giou: 0.17491  ,  Loss/l1: 0.01580  ,  Loss/location: 0.28758  ,  IoU: 0.83787
[train: 18, 117 / 117] FPS: 57.7 (107.6)  ,  DataTime: 0.348 (0.064)  ,  ForwardTime: 0.697  ,  TotalTime: 1.109  ,  Loss/total: 0.71814  ,  Loss/giou: 0.17484  ,  Loss/l1: 0.01607  ,  Loss/location: 0.28811  ,  IoU: 0.83848
[train: 18, 117 / 117] FPS: 57.7 (107.7)  ,  DataTime: 0.224 (0.066)  ,  ForwardTime: 0.819  ,  TotalTime: 1.109  ,  Loss/total: 0.70621  ,  Loss/giou: 0.17290  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28221  ,  IoU: 0.83946
Epoch Time: 0:02:09.745153
Avg Data Time: 0.34811
Avg GPU Trans Time: 0.06415
Avg Forward Time: 0.69668
Epoch Time: 0:02:09.758980
Avg Data Time: 0.11680
Avg GPU Trans Time: 0.05923
Avg Forward Time: 0.93302
Epoch Time: 0:02:09.740556
Avg Data Time: 0.14951
Avg GPU Trans Time: 0.06208
Avg Forward Time: 0.89731
Epoch Time: 0:02:09.737136
Avg Data Time: 0.13271
Avg GPU Trans Time: 0.05948
Avg Forward Time: 0.91667
Epoch Time: 0:02:09.844462
Avg Data Time: 0.12960
Avg GPU Trans Time: 0.06637
Avg Forward Time: 0.91381
Epoch Time: 0:02:09.784876
Avg Data Time: 0.22431
Avg GPU Trans Time: 0.06624
Avg Forward Time: 0.81871
Epoch Time: 0:02:09.739417
Avg Data Time: 0.17103
Avg GPU Trans Time: 0.05876
Avg Forward Time: 0.87909
Epoch Time: 0:02:09.726740
Avg Data Time: 0.11386
Avg GPU Trans Time: 0.06079
Avg Forward Time: 0.93413
[train: 19, 50 / 117] FPS: 51.5 (85.0)  ,  DataTime: 0.216 (0.061)  ,  ForwardTime: 0.965  ,  TotalTime: 1.243  ,  Loss/total: 0.71565  ,  Loss/giou: 0.17460  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28762  ,  IoU: 0.83785
[train: 19, 50 / 117] FPS: 51.5 (84.9)  ,  DataTime: 0.429 (0.066)  ,  ForwardTime: 0.747  ,  TotalTime: 1.242  ,  Loss/total: 0.70692  ,  Loss/giou: 0.17117  ,  Loss/l1: 0.01502  ,  Loss/location: 0.28949  ,  IoU: 0.83974
[train: 19, 50 / 117] FPS: 51.5 (85.0)  ,  DataTime: 0.319 (0.069)  ,  ForwardTime: 0.854  ,  TotalTime: 1.243  ,  Loss/total: 0.70498  ,  Loss/giou: 0.17181  ,  Loss/l1: 0.01510  ,  Loss/location: 0.28588  ,  IoU: 0.83961
[train: 19, 50 / 117] FPS: 51.5 (85.0)  ,  DataTime: 0.419 (0.063)  ,  ForwardTime: 0.760  ,  TotalTime: 1.242  ,  Loss/total: 0.73795  ,  Loss/giou: 0.17726  ,  Loss/l1: 0.01649  ,  Loss/location: 0.30099  ,  IoU: 0.83639
[train: 19, 50 / 117] FPS: 51.5 (85.1)  ,  DataTime: 0.225 (0.063)  ,  ForwardTime: 0.954  ,  TotalTime: 1.243  ,  Loss/total: 0.69983  ,  Loss/giou: 0.16813  ,  Loss/l1: 0.01485  ,  Loss/location: 0.28934  ,  IoU: 0.84239
[train: 19, 50 / 117] FPS: 51.5 (85.0)  ,  DataTime: 0.232 (0.069)  ,  ForwardTime: 0.942  ,  TotalTime: 1.243  ,  Loss/total: 0.75051  ,  Loss/giou: 0.18221  ,  Loss/l1: 0.01708  ,  Loss/location: 0.30069  ,  IoU: 0.83219
[train: 19, 50 / 117] FPS: 51.5 (84.9)  ,  DataTime: 0.273 (0.068)  ,  ForwardTime: 0.902  ,  TotalTime: 1.243  ,  Loss/total: 0.72396  ,  Loss/giou: 0.17652  ,  Loss/l1: 0.01633  ,  Loss/location: 0.28926  ,  IoU: 0.83697
[train: 19, 50 / 117] FPS: 51.5 (84.8)  ,  DataTime: 0.253 (0.063)  ,  ForwardTime: 0.927  ,  TotalTime: 1.242  ,  Loss/total: 0.73608  ,  Loss/giou: 0.18067  ,  Loss/l1: 0.01696  ,  Loss/location: 0.28991  ,  IoU: 0.83370
[train: 19, 100 / 117] FPS: 61.2 (88.0)  ,  DataTime: 0.171 (0.070)  ,  ForwardTime: 0.804  ,  TotalTime: 1.045  ,  Loss/total: 0.71715  ,  Loss/giou: 0.17405  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28913  ,  IoU: 0.83880
[train: 19, 100 / 117] FPS: 61.2 (88.2)  ,  DataTime: 0.187 (0.072)  ,  ForwardTime: 0.785  ,  TotalTime: 1.045  ,  Loss/total: 0.71009  ,  Loss/giou: 0.17142  ,  Loss/l1: 0.01523  ,  Loss/location: 0.29110  ,  IoU: 0.84017
[train: 19, 100 / 117] FPS: 61.2 (88.0)  ,  DataTime: 0.133 (0.065)  ,  ForwardTime: 0.847  ,  TotalTime: 1.045  ,  Loss/total: 0.71524  ,  Loss/giou: 0.17517  ,  Loss/l1: 0.01585  ,  Loss/location: 0.28567  ,  IoU: 0.83775
[train: 19, 100 / 117] FPS: 61.2 (88.0)  ,  DataTime: 0.234 (0.062)  ,  ForwardTime: 0.749  ,  TotalTime: 1.045  ,  Loss/total: 0.72578  ,  Loss/giou: 0.17584  ,  Loss/l1: 0.01608  ,  Loss/location: 0.29369  ,  IoU: 0.83707
[train: 19, 100 / 117] FPS: 61.2 (87.7)  ,  DataTime: 0.258 (0.069)  ,  ForwardTime: 0.718  ,  TotalTime: 1.045  ,  Loss/total: 0.70338  ,  Loss/giou: 0.17218  ,  Loss/l1: 0.01519  ,  Loss/location: 0.28309  ,  IoU: 0.83924
[train: 19, 100 / 117] FPS: 61.2 (87.9)  ,  DataTime: 0.157 (0.068)  ,  ForwardTime: 0.820  ,  TotalTime: 1.045  ,  Loss/total: 0.72487  ,  Loss/giou: 0.17719  ,  Loss/l1: 0.01625  ,  Loss/location: 0.28925  ,  IoU: 0.83601
[train: 19, 100 / 117] FPS: 61.2 (87.8)  ,  DataTime: 0.145 (0.065)  ,  ForwardTime: 0.835  ,  TotalTime: 1.045  ,  Loss/total: 0.70086  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01494  ,  Loss/location: 0.28892  ,  IoU: 0.84254
[train: 19, 100 / 117] FPS: 61.2 (87.8)  ,  DataTime: 0.163 (0.069)  ,  ForwardTime: 0.813  ,  TotalTime: 1.045  ,  Loss/total: 0.73479  ,  Loss/giou: 0.17832  ,  Loss/l1: 0.01640  ,  Loss/location: 0.29618  ,  IoU: 0.83496
[train: 19, 117 / 117] FPS: 64.9 (108.2)  ,  DataTime: 0.227 (0.066)  ,  ForwardTime: 0.693  ,  TotalTime: 0.986  ,  Loss/total: 0.70724  ,  Loss/giou: 0.17271  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28505  ,  IoU: 0.83905
[train: 19, 117 / 117] FPS: 64.9 (108.1)  ,  DataTime: 0.205 (0.060)  ,  ForwardTime: 0.720  ,  TotalTime: 0.985  ,  Loss/total: 0.73118  ,  Loss/giou: 0.17740  ,  Loss/l1: 0.01634  ,  Loss/location: 0.29466  ,  IoU: 0.83593
[train: 19, 117 / 117] FPS: 64.9 (108.1)  ,  DataTime: 0.129 (0.064)  ,  ForwardTime: 0.793  ,  TotalTime: 0.986  ,  Loss/total: 0.70410  ,  Loss/giou: 0.16928  ,  Loss/l1: 0.01510  ,  Loss/location: 0.29005  ,  IoU: 0.84193
[train: 19, 117 / 117] FPS: 64.9 (108.1)  ,  DataTime: 0.145 (0.067)  ,  ForwardTime: 0.773  ,  TotalTime: 0.986  ,  Loss/total: 0.73760  ,  Loss/giou: 0.17912  ,  Loss/l1: 0.01653  ,  Loss/location: 0.29673  ,  IoU: 0.83425
[train: 19, 117 / 117] FPS: 64.9 (108.2)  ,  DataTime: 0.152 (0.067)  ,  ForwardTime: 0.767  ,  TotalTime: 0.986  ,  Loss/total: 0.71600  ,  Loss/giou: 0.17421  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28740  ,  IoU: 0.83870[train: 19, 117 / 117] FPS: 64.9 (108.1)  ,  DataTime: 0.166 (0.070)  ,  ForwardTime: 0.750  ,  TotalTime: 0.986  ,  Loss/total: 0.71103  ,  Loss/giou: 0.17190  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28995  ,  IoU: 0.84008

[train: 19, 117 / 117] FPS: 64.9 (108.1)  ,  DataTime: 0.121 (0.063)  ,  ForwardTime: 0.802  ,  TotalTime: 0.986  ,  Loss/total: 0.71661  ,  Loss/giou: 0.17471  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28831  ,  IoU: 0.83795
[train: 19, 117 / 117] FPS: 64.9 (108.0)  ,  DataTime: 0.140 (0.066)  ,  ForwardTime: 0.779  ,  TotalTime: 0.986  ,  Loss/total: 0.72205  ,  Loss/giou: 0.17634  ,  Loss/l1: 0.01612  ,  Loss/location: 0.28878  ,  IoU: 0.83669
Epoch Time: 0:01:55.310987
Avg Data Time: 0.22652
Avg GPU Trans Time: 0.06642
Avg Forward Time: 0.69263
Epoch Time: 0:01:55.318750
Avg Data Time: 0.12948
Avg GPU Trans Time: 0.06355
Avg Forward Time: 0.79260
Epoch Time: 0:01:55.324805
Avg Data Time: 0.12062
Avg GPU Trans Time: 0.06293
Avg Forward Time: 0.80214
Epoch Time: 0:01:55.327587
Avg Data Time: 0.14541
Avg GPU Trans Time: 0.06719
Avg Forward Time: 0.77310
Epoch Time: 0:01:55.305308
Avg Data Time: 0.14018
Avg GPU Trans Time: 0.06642
Avg Forward Time: 0.77892
Epoch Time: 0:01:55.314375
Avg Data Time: 0.16556
Avg GPU Trans Time: 0.06961
Avg Forward Time: 0.75043
Epoch Time: 0:01:55.301360
Avg Data Time: 0.20549
Avg GPU Trans Time: 0.05953
Avg Forward Time: 0.72046
Epoch Time: 0:01:55.325148
Avg Data Time: 0.15178
Avg GPU Trans Time: 0.06696
Avg Forward Time: 0.76694
[train: 20, 50 / 117] FPS: 58.8 (86.9)  ,  DataTime: 0.171 (0.075)  ,  ForwardTime: 0.841  ,  TotalTime: 1.088  ,  Loss/total: 0.69195  ,  Loss/giou: 0.16765  ,  Loss/l1: 0.01446  ,  Loss/location: 0.28434  ,  IoU: 0.84274
[train: 20, 50 / 117] FPS: 58.8 (87.1)  ,  DataTime: 0.278 (0.077)  ,  ForwardTime: 0.733  ,  TotalTime: 1.088  ,  Loss/total: 0.70227  ,  Loss/giou: 0.16995  ,  Loss/l1: 0.01530  ,  Loss/location: 0.28585  ,  IoU: 0.84188
[train: 20, 50 / 117] FPS: 58.7 (87.1)  ,  DataTime: 0.186 (0.078)  ,  ForwardTime: 0.825  ,  TotalTime: 1.089  ,  Loss/total: 0.70973  ,  Loss/giou: 0.17155  ,  Loss/l1: 0.01511  ,  Loss/location: 0.29109  ,  IoU: 0.83986
[train: 20, 50 / 117] FPS: 58.7 (86.8)  ,  DataTime: 0.220 (0.079)  ,  ForwardTime: 0.791  ,  TotalTime: 1.090  ,  Loss/total: 0.71980  ,  Loss/giou: 0.17576  ,  Loss/l1: 0.01621  ,  Loss/location: 0.28725  ,  IoU: 0.83765
[train: 20, 50 / 117] FPS: 58.8 (86.6)  ,  DataTime: 0.244 (0.086)  ,  ForwardTime: 0.758  ,  TotalTime: 1.088  ,  Loss/total: 0.69040  ,  Loss/giou: 0.16786  ,  Loss/l1: 0.01475  ,  Loss/location: 0.28090  ,  IoU: 0.84386
[train: 20, 50 / 117] FPS: 58.8 (86.8)  ,  DataTime: 0.246 (0.081)  ,  ForwardTime: 0.761  ,  TotalTime: 1.088  ,  Loss/total: 0.73100  ,  Loss/giou: 0.17900  ,  Loss/l1: 0.01667  ,  Loss/location: 0.28967  ,  IoU: 0.83506[train: 20, 50 / 117] FPS: 58.8 (86.9)  ,  DataTime: 0.297 (0.083)  ,  ForwardTime: 0.709  ,  TotalTime: 1.088  ,  Loss/total: 0.70807  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28738  ,  IoU: 0.84044

[train: 20, 50 / 117] FPS: 58.8 (86.3)  ,  DataTime: 0.234 (0.070)  ,  ForwardTime: 0.784  ,  TotalTime: 1.088  ,  Loss/total: 0.73157  ,  Loss/giou: 0.17849  ,  Loss/l1: 0.01648  ,  Loss/location: 0.29220  ,  IoU: 0.83517
[train: 20, 100 / 117] FPS: 68.2 (101.5)  ,  DataTime: 0.117 (0.078)  ,  ForwardTime: 0.744  ,  TotalTime: 0.939  ,  Loss/total: 0.71830  ,  Loss/giou: 0.17282  ,  Loss/l1: 0.01543  ,  Loss/location: 0.29551  ,  IoU: 0.83912[train: 20, 100 / 117] FPS: 68.1 (101.3)  ,  DataTime: 0.141 (0.080)  ,  ForwardTime: 0.719  ,  TotalTime: 0.940  ,  Loss/total: 0.70100  ,  Loss/giou: 0.17133  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28149  ,  IoU: 0.84058

[train: 20, 100 / 117] FPS: 68.1 (101.5)  ,  DataTime: 0.121 (0.077)  ,  ForwardTime: 0.741  ,  TotalTime: 0.940  ,  Loss/total: 0.72217  ,  Loss/giou: 0.17286  ,  Loss/l1: 0.01551  ,  Loss/location: 0.29892  ,  IoU: 0.83910
[train: 20, 100 / 117] FPS: 68.2 (101.6)  ,  DataTime: 0.147 (0.071)  ,  ForwardTime: 0.721  ,  TotalTime: 0.939  ,  Loss/total: 0.72677  ,  Loss/giou: 0.17754  ,  Loss/l1: 0.01647  ,  Loss/location: 0.28935  ,  IoU: 0.83585
[train: 20, 100 / 117] FPS: 68.2 (101.2)  ,  DataTime: 0.154 (0.087)  ,  ForwardTime: 0.699  ,  TotalTime: 0.939  ,  Loss/total: 0.70123  ,  Loss/giou: 0.17051  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28464  ,  IoU: 0.84116
[train: 20, 100 / 117] FPS: 68.1 (101.3)  ,  DataTime: 0.155 (0.079)  ,  ForwardTime: 0.705  ,  TotalTime: 0.939  ,  Loss/total: 0.72562  ,  Loss/giou: 0.17675  ,  Loss/l1: 0.01638  ,  Loss/location: 0.29025  ,  IoU: 0.83673
[train: 20, 100 / 117] FPS: 68.2 (101.2)  ,  DataTime: 0.169 (0.078)  ,  ForwardTime: 0.693  ,  TotalTime: 0.939  ,  Loss/total: 0.71260  ,  Loss/giou: 0.17298  ,  Loss/l1: 0.01579  ,  Loss/location: 0.28766  ,  IoU: 0.83955
[train: 20, 100 / 117] FPS: 68.1 (101.2)  ,  DataTime: 0.179 (0.083)  ,  ForwardTime: 0.678  ,  TotalTime: 0.939  ,  Loss/total: 0.68900  ,  Loss/giou: 0.16773  ,  Loss/l1: 0.01477  ,  Loss/location: 0.27967  ,  IoU: 0.84316
[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.131 (0.066)  ,  ForwardTime: 0.690  ,  TotalTime: 0.887  ,  Loss/total: 0.73334  ,  Loss/giou: 0.17923  ,  Loss/l1: 0.01672  ,  Loss/location: 0.29128  ,  IoU: 0.83444[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.109 (0.073)  ,  ForwardTime: 0.707  ,  TotalTime: 0.888  ,  Loss/total: 0.72125  ,  Loss/giou: 0.17310  ,  Loss/l1: 0.01554  ,  Loss/location: 0.29735  ,  IoU: 0.83898

[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.138 (0.075)  ,  ForwardTime: 0.675  ,  TotalTime: 0.888  ,  Loss/total: 0.72436  ,  Loss/giou: 0.17658  ,  Loss/l1: 0.01620  ,  Loss/location: 0.29021  ,  IoU: 0.83649
[train: 20, 117 / 117] FPS: 72.1 (105.9)  ,  DataTime: 0.126 (0.075)  ,  ForwardTime: 0.687  ,  TotalTime: 0.888  ,  Loss/total: 0.70149  ,  Loss/giou: 0.17092  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28358  ,  IoU: 0.84073[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.137 (0.080)  ,  ForwardTime: 0.671  ,  TotalTime: 0.888  ,  Loss/total: 0.70504  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28548  ,  IoU: 0.84008

[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.159 (0.077)  ,  ForwardTime: 0.652  ,  TotalTime: 0.888  ,  Loss/total: 0.69174  ,  Loss/giou: 0.16772  ,  Loss/l1: 0.01472  ,  Loss/location: 0.28267  ,  IoU: 0.84308
[train: 20, 117 / 117] FPS: 72.1 (105.7)  ,  DataTime: 0.105 (0.073)  ,  ForwardTime: 0.709  ,  TotalTime: 0.887  ,  Loss/total: 0.71538  ,  Loss/giou: 0.17260  ,  Loss/l1: 0.01536  ,  Loss/location: 0.29335  ,  IoU: 0.83926
[train: 20, 117 / 117] FPS: 72.1 (105.6)  ,  DataTime: 0.149 (0.073)  ,  ForwardTime: 0.665  ,  TotalTime: 0.888  ,  Loss/total: 0.71188  ,  Loss/giou: 0.17320  ,  Loss/l1: 0.01580  ,  Loss/location: 0.28646  ,  IoU: 0.83937
Epoch Time: 0:01:43.839771
Avg Data Time: 0.14911
Avg GPU Trans Time: 0.07315
Avg Forward Time: 0.66526
Epoch Time: 0:01:43.907731
Avg Data Time: 0.10881
Avg GPU Trans Time: 0.07268
Avg Forward Time: 0.70660
Epoch Time: 0:01:43.912139
Avg Data Time: 0.12608
Avg GPU Trans Time: 0.07514
Avg Forward Time: 0.68692
Epoch Time: 0:01:43.849485
Avg Data Time: 0.13767
Avg GPU Trans Time: 0.07454
Avg Forward Time: 0.67540
Epoch Time: 0:01:43.841230
Avg Data Time: 0.13668
Avg GPU Trans Time: 0.08029
Avg Forward Time: 0.67057
Epoch Time: 0:01:43.854323
Avg Data Time: 0.15872
Avg GPU Trans Time: 0.07738
Avg Forward Time: 0.65154
Epoch Time: 0:01:43.834805
Avg Data Time: 0.13071
Avg GPU Trans Time: 0.06640
Avg Forward Time: 0.69037
Epoch Time: 0:01:43.815886
Avg Data Time: 0.10531
Avg GPU Trans Time: 0.07291
Avg Forward Time: 0.70909
[val: 20, 50 / 78] FPS: 9.9 (222.5)  ,  DataTime: 5.924 (0.050)  ,  ForwardTime: 0.503  ,  TotalTime: 6.477  ,  Loss/total: 0.84733  ,  Loss/giou: 0.20381  ,  Loss/l1: 0.02136  ,  Loss/location: 0.33294  ,  IoU: 0.81830
[val: 20, 50 / 78] FPS: 9.9 (210.7)  ,  DataTime: 6.205 (0.052)  ,  ForwardTime: 0.230  ,  TotalTime: 6.487  ,  Loss/total: 0.83031  ,  Loss/giou: 0.20326  ,  Loss/l1: 0.02105  ,  Loss/location: 0.31851  ,  IoU: 0.81821
[val: 20, 50 / 78] FPS: 9.8 (225.8)  ,  DataTime: 6.204 (0.045)  ,  ForwardTime: 0.303  ,  TotalTime: 6.552  ,  Loss/total: 0.82461  ,  Loss/giou: 0.19786  ,  Loss/l1: 0.02083  ,  Loss/location: 0.32473  ,  IoU: 0.82307
[val: 20, 50 / 78] FPS: 9.6 (202.2)  ,  DataTime: 6.415 (0.050)  ,  ForwardTime: 0.228  ,  TotalTime: 6.692  ,  Loss/total: 0.84724  ,  Loss/giou: 0.20258  ,  Loss/l1: 0.02062  ,  Loss/location: 0.33900  ,  IoU: 0.81845
[val: 20, 50 / 78] FPS: 9.4 (202.9)  ,  DataTime: 6.358 (0.053)  ,  ForwardTime: 0.362  ,  TotalTime: 6.773  ,  Loss/total: 0.84167  ,  Loss/giou: 0.20312  ,  Loss/l1: 0.02171  ,  Loss/location: 0.32687  ,  IoU: 0.81904
[val: 20, 50 / 78] FPS: 9.4 (234.8)  ,  DataTime: 6.197 (0.049)  ,  ForwardTime: 0.544  ,  TotalTime: 6.790  ,  Loss/total: 0.85460  ,  Loss/giou: 0.20618  ,  Loss/l1: 0.02246  ,  Loss/location: 0.32995  ,  IoU: 0.81783
[val: 20, 50 / 78] FPS: 9.4 (211.0)  ,  DataTime: 6.548 (0.049)  ,  ForwardTime: 0.229  ,  TotalTime: 6.826  ,  Loss/total: 0.83188  ,  Loss/giou: 0.19965  ,  Loss/l1: 0.02091  ,  Loss/location: 0.32804  ,  IoU: 0.82184
[val: 20, 50 / 78] FPS: 9.3 (190.9)  ,  DataTime: 6.585 (0.048)  ,  ForwardTime: 0.264  ,  TotalTime: 6.897  ,  Loss/total: 0.85745  ,  Loss/giou: 0.20574  ,  Loss/l1: 0.02150  ,  Loss/location: 0.33849  ,  IoU: 0.81597
[val: 20, 78 / 78] FPS: 11.3 (188.5)  ,  DataTime: 5.404 (0.054)  ,  ForwardTime: 0.230  ,  TotalTime: 5.688  ,  Loss/total: 0.83836  ,  Loss/giou: 0.20395  ,  Loss/l1: 0.02126  ,  Loss/location: 0.32415  ,  IoU: 0.81777
Epoch Time: 0:07:23.656941
Avg Data Time: 5.40428
Avg GPU Trans Time: 0.05377
Avg Forward Time: 0.22986
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 20, 78 / 78] FPS: 11.2 (78.8)  ,  DataTime: 5.330 (0.052)  ,  ForwardTime: 0.314  ,  TotalTime: 5.696  ,  Loss/total: 0.85965  ,  Loss/giou: 0.20592  ,  Loss/l1: 0.02204  ,  Loss/location: 0.33761  ,  IoU: 0.81686
Epoch Time: 0:07:24.256876
Avg Data Time: 5.32998
Avg GPU Trans Time: 0.05202
Avg Forward Time: 0.31360
[val: 20, 78 / 78] FPS: 11.2 (194.0)  ,  DataTime: 5.250 (0.050)  ,  ForwardTime: 0.405  ,  TotalTime: 5.705  ,  Loss/total: 0.84188  ,  Loss/giou: 0.20118  ,  Loss/l1: 0.02118  ,  Loss/location: 0.33361  ,  IoU: 0.82107
Epoch Time: 0:07:24.981383
Avg Data Time: 5.25041
Avg GPU Trans Time: 0.04971
Avg Forward Time: 0.40477
[val: 20, 78 / 78] FPS: 11.2 (179.5)  ,  DataTime: 5.401 (0.046)  ,  ForwardTime: 0.277  ,  TotalTime: 5.724  ,  Loss/total: 0.82180  ,  Loss/giou: 0.19720  ,  Loss/l1: 0.02049  ,  Loss/location: 0.32497  ,  IoU: 0.82334
[val: 20, 78 / 78] FPS: 11.2 (183.9)  ,  DataTime: 5.246 (0.049)  ,  ForwardTime: 0.430  ,  TotalTime: 5.725  ,  Loss/total: 0.84240  ,  Loss/giou: 0.20390  ,  Loss/l1: 0.02181  ,  Loss/location: 0.32554  ,  IoU: 0.81925
Epoch Time: 0:07:26.500349
Avg Data Time: 5.40122
Avg GPU Trans Time: 0.04598
Avg Forward Time: 0.27717
Epoch Time: 0:07:26.570036
Avg Data Time: 5.24562
Avg GPU Trans Time: 0.04934
Avg Forward Time: 0.43030
[val: 20, 78 / 78] FPS: 11.2 (198.0)  ,  DataTime: 5.432 (0.050)  ,  ForwardTime: 0.253  ,  TotalTime: 5.735  ,  Loss/total: 0.83866  ,  Loss/giou: 0.20105  ,  Loss/l1: 0.02128  ,  Loss/location: 0.33019  ,  IoU: 0.82070
[val: 20, 78 / 78] FPS: 11.2 (192.4)  ,  DataTime: 5.457 (0.049)  ,  ForwardTime: 0.230  ,  TotalTime: 5.736  ,  Loss/total: 0.82501  ,  Loss/giou: 0.19880  ,  Loss/l1: 0.02072  ,  Loss/location: 0.32383  ,  IoU: 0.82234
Epoch Time: 0:07:27.307090
Avg Data Time: 5.43171
Avg GPU Trans Time: 0.05038
Avg Forward Time: 0.25261
Epoch Time: 0:07:27.418749
Avg Data Time: 5.45744
Avg GPU Trans Time: 0.04905
Avg Forward Time: 0.22965
[val: 20, 78 / 78] FPS: 11.1 (171.2)  ,  DataTime: 5.482 (0.051)  ,  ForwardTime: 0.229  ,  TotalTime: 5.763  ,  Loss/total: 0.83398  ,  Loss/giou: 0.20179  ,  Loss/l1: 0.02101  ,  Loss/location: 0.32537  ,  IoU: 0.81985
Epoch Time: 0:07:29.525224
Avg Data Time: 5.48248
Avg GPU Trans Time: 0.05121
Avg Forward Time: 0.22945
[train: 21, 50 / 117] FPS: 61.2 (55.0)  ,  DataTime: 0.376 (0.063)  ,  ForwardTime: 0.607  ,  TotalTime: 1.046  ,  Loss/total: 0.71962  ,  Loss/giou: 0.17626  ,  Loss/l1: 0.01632  ,  Loss/location: 0.28548  ,  IoU: 0.83639
[train: 21, 50 / 117] FPS: 57.7 (55.2)  ,  DataTime: 0.158 (0.073)  ,  ForwardTime: 0.877  ,  TotalTime: 1.108  ,  Loss/total: 0.75603  ,  Loss/giou: 0.18210  ,  Loss/l1: 0.01763  ,  Loss/location: 0.30370  ,  IoU: 0.83338
[train: 21, 50 / 117] FPS: 58.7 (55.0)  ,  DataTime: 0.195 (0.065)  ,  ForwardTime: 0.831  ,  TotalTime: 1.091  ,  Loss/total: 0.71094  ,  Loss/giou: 0.17286  ,  Loss/l1: 0.01515  ,  Loss/location: 0.28949  ,  IoU: 0.83803
[train: 21, 50 / 117] FPS: 57.7 (55.0)  ,  DataTime: 0.266 (0.068)  ,  ForwardTime: 0.775  ,  TotalTime: 1.110  ,  Loss/total: 0.68791  ,  Loss/giou: 0.16797  ,  Loss/l1: 0.01478  ,  Loss/location: 0.27805  ,  IoU: 0.84338
[train: 21, 50 / 117] FPS: 55.4 (54.9)  ,  DataTime: 0.169 (0.073)  ,  ForwardTime: 0.913  ,  TotalTime: 1.155  ,  Loss/total: 0.68484  ,  Loss/giou: 0.16545  ,  Loss/l1: 0.01458  ,  Loss/location: 0.28102  ,  IoU: 0.84516
[train: 21, 50 / 117] FPS: 58.5 (55.0)  ,  DataTime: 0.223 (0.066)  ,  ForwardTime: 0.805  ,  TotalTime: 1.093  ,  Loss/total: 0.70075  ,  Loss/giou: 0.16985  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28422  ,  IoU: 0.84227
[train: 21, 50 / 117] FPS: 56.1 (55.0)  ,  DataTime: 0.149 (0.066)  ,  ForwardTime: 0.925  ,  TotalTime: 1.140  ,  Loss/total: 0.74239  ,  Loss/giou: 0.18208  ,  Loss/l1: 0.01691  ,  Loss/location: 0.29369  ,  IoU: 0.83255
[train: 21, 50 / 117] FPS: 55.6 (54.9)  ,  DataTime: 0.163 (0.070)  ,  ForwardTime: 0.917  ,  TotalTime: 1.151  ,  Loss/total: 0.69670  ,  Loss/giou: 0.17176  ,  Loss/l1: 0.01487  ,  Loss/location: 0.27881  ,  IoU: 0.83937
[train: 21, 100 / 117] FPS: 63.1 (92.5)  ,  DataTime: 0.180 (0.070)  ,  ForwardTime: 0.764  ,  TotalTime: 1.014  ,  Loss/total: 0.70748  ,  Loss/giou: 0.17164  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28738  ,  IoU: 0.84045[train: 21, 100 / 117] FPS: 61.9 (92.8)  ,  DataTime: 0.109 (0.073)  ,  ForwardTime: 0.852  ,  TotalTime: 1.034  ,  Loss/total: 0.70021  ,  Loss/giou: 0.17208  ,  Loss/l1: 0.01503  ,  Loss/location: 0.28089  ,  IoU: 0.83944

[train: 21, 100 / 117] FPS: 63.2 (92.7)  ,  DataTime: 0.126 (0.072)  ,  ForwardTime: 0.816  ,  TotalTime: 1.013  ,  Loss/total: 0.72599  ,  Loss/giou: 0.17608  ,  Loss/l1: 0.01657  ,  Loss/location: 0.29097  ,  IoU: 0.83784
[train: 21, 100 / 117] FPS: 63.7 (92.4)  ,  DataTime: 0.168 (0.069)  ,  ForwardTime: 0.768  ,  TotalTime: 1.005  ,  Loss/total: 0.70995  ,  Loss/giou: 0.17225  ,  Loss/l1: 0.01514  ,  Loss/location: 0.28973  ,  IoU: 0.83881
[train: 21, 100 / 117] FPS: 61.7 (92.6)  ,  DataTime: 0.115 (0.074)  ,  ForwardTime: 0.848  ,  TotalTime: 1.037  ,  Loss/total: 0.68379  ,  Loss/giou: 0.16637  ,  Loss/l1: 0.01453  ,  Loss/location: 0.27840  ,  IoU: 0.84406
[train: 21, 100 / 117] FPS: 63.6 (92.3)  ,  DataTime: 0.139 (0.070)  ,  ForwardTime: 0.796  ,  TotalTime: 1.006  ,  Loss/total: 0.71420  ,  Loss/giou: 0.17273  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28989  ,  IoU: 0.83980
[train: 21, 100 / 117] FPS: 65.1 (92.5)  ,  DataTime: 0.239 (0.068)  ,  ForwardTime: 0.676  ,  TotalTime: 0.982  ,  Loss/total: 0.71901  ,  Loss/giou: 0.17563  ,  Loss/l1: 0.01597  ,  Loss/location: 0.28791  ,  IoU: 0.83673
[train: 21, 100 / 117] FPS: 62.2 (92.0)  ,  DataTime: 0.100 (0.068)  ,  ForwardTime: 0.862  ,  TotalTime: 1.029  ,  Loss/total: 0.73342  ,  Loss/giou: 0.17958  ,  Loss/l1: 0.01650  ,  Loss/location: 0.29173  ,  IoU: 0.83411
[train: 21, 117 / 117] FPS: 66.2 (107.1)  ,  DataTime: 0.090 (0.065)  ,  ForwardTime: 0.812  ,  TotalTime: 0.967  ,  Loss/total: 0.73227  ,  Loss/giou: 0.17947  ,  Loss/l1: 0.01655  ,  Loss/location: 0.29055  ,  IoU: 0.83433[train: 21, 117 / 117] FPS: 67.1 (107.2)  ,  DataTime: 0.159 (0.067)  ,  ForwardTime: 0.728  ,  TotalTime: 0.954  ,  Loss/total: 0.69788  ,  Loss/giou: 0.16951  ,  Loss/l1: 0.01499  ,  Loss/location: 0.28392  ,  IoU: 0.84196
[train: 21, 117 / 117] FPS: 69.0 (107.2)  ,  DataTime: 0.210 (0.065)  ,  ForwardTime: 0.653  ,  TotalTime: 0.927  ,  Loss/total: 0.71811  ,  Loss/giou: 0.17485  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28925  ,  IoU: 0.83743

[train: 21, 117 / 117] FPS: 67.1 (107.1)  ,  DataTime: 0.113 (0.069)  ,  ForwardTime: 0.772  ,  TotalTime: 0.954  ,  Loss/total: 0.73019  ,  Loss/giou: 0.17628  ,  Loss/l1: 0.01661  ,  Loss/location: 0.29459  ,  IoU: 0.83756
[train: 21, 117 / 117] FPS: 67.6 (107.1)  ,  DataTime: 0.149 (0.065)  ,  ForwardTime: 0.732  ,  TotalTime: 0.946  ,  Loss/total: 0.72069  ,  Loss/giou: 0.17479  ,  Loss/l1: 0.01557  ,  Loss/location: 0.29325  ,  IoU: 0.83705
[train: 21, 117 / 117] FPS: 67.6 (107.1)  ,  DataTime: 0.124 (0.067)  ,  ForwardTime: 0.756  ,  TotalTime: 0.947  ,  Loss/total: 0.71941  ,  Loss/giou: 0.17318  ,  Loss/l1: 0.01577  ,  Loss/location: 0.29420  ,  IoU: 0.83910
[train: 21, 117 / 117] FPS: 65.7 (107.5)  ,  DataTime: 0.104 (0.071)  ,  ForwardTime: 0.799  ,  TotalTime: 0.974  ,  Loss/total: 0.68505  ,  Loss/giou: 0.16657  ,  Loss/l1: 0.01461  ,  Loss/location: 0.27886  ,  IoU: 0.84409
[train: 21, 117 / 117] FPS: 65.9 (107.1)  ,  DataTime: 0.098 (0.069)  ,  ForwardTime: 0.804  ,  TotalTime: 0.972  ,  Loss/total: 0.69489  ,  Loss/giou: 0.17059  ,  Loss/l1: 0.01479  ,  Loss/location: 0.27976  ,  IoU: 0.84044
Epoch Time: 0:01:48.468702
Avg Data Time: 0.20961
Avg GPU Trans Time: 0.06490
Avg Forward Time: 0.65258
Epoch Time: 0:01:51.571911
Avg Data Time: 0.11288
Avg GPU Trans Time: 0.06854
Avg Forward Time: 0.77219
Epoch Time: 0:01:53.673777
Avg Data Time: 0.09842
Avg GPU Trans Time: 0.06924
Avg Forward Time: 0.80391
Epoch Time: 0:01:53.918073
Avg Data Time: 0.10366
Avg GPU Trans Time: 0.07068
Avg Forward Time: 0.79932
Epoch Time: 0:01:50.820452
Avg Data Time: 0.12431
Avg GPU Trans Time: 0.06704
Avg Forward Time: 0.75583
Epoch Time: 0:01:53.166941
Avg Data Time: 0.09042
Avg GPU Trans Time: 0.06504
Avg Forward Time: 0.81178
Epoch Time: 0:01:50.702929
Avg Data Time: 0.14875
Avg GPU Trans Time: 0.06530
Avg Forward Time: 0.73213
Epoch Time: 0:01:51.642514
Avg Data Time: 0.15937
Avg GPU Trans Time: 0.06720
Avg Forward Time: 0.72765
[train: 22, 50 / 117] FPS: 59.3 (87.8)  ,  DataTime: 0.216 (0.080)  ,  ForwardTime: 0.783  ,  TotalTime: 1.079  ,  Loss/total: 0.73544  ,  Loss/giou: 0.17585  ,  Loss/l1: 0.01586  ,  Loss/location: 0.30443  ,  IoU: 0.83693
[train: 22, 50 / 117] FPS: 59.3 (87.6)  ,  DataTime: 0.284 (0.075)  ,  ForwardTime: 0.720  ,  TotalTime: 1.079  ,  Loss/total: 0.70191  ,  Loss/giou: 0.17204  ,  Loss/l1: 0.01559  ,  Loss/location: 0.27987  ,  IoU: 0.83964
[train: 22, 50 / 117] FPS: 59.3 (87.3)  ,  DataTime: 0.289 (0.083)  ,  ForwardTime: 0.707  ,  TotalTime: 1.079  ,  Loss/total: 0.70247  ,  Loss/giou: 0.17083  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28357  ,  IoU: 0.84144
[train: 22, 50 / 117] FPS: 59.3 (87.2)  ,  DataTime: 0.310 (0.078)  ,  ForwardTime: 0.691  ,  TotalTime: 1.079  ,  Loss/total: 0.70859  ,  Loss/giou: 0.17148  ,  Loss/l1: 0.01504  ,  Loss/location: 0.29044  ,  IoU: 0.83974[train: 22, 50 / 117] FPS: 59.3 (87.5)  ,  DataTime: 0.259 (0.080)  ,  ForwardTime: 0.740  ,  TotalTime: 1.079  ,  Loss/total: 0.72338  ,  Loss/giou: 0.17589  ,  Loss/l1: 0.01652  ,  Loss/location: 0.28898  ,  IoU: 0.83773
[train: 22, 50 / 117] FPS: 59.3 (87.2)  ,  DataTime: 0.289 (0.075)  ,  ForwardTime: 0.714  ,  TotalTime: 1.078  ,  Loss/total: 0.74545  ,  Loss/giou: 0.18113  ,  Loss/l1: 0.01694  ,  Loss/location: 0.29850  ,  IoU: 0.83326

[train: 22, 50 / 117] FPS: 59.3 (87.5)  ,  DataTime: 0.346 (0.073)  ,  ForwardTime: 0.660  ,  TotalTime: 1.079  ,  Loss/total: 0.70588  ,  Loss/giou: 0.17048  ,  Loss/l1: 0.01488  ,  Loss/location: 0.29050  ,  IoU: 0.84047
[train: 22, 50 / 117] FPS: 59.3 (87.1)  ,  DataTime: 0.259 (0.070)  ,  ForwardTime: 0.750  ,  TotalTime: 1.079  ,  Loss/total: 0.75254  ,  Loss/giou: 0.18007  ,  Loss/l1: 0.01687  ,  Loss/location: 0.30803  ,  IoU: 0.83410
[train: 22, 100 / 117] FPS: 67.8 (91.4)  ,  DataTime: 0.173 (0.076)  ,  ForwardTime: 0.694  ,  TotalTime: 0.944  ,  Loss/total: 0.71360  ,  Loss/giou: 0.17343  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28824  ,  IoU: 0.83858
[train: 22, 100 / 117] FPS: 67.8 (91.4)  ,  DataTime: 0.178 (0.086)  ,  ForwardTime: 0.680  ,  TotalTime: 0.944  ,  Loss/total: 0.69154  ,  Loss/giou: 0.16936  ,  Loss/l1: 0.01505  ,  Loss/location: 0.27757  ,  IoU: 0.84204[train: 22, 100 / 117] FPS: 67.8 (91.0)  ,  DataTime: 0.160 (0.079)  ,  ForwardTime: 0.705  ,  TotalTime: 0.944  ,  Loss/total: 0.71483  ,  Loss/giou: 0.17368  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28834  ,  IoU: 0.83897[train: 22, 100 / 117] FPS: 67.8 (91.5)  ,  DataTime: 0.204 (0.073)  ,  ForwardTime: 0.667  ,  TotalTime: 0.944  ,  Loss/total: 0.71190  ,  Loss/giou: 0.17341  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28662  ,  IoU: 0.83898


[train: 22, 100 / 117] FPS: 67.8 (91.4)  ,  DataTime: 0.173 (0.076)  ,  ForwardTime: 0.695  ,  TotalTime: 0.944  ,  Loss/total: 0.72993  ,  Loss/giou: 0.17685  ,  Loss/l1: 0.01647  ,  Loss/location: 0.29388  ,  IoU: 0.83671
[train: 22, 100 / 117] FPS: 67.8 (91.0)  ,  DataTime: 0.139 (0.078)  ,  ForwardTime: 0.727  ,  TotalTime: 0.944  ,  Loss/total: 0.72520  ,  Loss/giou: 0.17513  ,  Loss/l1: 0.01605  ,  Loss/location: 0.29469  ,  IoU: 0.83827
[train: 22, 100 / 117] FPS: 67.8 (91.1)  ,  DataTime: 0.188 (0.082)  ,  ForwardTime: 0.673  ,  TotalTime: 0.944  ,  Loss/total: 0.71535  ,  Loss/giou: 0.17269  ,  Loss/l1: 0.01547  ,  Loss/location: 0.29266  ,  IoU: 0.83917
[train: 22, 100 / 117] FPS: 67.8 (91.0)  ,  DataTime: 0.160 (0.073)  ,  ForwardTime: 0.711  ,  TotalTime: 0.944  ,  Loss/total: 0.73501  ,  Loss/giou: 0.17672  ,  Loss/l1: 0.01641  ,  Loss/location: 0.29949  ,  IoU: 0.83668
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.154 (0.072)  ,  ForwardTime: 0.667  ,  TotalTime: 0.893  ,  Loss/total: 0.71259  ,  Loss/giou: 0.17366  ,  Loss/l1: 0.01574  ,  Loss/location: 0.28656  ,  IoU: 0.83847
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.153 (0.072)  ,  ForwardTime: 0.667  ,  TotalTime: 0.893  ,  Loss/total: 0.72965  ,  Loss/giou: 0.17650  ,  Loss/l1: 0.01641  ,  Loss/location: 0.29461  ,  IoU: 0.83693
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.124 (0.073)  ,  ForwardTime: 0.696  ,  TotalTime: 0.893  ,  Loss/total: 0.72402  ,  Loss/giou: 0.17516  ,  Loss/l1: 0.01612  ,  Loss/location: 0.29309  ,  IoU: 0.83818
[train: 22, 117 / 117] FPS: 71.7 (109.8)  ,  DataTime: 0.158 (0.080)  ,  ForwardTime: 0.655  ,  TotalTime: 0.893  ,  Loss/total: 0.69150  ,  Loss/giou: 0.16937  ,  Loss/l1: 0.01510  ,  Loss/location: 0.27724  ,  IoU: 0.84228
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.143 (0.068)  ,  ForwardTime: 0.682  ,  TotalTime: 0.893  ,  Loss/total: 0.73760  ,  Loss/giou: 0.17766  ,  Loss/l1: 0.01645  ,  Loss/location: 0.30001  ,  IoU: 0.83578
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.179 (0.069)  ,  ForwardTime: 0.645  ,  TotalTime: 0.893  ,  Loss/total: 0.70900  ,  Loss/giou: 0.17340  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28337  ,  IoU: 0.83920
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.142 (0.075)  ,  ForwardTime: 0.676  ,  TotalTime: 0.893  ,  Loss/total: 0.71810  ,  Loss/giou: 0.17419  ,  Loss/l1: 0.01587  ,  Loss/location: 0.29038  ,  IoU: 0.83859
[train: 22, 117 / 117] FPS: 71.7 (109.7)  ,  DataTime: 0.167 (0.077)  ,  ForwardTime: 0.650  ,  TotalTime: 0.893  ,  Loss/total: 0.71834  ,  Loss/giou: 0.17335  ,  Loss/l1: 0.01563  ,  Loss/location: 0.29350  ,  IoU: 0.83876
Epoch Time: 0:01:44.438904
Avg Data Time: 0.15332
Avg GPU Trans Time: 0.07203
Avg Forward Time: 0.66729
Epoch Time: 0:01:44.461468
Avg Data Time: 0.14239
Avg GPU Trans Time: 0.07460
Avg Forward Time: 0.67584
Epoch Time: 0:01:44.478077
Avg Data Time: 0.12398
Avg GPU Trans Time: 0.07307
Avg Forward Time: 0.69592
Epoch Time: 0:01:44.479101
Avg Data Time: 0.17944
Avg GPU Trans Time: 0.06870
Avg Forward Time: 0.64484
Epoch Time: 0:01:44.468355
Avg Data Time: 0.16655
Avg GPU Trans Time: 0.07668
Avg Forward Time: 0.64967
Epoch Time: 0:01:44.453180
Avg Data Time: 0.15789
Avg GPU Trans Time: 0.07977
Avg Forward Time: 0.65510
Epoch Time: 0:01:44.459963
Avg Data Time: 0.15384
Avg GPU Trans Time: 0.07176
Avg Forward Time: 0.66722
Epoch Time: 0:01:44.457159
Avg Data Time: 0.14291
Avg GPU Trans Time: 0.06791
Avg Forward Time: 0.68198
[train: 23, 50 / 117] FPS: 59.2 (79.1)  ,  DataTime: 0.282 (0.076)  ,  ForwardTime: 0.725  ,  TotalTime: 1.082  ,  Loss/total: 0.75409  ,  Loss/giou: 0.18541  ,  Loss/l1: 0.01776  ,  Loss/location: 0.29448  ,  IoU: 0.83065
[train: 23, 50 / 117] FPS: 59.2 (79.1)  ,  DataTime: 0.290 (0.076)  ,  ForwardTime: 0.715  ,  TotalTime: 1.082  ,  Loss/total: 0.67631  ,  Loss/giou: 0.16430  ,  Loss/l1: 0.01457  ,  Loss/location: 0.27487  ,  IoU: 0.84630
[train: 23, 50 / 117] FPS: 59.1 (79.4)  ,  DataTime: 0.176 (0.078)  ,  ForwardTime: 0.828  ,  TotalTime: 1.082  ,  Loss/total: 0.73256  ,  Loss/giou: 0.17717  ,  Loss/l1: 0.01587  ,  Loss/location: 0.29888  ,  IoU: 0.83583
[train: 23, 50 / 117] FPS: 59.2 (79.2)  ,  DataTime: 0.263 (0.084)  ,  ForwardTime: 0.735  ,  TotalTime: 1.082  ,  Loss/total: 0.68966  ,  Loss/giou: 0.16833  ,  Loss/l1: 0.01517  ,  Loss/location: 0.27714  ,  IoU: 0.84309
[train: 23, 50 / 117] FPS: 59.1 (79.8)  ,  DataTime: 0.316 (0.077)  ,  ForwardTime: 0.690  ,  TotalTime: 1.083  ,  Loss/total: 0.72199  ,  Loss/giou: 0.17307  ,  Loss/l1: 0.01555  ,  Loss/location: 0.29810  ,  IoU: 0.83938[train: 23, 50 / 117] FPS: 59.2 (79.0)  ,  DataTime: 0.224 (0.066)  ,  ForwardTime: 0.792  ,  TotalTime: 1.082  ,  Loss/total: 0.68175  ,  Loss/giou: 0.16862  ,  Loss/l1: 0.01490  ,  Loss/location: 0.27001  ,  IoU: 0.84252

[train: 23, 50 / 117] FPS: 59.1 (79.1)  ,  DataTime: 0.186 (0.075)  ,  ForwardTime: 0.822  ,  TotalTime: 1.082  ,  Loss/total: 0.70943  ,  Loss/giou: 0.17221  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28721  ,  IoU: 0.84075
[train: 23, 50 / 117] FPS: 59.1 (78.6)  ,  DataTime: 0.248 (0.075)  ,  ForwardTime: 0.760  ,  TotalTime: 1.082  ,  Loss/total: 0.73631  ,  Loss/giou: 0.17782  ,  Loss/l1: 0.01639  ,  Loss/location: 0.29873  ,  IoU: 0.83595
[train: 23, 100 / 117] FPS: 68.2 (92.5)  ,  DataTime: 0.123 (0.076)  ,  ForwardTime: 0.739  ,  TotalTime: 0.939  ,  Loss/total: 0.70536  ,  Loss/giou: 0.17248  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28242  ,  IoU: 0.84035[train: 23, 100 / 117] FPS: 68.2 (92.6)  ,  DataTime: 0.143 (0.068)  ,  ForwardTime: 0.728  ,  TotalTime: 0.938  ,  Loss/total: 0.70108  ,  Loss/giou: 0.17213  ,  Loss/l1: 0.01553  ,  Loss/location: 0.27917  ,  IoU: 0.84018

[train: 23, 100 / 117] FPS: 68.2 (92.5)  ,  DataTime: 0.117 (0.080)  ,  ForwardTime: 0.742  ,  TotalTime: 0.939  ,  Loss/total: 0.73160  ,  Loss/giou: 0.17685  ,  Loss/l1: 0.01617  ,  Loss/location: 0.29706  ,  IoU: 0.83662
[train: 23, 100 / 117] FPS: 68.2 (92.7)  ,  DataTime: 0.164 (0.085)  ,  ForwardTime: 0.689  ,  TotalTime: 0.939  ,  Loss/total: 0.69718  ,  Loss/giou: 0.16980  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28032  ,  IoU: 0.84230
[train: 23, 100 / 117] FPS: 68.2 (92.4)  ,  DataTime: 0.190 (0.078)  ,  ForwardTime: 0.671  ,  TotalTime: 0.939  ,  Loss/total: 0.71341  ,  Loss/giou: 0.17164  ,  Loss/l1: 0.01537  ,  Loss/location: 0.29327  ,  IoU: 0.84005
[train: 23, 100 / 117] FPS: 68.2 (92.6)  ,  DataTime: 0.154 (0.078)  ,  ForwardTime: 0.707  ,  TotalTime: 0.939  ,  Loss/total: 0.73447  ,  Loss/giou: 0.17777  ,  Loss/l1: 0.01648  ,  Loss/location: 0.29653  ,  IoU: 0.83564
[train: 23, 100 / 117] FPS: 68.2 (92.5)  ,  DataTime: 0.174 (0.079)  ,  ForwardTime: 0.685  ,  TotalTime: 0.938  ,  Loss/total: 0.70647  ,  Loss/giou: 0.17108  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28641  ,  IoU: 0.84099
[train: 23, 100 / 117] FPS: 68.2 (92.5)  ,  DataTime: 0.170 (0.077)  ,  ForwardTime: 0.691  ,  TotalTime: 0.939  ,  Loss/total: 0.74230  ,  Loss/giou: 0.18056  ,  Loss/l1: 0.01689  ,  Loss/location: 0.29671  ,  IoU: 0.83382
[train: 23, 117 / 117] FPS: 72.1 (108.6)  ,  DataTime: 0.127 (0.064)  ,  ForwardTime: 0.697  ,  TotalTime: 0.888  ,  Loss/total: 0.70336  ,  Loss/giou: 0.17234  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28035  ,  IoU: 0.84026
[train: 23, 117 / 117] FPS: 72.1 (108.7)  ,  DataTime: 0.110 (0.071)  ,  ForwardTime: 0.706  ,  TotalTime: 0.888  ,  Loss/total: 0.70018  ,  Loss/giou: 0.17059  ,  Loss/l1: 0.01523  ,  Loss/location: 0.28282  ,  IoU: 0.84148
[train: 23, 117 / 117] FPS: 72.1 (108.9)  ,  DataTime: 0.167 (0.073)  ,  ForwardTime: 0.648  ,  TotalTime: 0.888  ,  Loss/total: 0.71165  ,  Loss/giou: 0.17163  ,  Loss/l1: 0.01538  ,  Loss/location: 0.29149  ,  IoU: 0.84004
[train: 23, 117 / 117] FPS: 72.1 (108.5)  ,  DataTime: 0.151 (0.073)  ,  ForwardTime: 0.664  ,  TotalTime: 0.888  ,  Loss/total: 0.73568  ,  Loss/giou: 0.17930  ,  Loss/l1: 0.01661  ,  Loss/location: 0.29402  ,  IoU: 0.83466
[train: 23, 117 / 117] FPS: 72.1 (108.5)  ,  DataTime: 0.155 (0.075)  ,  ForwardTime: 0.658  ,  TotalTime: 0.888  ,  Loss/total: 0.71235  ,  Loss/giou: 0.17264  ,  Loss/l1: 0.01573  ,  Loss/location: 0.28840  ,  IoU: 0.83959[train: 23, 117 / 117] FPS: 72.1 (108.5)  ,  DataTime: 0.136 (0.074)  ,  ForwardTime: 0.677  ,  TotalTime: 0.888  ,  Loss/total: 0.73764  ,  Loss/giou: 0.17862  ,  Loss/l1: 0.01657  ,  Loss/location: 0.29752  ,  IoU: 0.83501

[train: 23, 117 / 117] FPS: 72.1 (108.5)  ,  DataTime: 0.105 (0.075)  ,  ForwardTime: 0.708  ,  TotalTime: 0.888  ,  Loss/total: 0.73000  ,  Loss/giou: 0.17695  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29516  ,  IoU: 0.83670
[train: 23, 117 / 117] FPS: 72.1 (108.6)  ,  DataTime: 0.145 (0.079)  ,  ForwardTime: 0.663  ,  TotalTime: 0.888  ,  Loss/total: 0.69747  ,  Loss/giou: 0.17013  ,  Loss/l1: 0.01545  ,  Loss/location: 0.27993  ,  IoU: 0.84203
Epoch Time: 0:01:43.869528
Avg Data Time: 0.13615
Avg GPU Trans Time: 0.07421
Avg Forward Time: 0.67741
Epoch Time: 0:01:43.855447
Avg Data Time: 0.15479
Avg GPU Trans Time: 0.07458
Avg Forward Time: 0.65828
Epoch Time: 0:01:43.860497
Avg Data Time: 0.15086
Avg GPU Trans Time: 0.07313
Avg Forward Time: 0.66371
Epoch Time: 0:01:43.862069
Avg Data Time: 0.11003
Avg GPU Trans Time: 0.07129
Avg Forward Time: 0.70638
Epoch Time: 0:01:43.877665
Avg Data Time: 0.10499
Avg GPU Trans Time: 0.07520
Avg Forward Time: 0.70765
Epoch Time: 0:01:43.849386
Avg Data Time: 0.12699
Avg GPU Trans Time: 0.06405
Avg Forward Time: 0.69656
Epoch Time: 0:01:43.897522
Avg Data Time: 0.16721
Avg GPU Trans Time: 0.07320
Avg Forward Time: 0.64761
Epoch Time: 0:01:43.860622
Avg Data Time: 0.14498
Avg GPU Trans Time: 0.07928
Avg Forward Time: 0.66344
[train: 24, 50 / 117] FPS: 59.1 (90.1)  ,  DataTime: 0.229 (0.081)  ,  ForwardTime: 0.773  ,  TotalTime: 1.082  ,  Loss/total: 0.69886  ,  Loss/giou: 0.16636  ,  Loss/l1: 0.01443  ,  Loss/location: 0.29397  ,  IoU: 0.84369
[train: 24, 50 / 117] FPS: 59.2 (90.2)  ,  DataTime: 0.219 (0.077)  ,  ForwardTime: 0.785  ,  TotalTime: 1.081  ,  Loss/total: 0.72986  ,  Loss/giou: 0.17697  ,  Loss/l1: 0.01656  ,  Loss/location: 0.29313  ,  IoU: 0.83681
[train: 24, 50 / 117] FPS: 59.1 (90.0)  ,  DataTime: 0.248 (0.076)  ,  ForwardTime: 0.759  ,  TotalTime: 1.083  ,  Loss/total: 0.75415  ,  Loss/giou: 0.18403  ,  Loss/l1: 0.01765  ,  Loss/location: 0.29786  ,  IoU: 0.83118
[train: 24, 50 / 117] FPS: 59.2 (90.2)  ,  DataTime: 0.236 (0.077)  ,  ForwardTime: 0.769  ,  TotalTime: 1.081  ,  Loss/total: 0.69321  ,  Loss/giou: 0.16703  ,  Loss/l1: 0.01451  ,  Loss/location: 0.28661  ,  IoU: 0.84373
[train: 24, 50 / 117] FPS: 59.2 (89.9)  ,  DataTime: 0.132 (0.073)  ,  ForwardTime: 0.877  ,  TotalTime: 1.081  ,  Loss/total: 0.72956  ,  Loss/giou: 0.17681  ,  Loss/l1: 0.01613  ,  Loss/location: 0.29528  ,  IoU: 0.83616
[train: 24, 50 / 117] FPS: 59.2 (90.1)  ,  DataTime: 0.351 (0.081)  ,  ForwardTime: 0.649  ,  TotalTime: 1.081  ,  Loss/total: 0.72863  ,  Loss/giou: 0.17488  ,  Loss/l1: 0.01578  ,  Loss/location: 0.29996  ,  IoU: 0.83773
[train: 24, 50 / 117] FPS: 59.2 (90.2)  ,  DataTime: 0.305 (0.071)  ,  ForwardTime: 0.704  ,  TotalTime: 1.081  ,  Loss/total: 0.71253  ,  Loss/giou: 0.17487  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28481  ,  IoU: 0.83760
[train: 24, 50 / 117] FPS: 59.2 (89.4)  ,  DataTime: 0.186 (0.070)  ,  ForwardTime: 0.824  ,  TotalTime: 1.081  ,  Loss/total: 0.70975  ,  Loss/giou: 0.17410  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28448  ,  IoU: 0.83766
[train: 24, 100 / 117] FPS: 68.8 (96.4)  ,  DataTime: 0.092 (0.075)  ,  ForwardTime: 0.763  ,  TotalTime: 0.930  ,  Loss/total: 0.72422  ,  Loss/giou: 0.17596  ,  Loss/l1: 0.01614  ,  Loss/location: 0.29161  ,  IoU: 0.83700
[train: 24, 100 / 117] FPS: 68.8 (96.4)  ,  DataTime: 0.205 (0.084)  ,  ForwardTime: 0.640  ,  TotalTime: 0.930  ,  Loss/total: 0.72566  ,  Loss/giou: 0.17539  ,  Loss/l1: 0.01593  ,  Loss/location: 0.29522  ,  IoU: 0.83768
[train: 24, 100 / 117] FPS: 68.8 (96.8)  ,  DataTime: 0.124 (0.072)  ,  ForwardTime: 0.734  ,  TotalTime: 0.930  ,  Loss/total: 0.71287  ,  Loss/giou: 0.17495  ,  Loss/l1: 0.01588  ,  Loss/location: 0.28358  ,  IoU: 0.83745
[train: 24, 100 / 117] FPS: 68.8 (96.3)  ,  DataTime: 0.150 (0.080)  ,  ForwardTime: 0.700  ,  TotalTime: 0.930  ,  Loss/total: 0.70344  ,  Loss/giou: 0.16901  ,  Loss/l1: 0.01502  ,  Loss/location: 0.29033  ,  IoU: 0.84261
[train: 24, 100 / 117] FPS: 68.8 (96.2)  ,  DataTime: 0.152 (0.076)  ,  ForwardTime: 0.702  ,  TotalTime: 0.931  ,  Loss/total: 0.74576  ,  Loss/giou: 0.18073  ,  Loss/l1: 0.01699  ,  Loss/location: 0.29937  ,  IoU: 0.83367
[train: 24, 100 / 117] FPS: 68.8 (96.5)  ,  DataTime: 0.143 (0.081)  ,  ForwardTime: 0.706  ,  TotalTime: 0.930  ,  Loss/total: 0.71798  ,  Loss/giou: 0.17145  ,  Loss/l1: 0.01518  ,  Loss/location: 0.29916  ,  IoU: 0.83980
[train: 24, 100 / 117] FPS: 68.8 (96.1)  ,  DataTime: 0.180 (0.071)  ,  ForwardTime: 0.678  ,  TotalTime: 0.930  ,  Loss/total: 0.70748  ,  Loss/giou: 0.17255  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28440  ,  IoU: 0.84011
[train: 24, 100 / 117] FPS: 68.8 (96.0)  ,  DataTime: 0.138 (0.078)  ,  ForwardTime: 0.714  ,  TotalTime: 0.930  ,  Loss/total: 0.72232  ,  Loss/giou: 0.17570  ,  Loss/l1: 0.01621  ,  Loss/location: 0.28987  ,  IoU: 0.83758
[train: 24, 117 / 117] FPS: 72.6 (108.9)  ,  DataTime: 0.128 (0.076)  ,  ForwardTime: 0.677  ,  TotalTime: 0.881  ,  Loss/total: 0.71641  ,  Loss/giou: 0.17122  ,  Loss/l1: 0.01522  ,  Loss/location: 0.29785  ,  IoU: 0.84017
[train: 24, 117 / 117] FPS: 72.6 (108.8)  ,  DataTime: 0.135 (0.072)  ,  ForwardTime: 0.675  ,  TotalTime: 0.882  ,  Loss/total: 0.73901  ,  Loss/giou: 0.17970  ,  Loss/l1: 0.01686  ,  Loss/location: 0.29529  ,  IoU: 0.83467
[train: 24, 117 / 117] FPS: 72.7 (109.0)  ,  DataTime: 0.181 (0.079)  ,  ForwardTime: 0.621  ,  TotalTime: 0.881  ,  Loss/total: 0.72058  ,  Loss/giou: 0.17470  ,  Loss/l1: 0.01583  ,  Loss/location: 0.29202  ,  IoU: 0.83821
[train: 24, 117 / 117] FPS: 72.7 (108.7)  ,  DataTime: 0.123 (0.073)  ,  ForwardTime: 0.685  ,  TotalTime: 0.881  ,  Loss/total: 0.71959  ,  Loss/giou: 0.17500  ,  Loss/l1: 0.01613  ,  Loss/location: 0.28895  ,  IoU: 0.83813
[train: 24, 117 / 117] FPS: 72.7 (108.8)  ,  DataTime: 0.159 (0.067)  ,  ForwardTime: 0.655  ,  TotalTime: 0.881  ,  Loss/total: 0.70964  ,  Loss/giou: 0.17347  ,  Loss/l1: 0.01600  ,  Loss/location: 0.28270  ,  IoU: 0.83989
[train: 24, 117 / 117] FPS: 72.6 (108.9)  ,  DataTime: 0.084 (0.072)  ,  ForwardTime: 0.725  ,  TotalTime: 0.881  ,  Loss/total: 0.72389  ,  Loss/giou: 0.17614  ,  Loss/l1: 0.01618  ,  Loss/location: 0.29068  ,  IoU: 0.83686
[train: 24, 117 / 117] FPS: 72.7 (108.8)  ,  DataTime: 0.111 (0.068)  ,  ForwardTime: 0.702  ,  TotalTime: 0.881  ,  Loss/total: 0.71430  ,  Loss/giou: 0.17444  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28566  ,  IoU: 0.83811
[train: 24, 117 / 117] FPS: 72.7 (108.6)  ,  DataTime: 0.133 (0.075)  ,  ForwardTime: 0.673  ,  TotalTime: 0.881  ,  Loss/total: 0.70227  ,  Loss/giou: 0.16945  ,  Loss/l1: 0.01503  ,  Loss/location: 0.28823  ,  IoU: 0.84213
Epoch Time: 0:01:43.162272
Avg Data Time: 0.13491
Avg GPU Trans Time: 0.07179
Avg Forward Time: 0.67503
Epoch Time: 0:01:43.082701
Avg Data Time: 0.08398
Avg GPU Trans Time: 0.07175
Avg Forward Time: 0.72532
Epoch Time: 0:01:43.122932
Avg Data Time: 0.12771
Avg GPU Trans Time: 0.07618
Avg Forward Time: 0.67750
Epoch Time: 0:01:43.060890
Avg Data Time: 0.12323
Avg GPU Trans Time: 0.07310
Avg Forward Time: 0.68453
Epoch Time: 0:01:43.063277
Avg Data Time: 0.11115
Avg GPU Trans Time: 0.06800
Avg Forward Time: 0.70173
Epoch Time: 0:01:43.056377
Avg Data Time: 0.13327
Avg GPU Trans Time: 0.07477
Avg Forward Time: 0.67278
Epoch Time: 0:01:43.046211
Avg Data Time: 0.18090
Avg GPU Trans Time: 0.07870
Avg Forward Time: 0.62114
Epoch Time: 0:01:43.058168
Avg Data Time: 0.15915
Avg GPU Trans Time: 0.06692
Avg Forward Time: 0.65477
[train: 25, 50 / 117] FPS: 63.9 (81.8)  ,  DataTime: 0.194 (0.084)  ,  ForwardTime: 0.724  ,  TotalTime: 1.002  ,  Loss/total: 0.72520  ,  Loss/giou: 0.17591  ,  Loss/l1: 0.01632  ,  Loss/location: 0.29177  ,  IoU: 0.83798
[train: 25, 50 / 117] FPS: 63.9 (81.8)  ,  DataTime: 0.221 (0.070)  ,  ForwardTime: 0.712  ,  TotalTime: 1.002  ,  Loss/total: 0.71098  ,  Loss/giou: 0.17129  ,  Loss/l1: 0.01514  ,  Loss/location: 0.29268  ,  IoU: 0.84023
[train: 25, 50 / 117] FPS: 63.9 (81.6)  ,  DataTime: 0.164 (0.079)  ,  ForwardTime: 0.759  ,  TotalTime: 1.002  ,  Loss/total: 0.71903  ,  Loss/giou: 0.17590  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28992  ,  IoU: 0.83617
[train: 25, 50 / 117] FPS: 63.9 (81.3)  ,  DataTime: 0.188 (0.084)  ,  ForwardTime: 0.730  ,  TotalTime: 1.002  ,  Loss/total: 0.71270  ,  Loss/giou: 0.17194  ,  Loss/l1: 0.01571  ,  Loss/location: 0.29028  ,  IoU: 0.84026
[train: 25, 50 / 117] FPS: 63.9 (81.6)  ,  DataTime: 0.236 (0.080)  ,  ForwardTime: 0.685  ,  TotalTime: 1.002  ,  Loss/total: 0.71430  ,  Loss/giou: 0.17446  ,  Loss/l1: 0.01580  ,  Loss/location: 0.28637  ,  IoU: 0.83807
[train: 25, 50 / 117] FPS: 63.8 (81.8)  ,  DataTime: 0.210 (0.081)  ,  ForwardTime: 0.712  ,  TotalTime: 1.002  ,  Loss/total: 0.69941  ,  Loss/giou: 0.17156  ,  Loss/l1: 0.01568  ,  Loss/location: 0.27786  ,  IoU: 0.84148
[train: 25, 50 / 117] FPS: 63.8 (81.5)  ,  DataTime: 0.234 (0.081)  ,  ForwardTime: 0.688  ,  TotalTime: 1.003  ,  Loss/total: 0.70681  ,  Loss/giou: 0.17204  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28575  ,  IoU: 0.84017
[train: 25, 50 / 117] FPS: 63.9 (81.2)  ,  DataTime: 0.186 (0.069)  ,  ForwardTime: 0.747  ,  TotalTime: 1.001  ,  Loss/total: 0.71721  ,  Loss/giou: 0.17440  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28961  ,  IoU: 0.83779
[train: 25, 100 / 117] FPS: 72.4 (97.7)  ,  DataTime: 0.113 (0.079)  ,  ForwardTime: 0.692  ,  TotalTime: 0.884  ,  Loss/total: 0.71775  ,  Loss/giou: 0.17538  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28766  ,  IoU: 0.83741
[train: 25, 100 / 117] FPS: 72.4 (97.4)  ,  DataTime: 0.126 (0.081)  ,  ForwardTime: 0.676  ,  TotalTime: 0.884  ,  Loss/total: 0.71177  ,  Loss/giou: 0.17196  ,  Loss/l1: 0.01556  ,  Loss/location: 0.29002  ,  IoU: 0.83999
[train: 25, 100 / 117] FPS: 72.4 (97.4)  ,  DataTime: 0.127 (0.082)  ,  ForwardTime: 0.676  ,  TotalTime: 0.884  ,  Loss/total: 0.71083  ,  Loss/giou: 0.17368  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28352  ,  IoU: 0.83936
[train: 25, 100 / 117] FPS: 72.4 (97.4)  ,  DataTime: 0.146 (0.078)  ,  ForwardTime: 0.661  ,  TotalTime: 0.884  ,  Loss/total: 0.71440  ,  Loss/giou: 0.17252  ,  Loss/l1: 0.01558  ,  Loss/location: 0.29148  ,  IoU: 0.83964
[train: 25, 100 / 117] FPS: 72.4 (97.4)  ,  DataTime: 0.135 (0.078)  ,  ForwardTime: 0.671  ,  TotalTime: 0.884  ,  Loss/total: 0.70058  ,  Loss/giou: 0.17117  ,  Loss/l1: 0.01550  ,  Loss/location: 0.28075  ,  IoU: 0.84102[train: 25, 100 / 117] FPS: 72.4 (97.7)  ,  DataTime: 0.140 (0.068)  ,  ForwardTime: 0.676  ,  TotalTime: 0.884  ,  Loss/total: 0.70857  ,  Loss/giou: 0.17226  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28762  ,  IoU: 0.83943

[train: 25, 100 / 117] FPS: 72.3 (97.6)  ,  DataTime: 0.146 (0.080)  ,  ForwardTime: 0.658  ,  TotalTime: 0.885  ,  Loss/total: 0.70005  ,  Loss/giou: 0.17085  ,  Loss/l1: 0.01519  ,  Loss/location: 0.28238  ,  IoU: 0.84097
[train: 25, 100 / 117] FPS: 72.4 (97.4)  ,  DataTime: 0.122 (0.067)  ,  ForwardTime: 0.696  ,  TotalTime: 0.884  ,  Loss/total: 0.70784  ,  Loss/giou: 0.17135  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28893  ,  IoU: 0.83988
[train: 25, 117 / 117] FPS: 76.1 (108.9)  ,  DataTime: 0.114 (0.076)  ,  ForwardTime: 0.651  ,  TotalTime: 0.841  ,  Loss/total: 0.70914  ,  Loss/giou: 0.17212  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28752  ,  IoU: 0.83972
[train: 25, 117 / 117] FPS: 76.1 (108.8)  ,  DataTime: 0.129 (0.073)  ,  ForwardTime: 0.639  ,  TotalTime: 0.841  ,  Loss/total: 0.72359  ,  Loss/giou: 0.17408  ,  Loss/l1: 0.01581  ,  Loss/location: 0.29639  ,  IoU: 0.83861[train: 25, 117 / 117] FPS: 76.0 (109.1)  ,  DataTime: 0.130 (0.075)  ,  ForwardTime: 0.637  ,  TotalTime: 0.842  ,  Loss/total: 0.69959  ,  Loss/giou: 0.17046  ,  Loss/l1: 0.01510  ,  Loss/location: 0.28316  ,  IoU: 0.84109

[train: 25, 117 / 117] FPS: 76.0 (108.8)  ,  DataTime: 0.102 (0.074)  ,  ForwardTime: 0.665  ,  TotalTime: 0.842  ,  Loss/total: 0.71120  ,  Loss/giou: 0.17329  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28715  ,  IoU: 0.83877
[train: 25, 117 / 117] FPS: 76.1 (108.9)  ,  DataTime: 0.110 (0.063)  ,  ForwardTime: 0.668  ,  TotalTime: 0.841  ,  Loss/total: 0.71117  ,  Loss/giou: 0.17131  ,  Loss/l1: 0.01526  ,  Loss/location: 0.29223  ,  IoU: 0.83994[train: 25, 117 / 117] FPS: 76.0 (108.8)  ,  DataTime: 0.121 (0.074)  ,  ForwardTime: 0.647  ,  TotalTime: 0.842  ,  Loss/total: 0.69982  ,  Loss/giou: 0.17078  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28172  ,  IoU: 0.84111

[train: 25, 117 / 117] FPS: 76.1 (108.8)  ,  DataTime: 0.114 (0.077)  ,  ForwardTime: 0.651  ,  TotalTime: 0.841  ,  Loss/total: 0.70612  ,  Loss/giou: 0.17265  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28166  ,  IoU: 0.84026
[train: 25, 117 / 117] FPS: 76.1 (108.4)  ,  DataTime: 0.126 (0.064)  ,  ForwardTime: 0.652  ,  TotalTime: 0.841  ,  Loss/total: 0.71395  ,  Loss/giou: 0.17368  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28858  ,  IoU: 0.83862
Epoch Time: 0:01:38.440982
Avg Data Time: 0.12933
Avg GPU Trans Time: 0.07324
Avg Forward Time: 0.63880
Epoch Time: 0:01:38.454941
Avg Data Time: 0.11394
Avg GPU Trans Time: 0.07660
Avg Forward Time: 0.65095
Epoch Time: 0:01:38.500509
Avg Data Time: 0.12962
Avg GPU Trans Time: 0.07529
Avg Forward Time: 0.63698
Epoch Time: 0:01:38.472317
Avg Data Time: 0.10209
Avg GPU Trans Time: 0.07416
Avg Forward Time: 0.66539
Epoch Time: 0:01:38.482656
Avg Data Time: 0.12122
Avg GPU Trans Time: 0.07363
Avg Forward Time: 0.64688
Epoch Time: 0:01:38.447731
Avg Data Time: 0.12563
Avg GPU Trans Time: 0.06367
Avg Forward Time: 0.65213
Epoch Time: 0:01:38.442372
Avg Data Time: 0.11421
Avg GPU Trans Time: 0.07606
Avg Forward Time: 0.65112
Epoch Time: 0:01:38.430309
Avg Data Time: 0.11017
Avg GPU Trans Time: 0.06308
Avg Forward Time: 0.66803
[val: 25, 50 / 78] FPS: 14.7 (198.2)  ,  DataTime: 3.999 (0.049)  ,  ForwardTime: 0.314  ,  TotalTime: 4.362  ,  Loss/total: 0.81003  ,  Loss/giou: 0.19637  ,  Loss/l1: 0.02051  ,  Loss/location: 0.31473  ,  IoU: 0.82422
[val: 25, 50 / 78] FPS: 14.4 (196.6)  ,  DataTime: 4.067 (0.052)  ,  ForwardTime: 0.321  ,  TotalTime: 4.440  ,  Loss/total: 0.85349  ,  Loss/giou: 0.20298  ,  Loss/l1: 0.02179  ,  Loss/location: 0.33858  ,  IoU: 0.81902
[val: 25, 50 / 78] FPS: 14.4 (203.8)  ,  DataTime: 3.865 (0.048)  ,  ForwardTime: 0.543  ,  TotalTime: 4.456  ,  Loss/total: 0.82639  ,  Loss/giou: 0.20182  ,  Loss/l1: 0.02136  ,  Loss/location: 0.31593  ,  IoU: 0.82028
[val: 25, 50 / 78] FPS: 14.3 (196.1)  ,  DataTime: 4.181 (0.053)  ,  ForwardTime: 0.232  ,  TotalTime: 4.466  ,  Loss/total: 0.83453  ,  Loss/giou: 0.20097  ,  Loss/l1: 0.02110  ,  Loss/location: 0.32710  ,  IoU: 0.82068
[val: 25, 50 / 78] FPS: 14.3 (187.7)  ,  DataTime: 4.113 (0.051)  ,  ForwardTime: 0.313  ,  TotalTime: 4.477  ,  Loss/total: 0.84094  ,  Loss/giou: 0.20144  ,  Loss/l1: 0.02130  ,  Loss/location: 0.33159  ,  IoU: 0.82080
[val: 25, 50 / 78] FPS: 14.2 (203.1)  ,  DataTime: 4.215 (0.053)  ,  ForwardTime: 0.233  ,  TotalTime: 4.501  ,  Loss/total: 0.85621  ,  Loss/giou: 0.20467  ,  Loss/l1: 0.02168  ,  Loss/location: 0.33849  ,  IoU: 0.81764
[val: 25, 50 / 78] FPS: 14.0 (190.7)  ,  DataTime: 4.297 (0.051)  ,  ForwardTime: 0.228  ,  TotalTime: 4.576  ,  Loss/total: 0.83265  ,  Loss/giou: 0.20071  ,  Loss/l1: 0.02159  ,  Loss/location: 0.32327  ,  IoU: 0.82146
[val: 25, 50 / 78] FPS: 13.8 (206.0)  ,  DataTime: 4.085 (0.047)  ,  ForwardTime: 0.501  ,  TotalTime: 4.633  ,  Loss/total: 0.82412  ,  Loss/giou: 0.19914  ,  Loss/l1: 0.02067  ,  Loss/location: 0.32249  ,  IoU: 0.82180
[val: 25, 78 / 78] FPS: 17.2 (198.3)  ,  DataTime: 3.427 (0.053)  ,  ForwardTime: 0.233  ,  TotalTime: 3.713  ,  Loss/total: 0.83512  ,  Loss/giou: 0.20051  ,  Loss/l1: 0.02103  ,  Loss/location: 0.32896  ,  IoU: 0.82129
Epoch Time: 0:04:49.634876
Avg Data Time: 3.42715
Avg GPU Trans Time: 0.05340
Avg Forward Time: 0.23272
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 25, 78 / 78] FPS: 17.2 (200.3)  ,  DataTime: 3.446 (0.054)  ,  ForwardTime: 0.231  ,  TotalTime: 3.732  ,  Loss/total: 0.81921  ,  Loss/giou: 0.19704  ,  Loss/l1: 0.02064  ,  Loss/location: 0.32192  ,  IoU: 0.82382
Epoch Time: 0:04:51.069870
Avg Data Time: 3.44632
Avg GPU Trans Time: 0.05387
Avg Forward Time: 0.23147
[val: 25, 78 / 78] FPS: 17.0 (185.5)  ,  DataTime: 3.278 (0.046)  ,  ForwardTime: 0.432  ,  TotalTime: 3.756  ,  Loss/total: 0.82894  ,  Loss/giou: 0.20145  ,  Loss/l1: 0.02124  ,  Loss/location: 0.31981  ,  IoU: 0.82006
[val: 25, 78 / 78] FPS: 17.0 (224.3)  ,  DataTime: 3.425 (0.049)  ,  ForwardTime: 0.283  ,  TotalTime: 3.757  ,  Loss/total: 0.81426  ,  Loss/giou: 0.19595  ,  Loss/l1: 0.02033  ,  Loss/location: 0.32071  ,  IoU: 0.82431
Epoch Time: 0:04:52.951832
Avg Data Time: 3.27803
Avg GPU Trans Time: 0.04610
Avg Forward Time: 0.43166
Epoch Time: 0:04:53.065035
Avg Data Time: 3.42532
Avg GPU Trans Time: 0.04890
Avg Forward Time: 0.28302
[val: 25, 78 / 78] FPS: 17.0 (177.4)  ,  DataTime: 3.437 (0.052)  ,  ForwardTime: 0.284  ,  TotalTime: 3.773  ,  Loss/total: 0.82442  ,  Loss/giou: 0.19864  ,  Loss/l1: 0.02094  ,  Loss/location: 0.32245  ,  IoU: 0.82265
Epoch Time: 0:04:54.309320
Avg Data Time: 3.43675
Avg GPU Trans Time: 0.05204
Avg Forward Time: 0.28440
[val: 25, 78 / 78] FPS: 16.9 (197.9)  ,  DataTime: 3.334 (0.046)  ,  ForwardTime: 0.405  ,  TotalTime: 3.785  ,  Loss/total: 0.85118  ,  Loss/giou: 0.20416  ,  Loss/l1: 0.02146  ,  Loss/location: 0.33558  ,  IoU: 0.81761
[val: 25, 78 / 78] FPS: 16.9 (179.2)  ,  DataTime: 3.444 (0.052)  ,  ForwardTime: 0.290  ,  TotalTime: 3.786  ,  Loss/total: 0.84480  ,  Loss/giou: 0.20182  ,  Loss/l1: 0.02166  ,  Loss/location: 0.33287  ,  IoU: 0.82023
[val: 25, 78 / 78] FPS: 16.9 (20.8)  ,  DataTime: 3.509 (0.050)  ,  ForwardTime: 0.229  ,  TotalTime: 3.789  ,  Loss/total: 0.84512  ,  Loss/giou: 0.20415  ,  Loss/l1: 0.02192  ,  Loss/location: 0.32722  ,  IoU: 0.81813
Epoch Time: 0:04:55.236627
Avg Data Time: 3.33389
Avg GPU Trans Time: 0.04593
Avg Forward Time: 0.40527
Epoch Time: 0:04:55.296302
Avg Data Time: 3.44397
Avg GPU Trans Time: 0.05204
Avg Forward Time: 0.28985
Epoch Time: 0:04:55.514237
Avg Data Time: 3.50927
Avg GPU Trans Time: 0.05012
Avg Forward Time: 0.22926
[train: 26, 50 / 117] FPS: 67.2 (84.0)  ,  DataTime: 0.198 (0.080)  ,  ForwardTime: 0.675  ,  TotalTime: 0.952  ,  Loss/total: 0.72429  ,  Loss/giou: 0.17264  ,  Loss/l1: 0.01613  ,  Loss/location: 0.29834  ,  IoU: 0.84091
[train: 26, 50 / 117] FPS: 60.7 (83.5)  ,  DataTime: 0.159 (0.081)  ,  ForwardTime: 0.814  ,  TotalTime: 1.054  ,  Loss/total: 0.71212  ,  Loss/giou: 0.17234  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28941  ,  IoU: 0.84019
[train: 26, 50 / 117] FPS: 63.9 (83.8)  ,  DataTime: 0.172 (0.084)  ,  ForwardTime: 0.747  ,  TotalTime: 1.002  ,  Loss/total: 0.71419  ,  Loss/giou: 0.17447  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28509  ,  IoU: 0.83828
[train: 26, 50 / 117] FPS: 63.7 (83.6)  ,  DataTime: 0.174 (0.083)  ,  ForwardTime: 0.747  ,  TotalTime: 1.004  ,  Loss/total: 0.70430  ,  Loss/giou: 0.17110  ,  Loss/l1: 0.01551  ,  Loss/location: 0.28455  ,  IoU: 0.84038
[train: 26, 50 / 117] FPS: 65.6 (83.3)  ,  DataTime: 0.179 (0.070)  ,  ForwardTime: 0.726  ,  TotalTime: 0.976  ,  Loss/total: 0.69642  ,  Loss/giou: 0.16754  ,  Loss/l1: 0.01491  ,  Loss/location: 0.28681  ,  IoU: 0.84356[train: 26, 50 / 117] FPS: 67.0 (83.6)  ,  DataTime: 0.216 (0.083)  ,  ForwardTime: 0.656  ,  TotalTime: 0.955  ,  Loss/total: 0.73399  ,  Loss/giou: 0.17747  ,  Loss/l1: 0.01604  ,  Loss/location: 0.29886  ,  IoU: 0.83544

[train: 26, 50 / 117] FPS: 61.5 (83.9)  ,  DataTime: 0.159 (0.079)  ,  ForwardTime: 0.803  ,  TotalTime: 1.041  ,  Loss/total: 0.72757  ,  Loss/giou: 0.17756  ,  Loss/l1: 0.01668  ,  Loss/location: 0.28903  ,  IoU: 0.83587
[train: 26, 50 / 117] FPS: 66.9 (83.5)  ,  DataTime: 0.221 (0.074)  ,  ForwardTime: 0.661  ,  TotalTime: 0.956  ,  Loss/total: 0.70449  ,  Loss/giou: 0.17054  ,  Loss/l1: 0.01585  ,  Loss/location: 0.28416  ,  IoU: 0.84248
[train: 26, 100 / 117] FPS: 74.6 (101.0)  ,  DataTime: 0.138 (0.083)  ,  ForwardTime: 0.638  ,  TotalTime: 0.858  ,  Loss/total: 0.72339  ,  Loss/giou: 0.17619  ,  Loss/l1: 0.01620  ,  Loss/location: 0.29000  ,  IoU: 0.83765
[train: 26, 100 / 117] FPS: 71.0 (100.9)  ,  DataTime: 0.109 (0.082)  ,  ForwardTime: 0.711  ,  TotalTime: 0.902  ,  Loss/total: 0.71145  ,  Loss/giou: 0.17243  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28811  ,  IoU: 0.83958
[train: 26, 100 / 117] FPS: 74.5 (100.7)  ,  DataTime: 0.137 (0.068)  ,  ForwardTime: 0.655  ,  TotalTime: 0.859  ,  Loss/total: 0.69326  ,  Loss/giou: 0.16914  ,  Loss/l1: 0.01562  ,  Loss/location: 0.27687  ,  IoU: 0.84352
[train: 26, 100 / 117] FPS: 74.7 (100.7)  ,  DataTime: 0.126 (0.081)  ,  ForwardTime: 0.649  ,  TotalTime: 0.857  ,  Loss/total: 0.71281  ,  Loss/giou: 0.17197  ,  Loss/l1: 0.01573  ,  Loss/location: 0.29022  ,  IoU: 0.84054
[train: 26, 100 / 117] FPS: 72.5 (101.0)  ,  DataTime: 0.116 (0.080)  ,  ForwardTime: 0.688  ,  TotalTime: 0.883  ,  Loss/total: 0.69423  ,  Loss/giou: 0.16998  ,  Loss/l1: 0.01507  ,  Loss/location: 0.27890  ,  IoU: 0.84103
[train: 26, 100 / 117] FPS: 70.5 (100.6)  ,  DataTime: 0.108 (0.081)  ,  ForwardTime: 0.719  ,  TotalTime: 0.908  ,  Loss/total: 0.71119  ,  Loss/giou: 0.17245  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28811  ,  IoU: 0.84033
[train: 26, 100 / 117] FPS: 73.6 (100.5)  ,  DataTime: 0.116 (0.066)  ,  ForwardTime: 0.687  ,  TotalTime: 0.869  ,  Loss/total: 0.71289  ,  Loss/giou: 0.17169  ,  Loss/l1: 0.01567  ,  Loss/location: 0.29116  ,  IoU: 0.84047
[train: 26, 100 / 117] FPS: 72.6 (100.5)  ,  DataTime: 0.115 (0.080)  ,  ForwardTime: 0.687  ,  TotalTime: 0.882  ,  Loss/total: 0.71134  ,  Loss/giou: 0.17459  ,  Loss/l1: 0.01614  ,  Loss/location: 0.28144  ,  IoU: 0.83829
[train: 26, 117 / 117] FPS: 78.2 (105.9)  ,  DataTime: 0.113 (0.077)  ,  ForwardTime: 0.629  ,  TotalTime: 0.819  ,  Loss/total: 0.71005  ,  Loss/giou: 0.17135  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28944  ,  IoU: 0.84103
[train: 26, 117 / 117] FPS: 77.2 (105.9)  ,  DataTime: 0.105 (0.063)  ,  ForwardTime: 0.661  ,  TotalTime: 0.829  ,  Loss/total: 0.70606  ,  Loss/giou: 0.17058  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28782  ,  IoU: 0.84121
[train: 26, 117 / 117] FPS: 78.0 (106.0)  ,  DataTime: 0.122 (0.063)  ,  ForwardTime: 0.635  ,  TotalTime: 0.820  ,  Loss/total: 0.70795  ,  Loss/giou: 0.17229  ,  Loss/l1: 0.01600  ,  Loss/location: 0.28335  ,  IoU: 0.84094
[train: 26, 117 / 117] FPS: 76.1 (105.9)  ,  DataTime: 0.104 (0.075)  ,  ForwardTime: 0.662  ,  TotalTime: 0.841  ,  Loss/total: 0.69483  ,  Loss/giou: 0.17006  ,  Loss/l1: 0.01506  ,  Loss/location: 0.27941  ,  IoU: 0.84104
[train: 26, 117 / 117] FPS: 78.1 (105.8)  ,  DataTime: 0.123 (0.078)  ,  ForwardTime: 0.619  ,  TotalTime: 0.820  ,  Loss/total: 0.72943  ,  Loss/giou: 0.17794  ,  Loss/l1: 0.01657  ,  Loss/location: 0.29067  ,  IoU: 0.83651
[train: 26, 117 / 117] FPS: 76.2 (105.7)  ,  DataTime: 0.104 (0.075)  ,  ForwardTime: 0.661  ,  TotalTime: 0.840  ,  Loss/total: 0.70625  ,  Loss/giou: 0.17333  ,  Loss/l1: 0.01598  ,  Loss/location: 0.27968  ,  IoU: 0.83928
[train: 26, 117 / 117] FPS: 74.7 (105.7)  ,  DataTime: 0.099 (0.077)  ,  ForwardTime: 0.681  ,  TotalTime: 0.857  ,  Loss/total: 0.70810  ,  Loss/giou: 0.17205  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28587  ,  IoU: 0.83995
[train: 26, 117 / 117] FPS: 74.2 (105.7)  ,  DataTime: 0.098 (0.076)  ,  ForwardTime: 0.688  ,  TotalTime: 0.862  ,  Loss/total: 0.70996  ,  Loss/giou: 0.17202  ,  Loss/l1: 0.01554  ,  Loss/location: 0.28820  ,  IoU: 0.84055
Epoch Time: 0:01:40.231481
Avg Data Time: 0.09864
Avg GPU Trans Time: 0.07735
Avg Forward Time: 0.68069
Epoch Time: 0:01:40.855028
Avg Data Time: 0.09791
Avg GPU Trans Time: 0.07605
Avg Forward Time: 0.68805
Epoch Time: 0:01:35.892614
Avg Data Time: 0.12309
Avg GPU Trans Time: 0.07787
Avg Forward Time: 0.61864
Epoch Time: 0:01:35.776609
Avg Data Time: 0.11327
Avg GPU Trans Time: 0.07664
Avg Forward Time: 0.62870
Epoch Time: 0:01:36.962473
Avg Data Time: 0.10466
Avg GPU Trans Time: 0.06303
Avg Forward Time: 0.66106
Epoch Time: 0:01:35.973912
Avg Data Time: 0.12168
Avg GPU Trans Time: 0.06336
Avg Forward Time: 0.63525
Epoch Time: 0:01:38.370409
Avg Data Time: 0.10412
Avg GPU Trans Time: 0.07508
Avg Forward Time: 0.66157
Epoch Time: 0:01:38.269240
Avg Data Time: 0.10378
Avg GPU Trans Time: 0.07492
Avg Forward Time: 0.66121
[train: 27, 50 / 117] FPS: 63.8 (79.3)  ,  DataTime: 0.266 (0.086)  ,  ForwardTime: 0.651  ,  TotalTime: 1.003  ,  Loss/total: 0.72236  ,  Loss/giou: 0.17324  ,  Loss/l1: 0.01576  ,  Loss/location: 0.29708  ,  IoU: 0.83918
[train: 27, 50 / 117] FPS: 63.8 (79.5)  ,  DataTime: 0.202 (0.086)  ,  ForwardTime: 0.714  ,  TotalTime: 1.003  ,  Loss/total: 0.69797  ,  Loss/giou: 0.16915  ,  Loss/l1: 0.01514  ,  Loss/location: 0.28397  ,  IoU: 0.84276
[train: 27, 50 / 117] FPS: 63.8 (79.1)  ,  DataTime: 0.177 (0.086)  ,  ForwardTime: 0.741  ,  TotalTime: 1.003  ,  Loss/total: 0.70071  ,  Loss/giou: 0.17225  ,  Loss/l1: 0.01564  ,  Loss/location: 0.27799  ,  IoU: 0.84029
[train: 27, 50 / 117] FPS: 63.8 (79.0)  ,  DataTime: 0.209 (0.082)  ,  ForwardTime: 0.712  ,  TotalTime: 1.004  ,  Loss/total: 0.72371  ,  Loss/giou: 0.17716  ,  Loss/l1: 0.01651  ,  Loss/location: 0.28685  ,  IoU: 0.83712
[train: 27, 50 / 117] FPS: 63.8 (79.0)  ,  DataTime: 0.189 (0.086)  ,  ForwardTime: 0.729  ,  TotalTime: 1.003  ,  Loss/total: 0.70215  ,  Loss/giou: 0.16864  ,  Loss/l1: 0.01484  ,  Loss/location: 0.29069  ,  IoU: 0.84199
[train: 27, 50 / 117] FPS: 63.8 (79.0)  ,  DataTime: 0.187 (0.068)  ,  ForwardTime: 0.748  ,  TotalTime: 1.003  ,  Loss/total: 0.70057  ,  Loss/giou: 0.17361  ,  Loss/l1: 0.01611  ,  Loss/location: 0.27283  ,  IoU: 0.84040
[train: 27, 50 / 117] FPS: 63.8 (78.9)  ,  DataTime: 0.179 (0.080)  ,  ForwardTime: 0.744  ,  TotalTime: 1.003  ,  Loss/total: 0.70752  ,  Loss/giou: 0.17323  ,  Loss/l1: 0.01594  ,  Loss/location: 0.28138  ,  IoU: 0.83969
[train: 27, 50 / 117] FPS: 63.8 (78.6)  ,  DataTime: 0.208 (0.069)  ,  ForwardTime: 0.726  ,  TotalTime: 1.003  ,  Loss/total: 0.71573  ,  Loss/giou: 0.17288  ,  Loss/l1: 0.01570  ,  Loss/location: 0.29150  ,  IoU: 0.83919
[train: 27, 100 / 117] FPS: 72.8 (104.6)  ,  DataTime: 0.116 (0.084)  ,  ForwardTime: 0.678  ,  TotalTime: 0.879  ,  Loss/total: 0.69280  ,  Loss/giou: 0.16984  ,  Loss/l1: 0.01535  ,  Loss/location: 0.27634  ,  IoU: 0.84219[train: 27, 100 / 117] FPS: 72.8 (104.7)  ,  DataTime: 0.133 (0.082)  ,  ForwardTime: 0.665  ,  TotalTime: 0.879  ,  Loss/total: 0.72340  ,  Loss/giou: 0.17564  ,  Loss/l1: 0.01621  ,  Loss/location: 0.29105  ,  IoU: 0.83793

[train: 27, 100 / 117] FPS: 72.8 (104.6)  ,  DataTime: 0.159 (0.084)  ,  ForwardTime: 0.636  ,  TotalTime: 0.879  ,  Loss/total: 0.72456  ,  Loss/giou: 0.17414  ,  Loss/l1: 0.01595  ,  Loss/location: 0.29650  ,  IoU: 0.83866
[train: 27, 100 / 117] FPS: 72.8 (104.8)  ,  DataTime: 0.120 (0.084)  ,  ForwardTime: 0.675  ,  TotalTime: 0.879  ,  Loss/total: 0.71843  ,  Loss/giou: 0.17472  ,  Loss/l1: 0.01585  ,  Loss/location: 0.28975  ,  IoU: 0.83790
[train: 27, 100 / 117] FPS: 72.8 (104.9)  ,  DataTime: 0.129 (0.068)  ,  ForwardTime: 0.682  ,  TotalTime: 0.879  ,  Loss/total: 0.70819  ,  Loss/giou: 0.17156  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28817  ,  IoU: 0.84021[train: 27, 100 / 117] FPS: 72.8 (104.9)  ,  DataTime: 0.120 (0.067)  ,  ForwardTime: 0.693  ,  TotalTime: 0.879  ,  Loss/total: 0.70343  ,  Loss/giou: 0.17207  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28071  ,  IoU: 0.84105

[train: 27, 100 / 117] FPS: 72.8 (104.9)  ,  DataTime: 0.127 (0.085)  ,  ForwardTime: 0.667  ,  TotalTime: 0.879  ,  Loss/total: 0.70418  ,  Loss/giou: 0.17046  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28716  ,  IoU: 0.84113
[train: 27, 100 / 117] FPS: 72.8 (104.8)  ,  DataTime: 0.115 (0.079)  ,  ForwardTime: 0.685  ,  TotalTime: 0.879  ,  Loss/total: 0.70966  ,  Loss/giou: 0.17282  ,  Loss/l1: 0.01579  ,  Loss/location: 0.28507  ,  IoU: 0.83970
[train: 27, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.119 (0.077)  ,  ForwardTime: 0.640  ,  TotalTime: 0.836  ,  Loss/total: 0.71668  ,  Loss/giou: 0.17369  ,  Loss/l1: 0.01590  ,  Loss/location: 0.28980  ,  IoU: 0.83941
[train: 27, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.141 (0.079)  ,  ForwardTime: 0.617  ,  TotalTime: 0.836  ,  Loss/total: 0.72291  ,  Loss/giou: 0.17455  ,  Loss/l1: 0.01604  ,  Loss/location: 0.29362  ,  IoU: 0.83850[train: 27, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.104 (0.079)  ,  ForwardTime: 0.653  ,  TotalTime: 0.836  ,  Loss/total: 0.69435  ,  Loss/giou: 0.17042  ,  Loss/l1: 0.01543  ,  Loss/location: 0.27635  ,  IoU: 0.84186

[train: 27, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.113 (0.080)  ,  ForwardTime: 0.642  ,  TotalTime: 0.836  ,  Loss/total: 0.70934  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28923  ,  IoU: 0.84023[train: 27, 117 / 117] FPS: 76.6 (110.0)  ,  DataTime: 0.107 (0.063)  ,  ForwardTime: 0.666  ,  TotalTime: 0.836  ,  Loss/total: 0.70516  ,  Loss/giou: 0.17205  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28276  ,  IoU: 0.84097
[train: 27, 117 / 117] FPS: 76.6 (110.0)  ,  DataTime: 0.103 (0.075)  ,  ForwardTime: 0.658  ,  TotalTime: 0.836  ,  Loss/total: 0.70851  ,  Loss/giou: 0.17319  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28331  ,  IoU: 0.83943

[train: 27, 117 / 117] FPS: 76.5 (110.1)  ,  DataTime: 0.108 (0.079)  ,  ForwardTime: 0.649  ,  TotalTime: 0.836  ,  Loss/total: 0.71534  ,  Loss/giou: 0.17364  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28978  ,  IoU: 0.83880
[train: 27, 117 / 117] FPS: 76.6 (110.0)  ,  DataTime: 0.115 (0.064)  ,  ForwardTime: 0.656  ,  TotalTime: 0.836  ,  Loss/total: 0.71297  ,  Loss/giou: 0.17225  ,  Loss/l1: 0.01557  ,  Loss/location: 0.29059  ,  IoU: 0.83979
Epoch Time: 0:01:37.833507
Avg Data Time: 0.14091
Avg GPU Trans Time: 0.07854
Avg Forward Time: 0.61673
Epoch Time: 0:01:37.826224
Avg Data Time: 0.10780
Avg GPU Trans Time: 0.07886
Avg Forward Time: 0.64946
Epoch Time: 0:01:37.834670
Avg Data Time: 0.11927
Avg GPU Trans Time: 0.07673
Avg Forward Time: 0.64019
Epoch Time: 0:01:37.817260
Avg Data Time: 0.11545
Avg GPU Trans Time: 0.06426
Avg Forward Time: 0.65633
Epoch Time: 0:01:37.830521
Avg Data Time: 0.10449
Avg GPU Trans Time: 0.07890
Avg Forward Time: 0.65276
Epoch Time: 0:01:37.815420
Avg Data Time: 0.10735
Avg GPU Trans Time: 0.06297
Avg Forward Time: 0.66571
Epoch Time: 0:01:37.820756
Avg Data Time: 0.11334
Avg GPU Trans Time: 0.08027
Avg Forward Time: 0.64247
Epoch Time: 0:01:37.809507
Avg Data Time: 0.10302
Avg GPU Trans Time: 0.07468
Avg Forward Time: 0.65828
[train: 28, 50 / 117] FPS: 64.5 (84.8)  ,  DataTime: 0.217 (0.087)  ,  ForwardTime: 0.688  ,  TotalTime: 0.993  ,  Loss/total: 0.73786  ,  Loss/giou: 0.17814  ,  Loss/l1: 0.01619  ,  Loss/location: 0.30065  ,  IoU: 0.83501
[train: 28, 50 / 117] FPS: 64.5 (84.9)  ,  DataTime: 0.204 (0.087)  ,  ForwardTime: 0.701  ,  TotalTime: 0.992  ,  Loss/total: 0.69242  ,  Loss/giou: 0.16862  ,  Loss/l1: 0.01470  ,  Loss/location: 0.28169  ,  IoU: 0.84191
[train: 28, 50 / 117] FPS: 64.5 (84.7)  ,  DataTime: 0.192 (0.076)  ,  ForwardTime: 0.724  ,  TotalTime: 0.992  ,  Loss/total: 0.68729  ,  Loss/giou: 0.16827  ,  Loss/l1: 0.01469  ,  Loss/location: 0.27734  ,  IoU: 0.84283
[train: 28, 50 / 117] FPS: 64.5 (84.4)  ,  DataTime: 0.214 (0.094)  ,  ForwardTime: 0.684  ,  TotalTime: 0.992  ,  Loss/total: 0.69905  ,  Loss/giou: 0.17211  ,  Loss/l1: 0.01534  ,  Loss/location: 0.27812  ,  IoU: 0.83975
[train: 28, 50 / 117] FPS: 64.4 (84.5)  ,  DataTime: 0.198 (0.086)  ,  ForwardTime: 0.709  ,  TotalTime: 0.993  ,  Loss/total: 0.70287  ,  Loss/giou: 0.17145  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28153  ,  IoU: 0.84154
[train: 28, 50 / 117] FPS: 64.5 (84.6)  ,  DataTime: 0.201 (0.083)  ,  ForwardTime: 0.709  ,  TotalTime: 0.992  ,  Loss/total: 0.70269  ,  Loss/giou: 0.16950  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28752  ,  IoU: 0.84230
[train: 28, 50 / 117] FPS: 64.5 (84.1)  ,  DataTime: 0.192 (0.087)  ,  ForwardTime: 0.714  ,  TotalTime: 0.993  ,  Loss/total: 0.70462  ,  Loss/giou: 0.17040  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28691  ,  IoU: 0.84121[train: 28, 50 / 117] FPS: 64.5 (84.6)  ,  DataTime: 0.196 (0.080)  ,  ForwardTime: 0.716  ,  TotalTime: 0.993  ,  Loss/total: 0.73288  ,  Loss/giou: 0.17700  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29789  ,  IoU: 0.83606

[train: 28, 100 / 117] FPS: 73.2 (102.5)  ,  DataTime: 0.128 (0.084)  ,  ForwardTime: 0.662  ,  TotalTime: 0.874  ,  Loss/total: 0.69849  ,  Loss/giou: 0.16973  ,  Loss/l1: 0.01504  ,  Loss/location: 0.28383  ,  IoU: 0.84099
[train: 28, 100 / 117] FPS: 73.2 (102.1)  ,  DataTime: 0.127 (0.081)  ,  ForwardTime: 0.666  ,  TotalTime: 0.874  ,  Loss/total: 0.70089  ,  Loss/giou: 0.16934  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28695  ,  IoU: 0.84207
[train: 28, 100 / 117] FPS: 73.2 (102.2)  ,  DataTime: 0.125 (0.082)  ,  ForwardTime: 0.668  ,  TotalTime: 0.875  ,  Loss/total: 0.71160  ,  Loss/giou: 0.17211  ,  Loss/l1: 0.01574  ,  Loss/location: 0.28867  ,  IoU: 0.84067
[train: 28, 100 / 117] FPS: 73.2 (102.0)  ,  DataTime: 0.121 (0.070)  ,  ForwardTime: 0.682  ,  TotalTime: 0.874  ,  Loss/total: 0.69542  ,  Loss/giou: 0.17033  ,  Loss/l1: 0.01525  ,  Loss/location: 0.27851  ,  IoU: 0.84147
[train: 28, 100 / 117] FPS: 73.2 (102.0)  ,  DataTime: 0.126 (0.077)  ,  ForwardTime: 0.672  ,  TotalTime: 0.874  ,  Loss/total: 0.72190  ,  Loss/giou: 0.17450  ,  Loss/l1: 0.01577  ,  Loss/location: 0.29407  ,  IoU: 0.83774
[train: 28, 100 / 117] FPS: 73.2 (102.3)  ,  DataTime: 0.134 (0.087)  ,  ForwardTime: 0.653  ,  TotalTime: 0.874  ,  Loss/total: 0.70875  ,  Loss/giou: 0.17344  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28405  ,  IoU: 0.83918
[train: 28, 100 / 117] FPS: 73.2 (102.1)  ,  DataTime: 0.134 (0.084)  ,  ForwardTime: 0.657  ,  TotalTime: 0.874  ,  Loss/total: 0.72568  ,  Loss/giou: 0.17556  ,  Loss/l1: 0.01590  ,  Loss/location: 0.29504  ,  IoU: 0.83699
[train: 28, 100 / 117] FPS: 73.2 (101.9)  ,  DataTime: 0.123 (0.085)  ,  ForwardTime: 0.666  ,  TotalTime: 0.874  ,  Loss/total: 0.71044  ,  Loss/giou: 0.17342  ,  Loss/l1: 0.01597  ,  Loss/location: 0.28375  ,  IoU: 0.83952
[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.114 (0.078)  ,  ForwardTime: 0.639  ,  TotalTime: 0.832  ,  Loss/total: 0.69841  ,  Loss/giou: 0.16901  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28567  ,  IoU: 0.84162
[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.111 (0.079)  ,  ForwardTime: 0.642  ,  TotalTime: 0.832  ,  Loss/total: 0.71873  ,  Loss/giou: 0.17462  ,  Loss/l1: 0.01620  ,  Loss/location: 0.28848  ,  IoU: 0.83872
[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.111 (0.077)  ,  ForwardTime: 0.644  ,  TotalTime: 0.833  ,  Loss/total: 0.71000  ,  Loss/giou: 0.17188  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28812  ,  IoU: 0.84067
[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.109 (0.067)  ,  ForwardTime: 0.657  ,  TotalTime: 0.832  ,  Loss/total: 0.69544  ,  Loss/giou: 0.17007  ,  Loss/l1: 0.01522  ,  Loss/location: 0.27920  ,  IoU: 0.84164
[train: 28, 117 / 117] FPS: 76.9 (109.9)  ,  DataTime: 0.113 (0.072)  ,  ForwardTime: 0.648  ,  TotalTime: 0.832  ,  Loss/total: 0.71837  ,  Loss/giou: 0.17351  ,  Loss/l1: 0.01555  ,  Loss/location: 0.29362  ,  IoU: 0.83829[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.119 (0.082)  ,  ForwardTime: 0.631  ,  TotalTime: 0.832  ,  Loss/total: 0.71315  ,  Loss/giou: 0.17416  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28627  ,  IoU: 0.83868

[train: 28, 117 / 117] FPS: 76.9 (109.6)  ,  DataTime: 0.114 (0.076)  ,  ForwardTime: 0.643  ,  TotalTime: 0.832  ,  Loss/total: 0.70082  ,  Loss/giou: 0.16925  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28703  ,  IoU: 0.84202
[train: 28, 117 / 117] FPS: 76.9 (110.1)  ,  DataTime: 0.119 (0.079)  ,  ForwardTime: 0.635  ,  TotalTime: 0.832  ,  Loss/total: 0.72767  ,  Loss/giou: 0.17646  ,  Loss/l1: 0.01615  ,  Loss/location: 0.29400  ,  IoU: 0.83654
Epoch Time: 0:01:37.409176
Avg Data Time: 0.11147
Avg GPU Trans Time: 0.07687
Avg Forward Time: 0.64422
Epoch Time: 0:01:37.386432
Avg Data Time: 0.11056
Avg GPU Trans Time: 0.07938
Avg Forward Time: 0.64242
Epoch Time: 0:01:37.363778
Avg Data Time: 0.11433
Avg GPU Trans Time: 0.07848
Avg Forward Time: 0.63936
Epoch Time: 0:01:37.385653
Avg Data Time: 0.11898
Avg GPU Trans Time: 0.07875
Avg Forward Time: 0.63462
Epoch Time: 0:01:37.373092
Avg Data Time: 0.11269
Avg GPU Trans Time: 0.07162
Avg Forward Time: 0.64794
Epoch Time: 0:01:37.348654
Avg Data Time: 0.11937
Avg GPU Trans Time: 0.08153
Avg Forward Time: 0.63114
Epoch Time: 0:01:37.360357
Avg Data Time: 0.11389
Avg GPU Trans Time: 0.07569
Avg Forward Time: 0.64256
Epoch Time: 0:01:37.354604
Avg Data Time: 0.10853
Avg GPU Trans Time: 0.06681
Avg Forward Time: 0.65675
[train: 29, 50 / 117] FPS: 63.9 (83.1)  ,  DataTime: 0.176 (0.086)  ,  ForwardTime: 0.739  ,  TotalTime: 1.001  ,  Loss/total: 0.69282  ,  Loss/giou: 0.16840  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28244  ,  IoU: 0.84197
[train: 29, 50 / 117] FPS: 63.8 (82.9)  ,  DataTime: 0.210 (0.084)  ,  ForwardTime: 0.709  ,  TotalTime: 1.003  ,  Loss/total: 0.69378  ,  Loss/giou: 0.16627  ,  Loss/l1: 0.01461  ,  Loss/location: 0.28818  ,  IoU: 0.84389
[train: 29, 50 / 117] FPS: 63.9 (82.9)  ,  DataTime: 0.196 (0.087)  ,  ForwardTime: 0.718  ,  TotalTime: 1.001  ,  Loss/total: 0.71134  ,  Loss/giou: 0.17125  ,  Loss/l1: 0.01522  ,  Loss/location: 0.29273  ,  IoU: 0.84072
[train: 29, 50 / 117] FPS: 63.9 (82.7)  ,  DataTime: 0.191 (0.084)  ,  ForwardTime: 0.726  ,  TotalTime: 1.001  ,  Loss/total: 0.68392  ,  Loss/giou: 0.16846  ,  Loss/l1: 0.01518  ,  Loss/location: 0.27112  ,  IoU: 0.84333[train: 29, 50 / 117] FPS: 63.9 (82.7)  ,  DataTime: 0.188 (0.087)  ,  ForwardTime: 0.726  ,  TotalTime: 1.001  ,  Loss/total: 0.69498  ,  Loss/giou: 0.16766  ,  Loss/l1: 0.01476  ,  Loss/location: 0.28588  ,  IoU: 0.84361

[train: 29, 50 / 117] FPS: 63.9 (82.9)  ,  DataTime: 0.222 (0.089)  ,  ForwardTime: 0.690  ,  TotalTime: 1.001  ,  Loss/total: 0.68434  ,  Loss/giou: 0.16741  ,  Loss/l1: 0.01488  ,  Loss/location: 0.27510  ,  IoU: 0.84280
[train: 29, 50 / 117] FPS: 63.9 (82.9)  ,  DataTime: 0.191 (0.083)  ,  ForwardTime: 0.727  ,  TotalTime: 1.001  ,  Loss/total: 0.71242  ,  Loss/giou: 0.17412  ,  Loss/l1: 0.01589  ,  Loss/location: 0.28473  ,  IoU: 0.83843
[train: 29, 50 / 117] FPS: 63.9 (82.5)  ,  DataTime: 0.191 (0.080)  ,  ForwardTime: 0.730  ,  TotalTime: 1.001  ,  Loss/total: 0.71066  ,  Loss/giou: 0.17492  ,  Loss/l1: 0.01610  ,  Loss/location: 0.28033  ,  IoU: 0.83881
[train: 29, 100 / 117] FPS: 72.9 (104.6)  ,  DataTime: 0.120 (0.075)  ,  ForwardTime: 0.683  ,  TotalTime: 0.878  ,  Loss/total: 0.70772  ,  Loss/giou: 0.17327  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28269  ,  IoU: 0.83937
[train: 29, 100 / 117] FPS: 72.9 (104.5)  ,  DataTime: 0.124 (0.082)  ,  ForwardTime: 0.672  ,  TotalTime: 0.878  ,  Loss/total: 0.72066  ,  Loss/giou: 0.17404  ,  Loss/l1: 0.01564  ,  Loss/location: 0.29438  ,  IoU: 0.83849[train: 29, 100 / 117] FPS: 72.9 (104.5)  ,  DataTime: 0.138 (0.084)  ,  ForwardTime: 0.656  ,  TotalTime: 0.878  ,  Loss/total: 0.71354  ,  Loss/giou: 0.17234  ,  Loss/l1: 0.01553  ,  Loss/location: 0.29120  ,  IoU: 0.83925

[train: 29, 100 / 117] FPS: 72.9 (104.5)  ,  DataTime: 0.121 (0.081)  ,  ForwardTime: 0.676  ,  TotalTime: 0.878  ,  Loss/total: 0.69750  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01546  ,  Loss/location: 0.27800  ,  IoU: 0.84080
[train: 29, 100 / 117] FPS: 72.8 (104.5)  ,  DataTime: 0.132 (0.081)  ,  ForwardTime: 0.666  ,  TotalTime: 0.879  ,  Loss/total: 0.69551  ,  Loss/giou: 0.16796  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28483  ,  IoU: 0.84304
[train: 29, 100 / 117] FPS: 72.8 (104.4)  ,  DataTime: 0.112 (0.081)  ,  ForwardTime: 0.685  ,  TotalTime: 0.879  ,  Loss/total: 0.70480  ,  Loss/giou: 0.17166  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28447  ,  IoU: 0.83991
[train: 29, 100 / 117] FPS: 72.9 (104.1)  ,  DataTime: 0.122 (0.082)  ,  ForwardTime: 0.674  ,  TotalTime: 0.878  ,  Loss/total: 0.69664  ,  Loss/giou: 0.16837  ,  Loss/l1: 0.01459  ,  Loss/location: 0.28698  ,  IoU: 0.84234
[train: 29, 100 / 117] FPS: 72.9 (104.0)  ,  DataTime: 0.122 (0.080)  ,  ForwardTime: 0.676  ,  TotalTime: 0.878  ,  Loss/total: 0.70919  ,  Loss/giou: 0.17297  ,  Loss/l1: 0.01575  ,  Loss/location: 0.28447  ,  IoU: 0.83957
[train: 29, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.109 (0.076)  ,  ForwardTime: 0.652  ,  TotalTime: 0.836  ,  Loss/total: 0.69376  ,  Loss/giou: 0.17021  ,  Loss/l1: 0.01525  ,  Loss/location: 0.27707  ,  IoU: 0.84132
[train: 29, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.123 (0.079)  ,  ForwardTime: 0.634  ,  TotalTime: 0.836  ,  Loss/total: 0.71405  ,  Loss/giou: 0.17306  ,  Loss/l1: 0.01579  ,  Loss/location: 0.28898  ,  IoU: 0.83908[train: 29, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.108 (0.070)  ,  ForwardTime: 0.658  ,  TotalTime: 0.836  ,  Loss/total: 0.70951  ,  Loss/giou: 0.17370  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28352  ,  IoU: 0.83892

[train: 29, 117 / 117] FPS: 76.5 (109.9)  ,  DataTime: 0.110 (0.075)  ,  ForwardTime: 0.651  ,  TotalTime: 0.836  ,  Loss/total: 0.70575  ,  Loss/giou: 0.17225  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28336  ,  IoU: 0.84000
[train: 29, 117 / 117] FPS: 76.5 (110.0)  ,  DataTime: 0.111 (0.077)  ,  ForwardTime: 0.648  ,  TotalTime: 0.836  ,  Loss/total: 0.71158  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01539  ,  Loss/location: 0.29081  ,  IoU: 0.84008
[train: 29, 117 / 117] FPS: 76.5 (110.1)  ,  DataTime: 0.100 (0.076)  ,  ForwardTime: 0.660  ,  TotalTime: 0.836  ,  Loss/total: 0.70227  ,  Loss/giou: 0.17100  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28354  ,  IoU: 0.84056
[train: 29, 117 / 117] FPS: 76.5 (110.1)  ,  DataTime: 0.110 (0.076)  ,  ForwardTime: 0.650  ,  TotalTime: 0.836  ,  Loss/total: 0.69732  ,  Loss/giou: 0.16905  ,  Loss/l1: 0.01472  ,  Loss/location: 0.28562  ,  IoU: 0.84185
[train: 29, 117 / 117] FPS: 76.5 (109.8)  ,  DataTime: 0.118 (0.076)  ,  ForwardTime: 0.643  ,  TotalTime: 0.837  ,  Loss/total: 0.68951  ,  Loss/giou: 0.16642  ,  Loss/l1: 0.01470  ,  Loss/location: 0.28316  ,  IoU: 0.84419
Epoch Time: 0:01:37.867711
Avg Data Time: 0.10044
Avg GPU Trans Time: 0.07636
Avg Forward Time: 0.65968
Epoch Time: 0:01:37.839603
Avg Data Time: 0.10781
Avg GPU Trans Time: 0.07017
Avg Forward Time: 0.65825
Epoch Time: 0:01:37.846658
Avg Data Time: 0.12341
Avg GPU Trans Time: 0.07915
Avg Forward Time: 0.63374
Epoch Time: 0:01:37.836941
Avg Data Time: 0.10970
Avg GPU Trans Time: 0.07535
Avg Forward Time: 0.65117
Epoch Time: 0:01:37.926806
Avg Data Time: 0.11801
Avg GPU Trans Time: 0.07601
Avg Forward Time: 0.64296
Epoch Time: 0:01:37.857769
Avg Data Time: 0.11105
Avg GPU Trans Time: 0.07728
Avg Forward Time: 0.64806
Epoch Time: 0:01:37.860374
Avg Data Time: 0.10863
Avg GPU Trans Time: 0.07584
Avg Forward Time: 0.65194
Epoch Time: 0:01:37.840675
Avg Data Time: 0.10965
Avg GPU Trans Time: 0.07642
Avg Forward Time: 0.65018
[train: 30, 50 / 117] FPS: 63.6 (84.5)  ,  DataTime: 0.196 (0.086)  ,  ForwardTime: 0.725  ,  TotalTime: 1.007  ,  Loss/total: 0.70657  ,  Loss/giou: 0.16986  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28953  ,  IoU: 0.84254
[train: 30, 50 / 117] FPS: 63.6 (84.2)  ,  DataTime: 0.189 (0.088)  ,  ForwardTime: 0.730  ,  TotalTime: 1.007  ,  Loss/total: 0.73977  ,  Loss/giou: 0.17782  ,  Loss/l1: 0.01656  ,  Loss/location: 0.30134  ,  IoU: 0.83539
[train: 30, 50 / 117] FPS: 63.6 (84.1)  ,  DataTime: 0.199 (0.087)  ,  ForwardTime: 0.721  ,  TotalTime: 1.007  ,  Loss/total: 0.71227  ,  Loss/giou: 0.17287  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28736  ,  IoU: 0.83991[train: 30, 50 / 117] FPS: 63.5 (84.1)  ,  DataTime: 0.185 (0.071)  ,  ForwardTime: 0.751  ,  TotalTime: 1.007  ,  Loss/total: 0.70320  ,  Loss/giou: 0.17382  ,  Loss/l1: 0.01530  ,  Loss/location: 0.27903  ,  IoU: 0.83835

[train: 30, 50 / 117] FPS: 63.6 (84.1)  ,  DataTime: 0.188 (0.085)  ,  ForwardTime: 0.733  ,  TotalTime: 1.007  ,  Loss/total: 0.72894  ,  Loss/giou: 0.17588  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29675  ,  IoU: 0.83666
[train: 30, 50 / 117] FPS: 63.5 (84.1)  ,  DataTime: 0.212 (0.084)  ,  ForwardTime: 0.712  ,  TotalTime: 1.007  ,  Loss/total: 0.70077  ,  Loss/giou: 0.17211  ,  Loss/l1: 0.01560  ,  Loss/location: 0.27858  ,  IoU: 0.84008[train: 30, 50 / 117] FPS: 63.6 (84.0)  ,  DataTime: 0.218 (0.082)  ,  ForwardTime: 0.708  ,  TotalTime: 1.007  ,  Loss/total: 0.69596  ,  Loss/giou: 0.16845  ,  Loss/l1: 0.01491  ,  Loss/location: 0.28452  ,  IoU: 0.84249

[train: 30, 50 / 117] FPS: 63.6 (84.0)  ,  DataTime: 0.202 (0.086)  ,  ForwardTime: 0.719  ,  TotalTime: 1.007  ,  Loss/total: 0.68173  ,  Loss/giou: 0.16666  ,  Loss/l1: 0.01514  ,  Loss/location: 0.27272  ,  IoU: 0.84535
[train: 30, 100 / 117] FPS: 72.7 (108.2)  ,  DataTime: 0.120 (0.084)  ,  ForwardTime: 0.676  ,  TotalTime: 0.880  ,  Loss/total: 0.74253  ,  Loss/giou: 0.17987  ,  Loss/l1: 0.01681  ,  Loss/location: 0.29875  ,  IoU: 0.83413
[train: 30, 100 / 117] FPS: 72.7 (108.4)  ,  DataTime: 0.132 (0.080)  ,  ForwardTime: 0.668  ,  TotalTime: 0.880  ,  Loss/total: 0.70466  ,  Loss/giou: 0.17262  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28087  ,  IoU: 0.84012
[train: 30, 100 / 117] FPS: 72.7 (108.1)  ,  DataTime: 0.129 (0.084)  ,  ForwardTime: 0.667  ,  TotalTime: 0.880  ,  Loss/total: 0.69069  ,  Loss/giou: 0.16743  ,  Loss/l1: 0.01485  ,  Loss/location: 0.28157  ,  IoU: 0.84417
[train: 30, 100 / 117] FPS: 72.7 (108.2)  ,  DataTime: 0.126 (0.083)  ,  ForwardTime: 0.671  ,  TotalTime: 0.880  ,  Loss/total: 0.71686  ,  Loss/giou: 0.17256  ,  Loss/l1: 0.01596  ,  Loss/location: 0.29196  ,  IoU: 0.84035
[train: 30, 100 / 117] FPS: 72.7 (108.2)  ,  DataTime: 0.120 (0.080)  ,  ForwardTime: 0.680  ,  TotalTime: 0.880  ,  Loss/total: 0.73515  ,  Loss/giou: 0.17824  ,  Loss/l1: 0.01652  ,  Loss/location: 0.29605  ,  IoU: 0.83537
[train: 30, 100 / 117] FPS: 72.7 (108.1)  ,  DataTime: 0.125 (0.084)  ,  ForwardTime: 0.671  ,  TotalTime: 0.880  ,  Loss/total: 0.70906  ,  Loss/giou: 0.17270  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28530  ,  IoU: 0.83966
[train: 30, 100 / 117] FPS: 72.7 (108.1)  ,  DataTime: 0.136 (0.077)  ,  ForwardTime: 0.668  ,  TotalTime: 0.880  ,  Loss/total: 0.72164  ,  Loss/giou: 0.17414  ,  Loss/l1: 0.01581  ,  Loss/location: 0.29430  ,  IoU: 0.83817
[train: 30, 100 / 117] FPS: 72.7 (107.8)  ,  DataTime: 0.118 (0.066)  ,  ForwardTime: 0.696  ,  TotalTime: 0.880  ,  Loss/total: 0.72513  ,  Loss/giou: 0.17591  ,  Loss/l1: 0.01591  ,  Loss/location: 0.29376  ,  IoU: 0.83716
[train: 30, 117 / 117] FPS: 76.4 (107.6)  ,  DataTime: 0.115 (0.078)  ,  ForwardTime: 0.644  ,  TotalTime: 0.838  ,  Loss/total: 0.69567  ,  Loss/giou: 0.16840  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28327  ,  IoU: 0.84358
[train: 30, 117 / 117] FPS: 76.4 (107.6)  ,  DataTime: 0.108 (0.079)  ,  ForwardTime: 0.651  ,  TotalTime: 0.838  ,  Loss/total: 0.73647  ,  Loss/giou: 0.17868  ,  Loss/l1: 0.01658  ,  Loss/location: 0.29620  ,  IoU: 0.83497
[train: 30, 117 / 117] FPS: 76.4 (107.4)  ,  DataTime: 0.118 (0.075)  ,  ForwardTime: 0.645  ,  TotalTime: 0.838  ,  Loss/total: 0.70583  ,  Loss/giou: 0.17269  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28199  ,  IoU: 0.83997
[train: 30, 117 / 117] FPS: 76.4 (107.4)  ,  DataTime: 0.121 (0.072)  ,  ForwardTime: 0.645  ,  TotalTime: 0.838  ,  Loss/total: 0.71999  ,  Loss/giou: 0.17395  ,  Loss/l1: 0.01581  ,  Loss/location: 0.29302  ,  IoU: 0.83861[train: 30, 117 / 117] FPS: 76.4 (107.4)  ,  DataTime: 0.106 (0.062)  ,  ForwardTime: 0.670  ,  TotalTime: 0.838  ,  Loss/total: 0.72774  ,  Loss/giou: 0.17657  ,  Loss/l1: 0.01607  ,  Loss/location: 0.29426  ,  IoU: 0.83661

[train: 30, 117 / 117] FPS: 76.4 (107.6)  ,  DataTime: 0.113 (0.077)  ,  ForwardTime: 0.647  ,  TotalTime: 0.838  ,  Loss/total: 0.71521  ,  Loss/giou: 0.17261  ,  Loss/l1: 0.01597  ,  Loss/location: 0.29015  ,  IoU: 0.84034
[train: 30, 117 / 117] FPS: 76.4 (107.7)  ,  DataTime: 0.112 (0.079)  ,  ForwardTime: 0.647  ,  TotalTime: 0.838  ,  Loss/total: 0.70918  ,  Loss/giou: 0.17265  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28545  ,  IoU: 0.83975
[train: 30, 117 / 117] FPS: 76.4 (107.4)  ,  DataTime: 0.107 (0.075)  ,  ForwardTime: 0.655  ,  TotalTime: 0.838  ,  Loss/total: 0.72663  ,  Loss/giou: 0.17669  ,  Loss/l1: 0.01626  ,  Loss/location: 0.29197  ,  IoU: 0.83657
Epoch Time: 0:01:38.000118
Avg Data Time: 0.11498
Avg GPU Trans Time: 0.07825
Avg Forward Time: 0.64437
Epoch Time: 0:01:38.007103
Avg Data Time: 0.10720
Avg GPU Trans Time: 0.07549
Avg Forward Time: 0.65498
Epoch Time: 0:01:38.000611
Avg Data Time: 0.10778
Avg GPU Trans Time: 0.07866
Avg Forward Time: 0.65117
Epoch Time: 0:01:38.024007
Avg Data Time: 0.11769
Avg GPU Trans Time: 0.07513
Avg Forward Time: 0.64500
Epoch Time: 0:01:38.001901
Avg Data Time: 0.11183
Avg GPU Trans Time: 0.07915
Avg Forward Time: 0.64664
Epoch Time: 0:01:38.015469
Avg Data Time: 0.12100
Avg GPU Trans Time: 0.07211
Avg Forward Time: 0.64463
Epoch Time: 0:01:38.021631
Avg Data Time: 0.10568
Avg GPU Trans Time: 0.06230
Avg Forward Time: 0.66981
Epoch Time: 0:01:38.011525
Avg Data Time: 0.11307
Avg GPU Trans Time: 0.07746
Avg Forward Time: 0.64718
[val: 30, 50 / 78] FPS: 22.1 (203.3)  ,  DataTime: 2.342 (0.051)  ,  ForwardTime: 0.499  ,  TotalTime: 2.892  ,  Loss/total: 0.82311  ,  Loss/giou: 0.19869  ,  Loss/l1: 0.02093  ,  Loss/location: 0.32107  ,  IoU: 0.82223
[val: 30, 50 / 78] FPS: 21.9 (200.6)  ,  DataTime: 2.560 (0.051)  ,  ForwardTime: 0.307  ,  TotalTime: 2.918  ,  Loss/total: 0.81587  ,  Loss/giou: 0.19590  ,  Loss/l1: 0.02077  ,  Loss/location: 0.32025  ,  IoU: 0.82475
[val: 30, 50 / 78] FPS: 21.8 (195.3)  ,  DataTime: 2.458 (0.051)  ,  ForwardTime: 0.421  ,  TotalTime: 2.929  ,  Loss/total: 0.84371  ,  Loss/giou: 0.20439  ,  Loss/l1: 0.02217  ,  Loss/location: 0.32408  ,  IoU: 0.81898
[val: 30, 50 / 78] FPS: 21.7 (204.7)  ,  DataTime: 2.677 (0.046)  ,  ForwardTime: 0.234  ,  TotalTime: 2.956  ,  Loss/total: 0.84794  ,  Loss/giou: 0.20365  ,  Loss/l1: 0.02100  ,  Loss/location: 0.33561  ,  IoU: 0.81697
[val: 30, 50 / 78] FPS: 20.8 (187.8)  ,  DataTime: 2.473 (0.051)  ,  ForwardTime: 0.547  ,  TotalTime: 3.071  ,  Loss/total: 0.81311  ,  Loss/giou: 0.19676  ,  Loss/l1: 0.01992  ,  Loss/location: 0.31999  ,  IoU: 0.82246
[val: 30, 50 / 78] FPS: 20.8 (198.1)  ,  DataTime: 2.690 (0.048)  ,  ForwardTime: 0.338  ,  TotalTime: 3.075  ,  Loss/total: 0.81554  ,  Loss/giou: 0.19742  ,  Loss/l1: 0.02079  ,  Loss/location: 0.31674  ,  IoU: 0.82445
[val: 30, 50 / 78] FPS: 20.3 (188.9)  ,  DataTime: 2.776 (0.051)  ,  ForwardTime: 0.325  ,  TotalTime: 3.152  ,  Loss/total: 0.80940  ,  Loss/giou: 0.19574  ,  Loss/l1: 0.02016  ,  Loss/location: 0.31710  ,  IoU: 0.82301
[val: 30, 50 / 78] FPS: 19.3 (4.7)  ,  DataTime: 2.927 (0.050)  ,  ForwardTime: 0.341  ,  TotalTime: 3.319  ,  Loss/total: 0.85696  ,  Loss/giou: 0.20583  ,  Loss/l1: 0.02151  ,  Loss/location: 0.33775  ,  IoU: 0.81639
[val: 30, 78 / 78] FPS: 25.2 (201.8)  ,  DataTime: 2.263 (0.044)  ,  ForwardTime: 0.232  ,  TotalTime: 2.539  ,  Loss/total: 0.85514  ,  Loss/giou: 0.20557  ,  Loss/l1: 0.02181  ,  Loss/location: 0.33496  ,  IoU: 0.81662
Epoch Time: 0:03:18.072897
Avg Data Time: 2.26316
Avg GPU Trans Time: 0.04440
Avg Forward Time: 0.23183
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 30, 78 / 78] FPS: 25.1 (104.1)  ,  DataTime: 2.098 (0.052)  ,  ForwardTime: 0.402  ,  TotalTime: 2.551  ,  Loss/total: 0.83340  ,  Loss/giou: 0.20094  ,  Loss/l1: 0.02131  ,  Loss/location: 0.32494  ,  IoU: 0.82034
Epoch Time: 0:03:18.973803
Avg Data Time: 2.09762
Avg GPU Trans Time: 0.05163
Avg Forward Time: 0.40169
[val: 30, 78 / 78] FPS: 25.0 (206.6)  ,  DataTime: 2.158 (0.050)  ,  ForwardTime: 0.353  ,  TotalTime: 2.561  ,  Loss/total: 0.82472  ,  Loss/giou: 0.19975  ,  Loss/l1: 0.02120  ,  Loss/location: 0.31922  ,  IoU: 0.82159
Epoch Time: 0:03:19.785692
Avg Data Time: 2.15828
Avg GPU Trans Time: 0.05024
Avg Forward Time: 0.35283
[val: 30, 78 / 78] FPS: 24.9 (195.4)  ,  DataTime: 2.237 (0.052)  ,  ForwardTime: 0.281  ,  TotalTime: 2.571  ,  Loss/total: 0.81549  ,  Loss/giou: 0.19691  ,  Loss/l1: 0.02089  ,  Loss/location: 0.31722  ,  IoU: 0.82435
[val: 30, 78 / 78] FPS: 24.9 (199.0)  ,  DataTime: 2.229 (0.051)  ,  ForwardTime: 0.293  ,  TotalTime: 2.573  ,  Loss/total: 0.80715  ,  Loss/giou: 0.19538  ,  Loss/l1: 0.02017  ,  Loss/location: 0.31553  ,  IoU: 0.82410
Epoch Time: 0:03:20.519847
Avg Data Time: 2.23723
Avg GPU Trans Time: 0.05245
Avg Forward Time: 0.28109
Epoch Time: 0:03:20.708946
Avg Data Time: 2.22868
Avg GPU Trans Time: 0.05133
Avg Forward Time: 0.29318
[val: 30, 78 / 78] FPS: 24.6 (181.4)  ,  DataTime: 2.114 (0.050)  ,  ForwardTime: 0.435  ,  TotalTime: 2.599  ,  Loss/total: 0.81585  ,  Loss/giou: 0.19642  ,  Loss/l1: 0.02020  ,  Loss/location: 0.32200  ,  IoU: 0.82332
Epoch Time: 0:03:22.717971
Avg Data Time: 2.11420
Avg GPU Trans Time: 0.05010
Avg Forward Time: 0.43464
[val: 30, 78 / 78] FPS: 24.6 (167.1)  ,  DataTime: 2.249 (0.052)  ,  ForwardTime: 0.304  ,  TotalTime: 2.605  ,  Loss/total: 0.86412  ,  Loss/giou: 0.20581  ,  Loss/l1: 0.02180  ,  Loss/location: 0.34351  ,  IoU: 0.81681
Epoch Time: 0:03:23.181829
Avg Data Time: 2.24948
Avg GPU Trans Time: 0.05159
Avg Forward Time: 0.30383
[val: 30, 78 / 78] FPS: 24.4 (181.8)  ,  DataTime: 2.270 (0.049)  ,  ForwardTime: 0.302  ,  TotalTime: 2.622  ,  Loss/total: 0.82226  ,  Loss/giou: 0.19875  ,  Loss/l1: 0.02096  ,  Loss/location: 0.31994  ,  IoU: 0.82257
Epoch Time: 0:03:24.489629
Avg Data Time: 2.27047
Avg GPU Trans Time: 0.04946
Avg Forward Time: 0.30173
[train: 31, 50 / 117] FPS: 62.1 (84.3)  ,  DataTime: 0.155 (0.086)  ,  ForwardTime: 0.789  ,  TotalTime: 1.030  ,  Loss/total: 0.68939  ,  Loss/giou: 0.16711  ,  Loss/l1: 0.01441  ,  Loss/location: 0.28312  ,  IoU: 0.84361
[train: 31, 50 / 117] FPS: 62.1 (83.9)  ,  DataTime: 0.154 (0.090)  ,  ForwardTime: 0.787  ,  TotalTime: 1.031  ,  Loss/total: 0.69852  ,  Loss/giou: 0.17110  ,  Loss/l1: 0.01540  ,  Loss/location: 0.27932  ,  IoU: 0.84176[train: 31, 50 / 117] FPS: 67.7 (83.9)  ,  DataTime: 0.189 (0.076)  ,  ForwardTime: 0.681  ,  TotalTime: 0.945  ,  Loss/total: 0.69547  ,  Loss/giou: 0.17277  ,  Loss/l1: 0.01590  ,  Loss/location: 0.27045  ,  IoU: 0.84096

[train: 31, 50 / 117] FPS: 67.1 (83.8)  ,  DataTime: 0.195 (0.072)  ,  ForwardTime: 0.687  ,  TotalTime: 0.953  ,  Loss/total: 0.71180  ,  Loss/giou: 0.17417  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28510  ,  IoU: 0.83792
[train: 31, 50 / 117] FPS: 64.4 (84.0)  ,  DataTime: 0.167 (0.083)  ,  ForwardTime: 0.744  ,  TotalTime: 0.994  ,  Loss/total: 0.69029  ,  Loss/giou: 0.16749  ,  Loss/l1: 0.01463  ,  Loss/location: 0.28218  ,  IoU: 0.84332
[train: 31, 50 / 117] FPS: 69.7 (83.9)  ,  DataTime: 0.201 (0.084)  ,  ForwardTime: 0.634  ,  TotalTime: 0.918  ,  Loss/total: 0.71015  ,  Loss/giou: 0.17287  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28643  ,  IoU: 0.83903
[train: 31, 50 / 117] FPS: 63.1 (84.0)  ,  DataTime: 0.146 (0.076)  ,  ForwardTime: 0.792  ,  TotalTime: 1.014  ,  Loss/total: 0.71163  ,  Loss/giou: 0.17416  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28565  ,  IoU: 0.83819
[train: 31, 50 / 117] FPS: 64.1 (84.0)  ,  DataTime: 0.161 (0.086)  ,  ForwardTime: 0.752  ,  TotalTime: 0.998  ,  Loss/total: 0.73781  ,  Loss/giou: 0.18034  ,  Loss/l1: 0.01703  ,  Loss/location: 0.29197  ,  IoU: 0.83416
[train: 31, 100 / 117] FPS: 72.1 (108.1)  ,  DataTime: 0.100 (0.080)  ,  ForwardTime: 0.707  ,  TotalTime: 0.887  ,  Loss/total: 0.71656  ,  Loss/giou: 0.17455  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28907  ,  IoU: 0.83797
[train: 31, 100 / 117] FPS: 71.4 (108.2)  ,  DataTime: 0.106 (0.084)  ,  ForwardTime: 0.706  ,  TotalTime: 0.896  ,  Loss/total: 0.69445  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01466  ,  Loss/location: 0.28393  ,  IoU: 0.84209
[train: 31, 100 / 117] FPS: 75.0 (108.5)  ,  DataTime: 0.121 (0.077)  ,  ForwardTime: 0.655  ,  TotalTime: 0.853  ,  Loss/total: 0.70364  ,  Loss/giou: 0.17322  ,  Loss/l1: 0.01613  ,  Loss/location: 0.27654  ,  IoU: 0.84036
[train: 31, 100 / 117] FPS: 72.9 (108.6)  ,  DataTime: 0.111 (0.081)  ,  ForwardTime: 0.685  ,  TotalTime: 0.878  ,  Loss/total: 0.70476  ,  Loss/giou: 0.17164  ,  Loss/l1: 0.01523  ,  Loss/location: 0.28535  ,  IoU: 0.84003
[train: 31, 100 / 117] FPS: 71.4 (108.1)  ,  DataTime: 0.107 (0.088)  ,  ForwardTime: 0.702  ,  TotalTime: 0.896  ,  Loss/total: 0.71333  ,  Loss/giou: 0.17278  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28924  ,  IoU: 0.83992
[train: 31, 100 / 117] FPS: 74.7 (108.1)  ,  DataTime: 0.126 (0.070)  ,  ForwardTime: 0.661  ,  TotalTime: 0.857  ,  Loss/total: 0.71179  ,  Loss/giou: 0.17372  ,  Loss/l1: 0.01594  ,  Loss/location: 0.28467  ,  IoU: 0.83925
[train: 31, 100 / 117] FPS: 72.7 (108.3)  ,  DataTime: 0.109 (0.083)  ,  ForwardTime: 0.688  ,  TotalTime: 0.880  ,  Loss/total: 0.73750  ,  Loss/giou: 0.17914  ,  Loss/l1: 0.01673  ,  Loss/location: 0.29558  ,  IoU: 0.83504
[train: 31, 100 / 117] FPS: 76.2 (107.7)  ,  DataTime: 0.131 (0.084)  ,  ForwardTime: 0.625  ,  TotalTime: 0.840  ,  Loss/total: 0.69707  ,  Loss/giou: 0.16982  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28131  ,  IoU: 0.84212
[train: 31, 117 / 117] FPS: 75.3 (110.4)  ,  DataTime: 0.096 (0.078)  ,  ForwardTime: 0.676  ,  TotalTime: 0.850  ,  Loss/total: 0.69722  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01467  ,  Loss/location: 0.28666  ,  IoU: 0.84210
[train: 31, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.099 (0.078)  ,  ForwardTime: 0.660  ,  TotalTime: 0.837  ,  Loss/total: 0.73203  ,  Loss/giou: 0.17738  ,  Loss/l1: 0.01648  ,  Loss/location: 0.29490  ,  IoU: 0.83638
[train: 31, 117 / 117] FPS: 75.9 (110.2)  ,  DataTime: 0.091 (0.076)  ,  ForwardTime: 0.676  ,  TotalTime: 0.843  ,  Loss/total: 0.71431  ,  Loss/giou: 0.17327  ,  Loss/l1: 0.01554  ,  Loss/location: 0.29009  ,  IoU: 0.83903
[train: 31, 117 / 117] FPS: 75.2 (110.2)  ,  DataTime: 0.097 (0.082)  ,  ForwardTime: 0.673  ,  TotalTime: 0.851  ,  Loss/total: 0.70810  ,  Loss/giou: 0.17193  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28626  ,  IoU: 0.84045
[train: 31, 117 / 117] FPS: 78.6 (110.1)  ,  DataTime: 0.109 (0.072)  ,  ForwardTime: 0.633  ,  TotalTime: 0.814  ,  Loss/total: 0.70144  ,  Loss/giou: 0.17258  ,  Loss/l1: 0.01593  ,  Loss/location: 0.27661  ,  IoU: 0.84066
[train: 31, 117 / 117] FPS: 78.3 (110.3)  ,  DataTime: 0.113 (0.066)  ,  ForwardTime: 0.639  ,  TotalTime: 0.817  ,  Loss/total: 0.72000  ,  Loss/giou: 0.17568  ,  Loss/l1: 0.01635  ,  Loss/location: 0.28687  ,  IoU: 0.83771
[train: 31, 117 / 117] FPS: 76.7 (110.2)  ,  DataTime: 0.100 (0.077)  ,  ForwardTime: 0.658  ,  TotalTime: 0.835  ,  Loss/total: 0.70594  ,  Loss/giou: 0.17167  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28607  ,  IoU: 0.84018
[train: 31, 117 / 117] FPS: 79.7 (109.4)  ,  DataTime: 0.117 (0.079)  ,  ForwardTime: 0.607  ,  TotalTime: 0.803  ,  Loss/total: 0.69107  ,  Loss/giou: 0.16831  ,  Loss/l1: 0.01485  ,  Loss/location: 0.28020  ,  IoU: 0.84304
Epoch Time: 0:01:35.224559
Avg Data Time: 0.10851
Avg GPU Trans Time: 0.07246
Avg Forward Time: 0.63291
Epoch Time: 0:01:39.545275
Avg Data Time: 0.09661
Avg GPU Trans Time: 0.08170
Avg Forward Time: 0.67251
Epoch Time: 0:01:39.490540
Avg Data Time: 0.09612
Avg GPU Trans Time: 0.07845
Avg Forward Time: 0.67578
Epoch Time: 0:01:37.896188
Avg Data Time: 0.09875
Avg GPU Trans Time: 0.07791
Avg Forward Time: 0.66006
Epoch Time: 0:01:37.669470
Avg Data Time: 0.10035
Avg GPU Trans Time: 0.07664
Avg Forward Time: 0.65779
Epoch Time: 0:01:33.896981
Avg Data Time: 0.11701
Avg GPU Trans Time: 0.07863
Avg Forward Time: 0.60690
Epoch Time: 0:01:38.651192
Avg Data Time: 0.09076
Avg GPU Trans Time: 0.07631
Avg Forward Time: 0.67610
Epoch Time: 0:01:35.642651
Avg Data Time: 0.11281
Avg GPU Trans Time: 0.06608
Avg Forward Time: 0.63858
[train: 32, 50 / 117] FPS: 64.1 (84.5)  ,  DataTime: 0.194 (0.085)  ,  ForwardTime: 0.720  ,  TotalTime: 0.999  ,  Loss/total: 0.71335  ,  Loss/giou: 0.17388  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28702  ,  IoU: 0.83837
[train: 32, 50 / 117] FPS: 64.0 (84.0)  ,  DataTime: 0.182 (0.084)  ,  ForwardTime: 0.733  ,  TotalTime: 1.000  ,  Loss/total: 0.73333  ,  Loss/giou: 0.17919  ,  Loss/l1: 0.01717  ,  Loss/location: 0.28910  ,  IoU: 0.83665
[train: 32, 50 / 117] FPS: 64.0 (84.3)  ,  DataTime: 0.182 (0.082)  ,  ForwardTime: 0.735  ,  TotalTime: 0.999  ,  Loss/total: 0.68301  ,  Loss/giou: 0.16603  ,  Loss/l1: 0.01418  ,  Loss/location: 0.28004  ,  IoU: 0.84329
[train: 32, 50 / 117] FPS: 64.1 (84.3)  ,  DataTime: 0.216 (0.078)  ,  ForwardTime: 0.705  ,  TotalTime: 0.999  ,  Loss/total: 0.70836  ,  Loss/giou: 0.17136  ,  Loss/l1: 0.01530  ,  Loss/location: 0.28913  ,  IoU: 0.84060
[train: 32, 50 / 117] FPS: 64.0 (84.3)  ,  DataTime: 0.193 (0.086)  ,  ForwardTime: 0.721  ,  TotalTime: 0.999  ,  Loss/total: 0.73264  ,  Loss/giou: 0.18045  ,  Loss/l1: 0.01669  ,  Loss/location: 0.28830  ,  IoU: 0.83348
[train: 32, 50 / 117] FPS: 64.0 (84.1)  ,  DataTime: 0.205 (0.080)  ,  ForwardTime: 0.714  ,  TotalTime: 1.000  ,  Loss/total: 0.69739  ,  Loss/giou: 0.17009  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28110  ,  IoU: 0.84128[train: 32, 50 / 117] FPS: 64.0 (84.0)  ,  DataTime: 0.192 (0.084)  ,  ForwardTime: 0.724  ,  TotalTime: 0.999  ,  Loss/total: 0.72365  ,  Loss/giou: 0.17676  ,  Loss/l1: 0.01669  ,  Loss/location: 0.28667  ,  IoU: 0.83741

[train: 32, 50 / 117] FPS: 64.0 (83.8)  ,  DataTime: 0.198 (0.086)  ,  ForwardTime: 0.716  ,  TotalTime: 1.000  ,  Loss/total: 0.70859  ,  Loss/giou: 0.17122  ,  Loss/l1: 0.01512  ,  Loss/location: 0.29053  ,  IoU: 0.83991
[train: 32, 100 / 117] FPS: 73.1 (106.4)  ,  DataTime: 0.124 (0.081)  ,  ForwardTime: 0.671  ,  TotalTime: 0.876  ,  Loss/total: 0.72399  ,  Loss/giou: 0.17622  ,  Loss/l1: 0.01610  ,  Loss/location: 0.29103  ,  IoU: 0.83678[train: 32, 100 / 117] FPS: 73.1 (106.4)  ,  DataTime: 0.128 (0.079)  ,  ForwardTime: 0.669  ,  TotalTime: 0.876  ,  Loss/total: 0.68862  ,  Loss/giou: 0.16860  ,  Loss/l1: 0.01486  ,  Loss/location: 0.27711  ,  IoU: 0.84257

[train: 32, 100 / 117] FPS: 73.1 (106.4)  ,  DataTime: 0.126 (0.080)  ,  ForwardTime: 0.671  ,  TotalTime: 0.876  ,  Loss/total: 0.70293  ,  Loss/giou: 0.16968  ,  Loss/l1: 0.01501  ,  Loss/location: 0.28852  ,  IoU: 0.84148
[train: 32, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.121 (0.081)  ,  ForwardTime: 0.674  ,  TotalTime: 0.876  ,  Loss/total: 0.71228  ,  Loss/giou: 0.17240  ,  Loss/l1: 0.01571  ,  Loss/location: 0.28893  ,  IoU: 0.84004
[train: 32, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.117 (0.081)  ,  ForwardTime: 0.678  ,  TotalTime: 0.876  ,  Loss/total: 0.69036  ,  Loss/giou: 0.16858  ,  Loss/l1: 0.01458  ,  Loss/location: 0.28029  ,  IoU: 0.84179
[train: 32, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.134 (0.072)  ,  ForwardTime: 0.670  ,  TotalTime: 0.876  ,  Loss/total: 0.71106  ,  Loss/giou: 0.17318  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28621  ,  IoU: 0.83964
[train: 32, 100 / 117] FPS: 73.1 (106.2)  ,  DataTime: 0.119 (0.083)  ,  ForwardTime: 0.674  ,  TotalTime: 0.876  ,  Loss/total: 0.71430  ,  Loss/giou: 0.17398  ,  Loss/l1: 0.01602  ,  Loss/location: 0.28621  ,  IoU: 0.83947
[train: 32, 100 / 117] FPS: 73.1 (106.2)  ,  DataTime: 0.126 (0.083)  ,  ForwardTime: 0.666  ,  TotalTime: 0.876  ,  Loss/total: 0.72626  ,  Loss/giou: 0.17741  ,  Loss/l1: 0.01642  ,  Loss/location: 0.28935  ,  IoU: 0.83587
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.114 (0.074)  ,  ForwardTime: 0.645  ,  TotalTime: 0.834  ,  Loss/total: 0.69052  ,  Loss/giou: 0.16863  ,  Loss/l1: 0.01481  ,  Loss/location: 0.27923  ,  IoU: 0.84233
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.111 (0.076)  ,  ForwardTime: 0.647  ,  TotalTime: 0.833  ,  Loss/total: 0.72317  ,  Loss/giou: 0.17570  ,  Loss/l1: 0.01617  ,  Loss/location: 0.29094  ,  IoU: 0.83748
[train: 32, 117 / 117] FPS: 76.8 (110.0)  ,  DataTime: 0.112 (0.075)  ,  ForwardTime: 0.646  ,  TotalTime: 0.833  ,  Loss/total: 0.69762  ,  Loss/giou: 0.16817  ,  Loss/l1: 0.01477  ,  Loss/location: 0.28742  ,  IoU: 0.84277
[train: 32, 117 / 117] FPS: 76.8 (110.2)  ,  DataTime: 0.119 (0.068)  ,  ForwardTime: 0.646  ,  TotalTime: 0.833  ,  Loss/total: 0.71077  ,  Loss/giou: 0.17338  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28577  ,  IoU: 0.83935
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.108 (0.076)  ,  ForwardTime: 0.649  ,  TotalTime: 0.833  ,  Loss/total: 0.71183  ,  Loss/giou: 0.17194  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28990  ,  IoU: 0.84038
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.112 (0.078)  ,  ForwardTime: 0.643  ,  TotalTime: 0.833  ,  Loss/total: 0.72650  ,  Loss/giou: 0.17758  ,  Loss/l1: 0.01640  ,  Loss/location: 0.28932  ,  IoU: 0.83562
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.105 (0.076)  ,  ForwardTime: 0.652  ,  TotalTime: 0.833  ,  Loss/total: 0.69455  ,  Loss/giou: 0.16904  ,  Loss/l1: 0.01475  ,  Loss/location: 0.28273  ,  IoU: 0.84158
[train: 32, 117 / 117] FPS: 76.8 (109.9)  ,  DataTime: 0.107 (0.077)  ,  ForwardTime: 0.650  ,  TotalTime: 0.833  ,  Loss/total: 0.71682  ,  Loss/giou: 0.17375  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28918  ,  IoU: 0.83952
Epoch Time: 0:01:37.520802
Avg Data Time: 0.11420
Avg GPU Trans Time: 0.07437
Avg Forward Time: 0.64493
Epoch Time: 0:01:37.499931
Avg Data Time: 0.10836
Avg GPU Trans Time: 0.07605
Avg Forward Time: 0.64892
Epoch Time: 0:01:37.513509
Avg Data Time: 0.11244
Avg GPU Trans Time: 0.07532
Avg Forward Time: 0.64569
Epoch Time: 0:01:37.501881
Avg Data Time: 0.11247
Avg GPU Trans Time: 0.07806
Avg Forward Time: 0.64281
Epoch Time: 0:01:37.472720
Avg Data Time: 0.11060
Avg GPU Trans Time: 0.07583
Avg Forward Time: 0.64668
Epoch Time: 0:01:37.493155
Avg Data Time: 0.10515
Avg GPU Trans Time: 0.07593
Avg Forward Time: 0.65220
Epoch Time: 0:01:37.484483
Avg Data Time: 0.11940
Avg GPU Trans Time: 0.06778
Avg Forward Time: 0.64603
Epoch Time: 0:01:37.509985
Avg Data Time: 0.10681
Avg GPU Trans Time: 0.07689
Avg Forward Time: 0.64972
[train: 33, 50 / 117] FPS: 63.6 (82.6)  ,  DataTime: 0.202 (0.084)  ,  ForwardTime: 0.722  ,  TotalTime: 1.007  ,  Loss/total: 0.70293  ,  Loss/giou: 0.17287  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28099  ,  IoU: 0.83873
[train: 33, 50 / 117] FPS: 63.6 (82.8)  ,  DataTime: 0.207 (0.084)  ,  ForwardTime: 0.715  ,  TotalTime: 1.006  ,  Loss/total: 0.70922  ,  Loss/giou: 0.17155  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28934  ,  IoU: 0.84008
[train: 33, 50 / 117] FPS: 63.6 (83.2)  ,  DataTime: 0.223 (0.088)  ,  ForwardTime: 0.696  ,  TotalTime: 1.007  ,  Loss/total: 0.69259  ,  Loss/giou: 0.16877  ,  Loss/l1: 0.01486  ,  Loss/location: 0.28074  ,  IoU: 0.84262
[train: 33, 50 / 117] FPS: 63.6 (82.5)  ,  DataTime: 0.202 (0.086)  ,  ForwardTime: 0.719  ,  TotalTime: 1.007  ,  Loss/total: 0.71180  ,  Loss/giou: 0.17249  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28880  ,  IoU: 0.83957[train: 33, 50 / 117] FPS: 63.5 (82.6)  ,  DataTime: 0.203 (0.085)  ,  ForwardTime: 0.720  ,  TotalTime: 1.007  ,  Loss/total: 0.69836  ,  Loss/giou: 0.16950  ,  Loss/l1: 0.01497  ,  Loss/location: 0.28451  ,  IoU: 0.84140[train: 33, 50 / 117] FPS: 63.6 (82.2)  ,  DataTime: 0.221 (0.087)  ,  ForwardTime: 0.698  ,  TotalTime: 1.006  ,  Loss/total: 0.68453  ,  Loss/giou: 0.16736  ,  Loss/l1: 0.01535  ,  Loss/location: 0.27306  ,  IoU: 0.84499


[train: 33, 50 / 117] FPS: 63.5 (82.6)  ,  DataTime: 0.192 (0.088)  ,  ForwardTime: 0.727  ,  TotalTime: 1.007  ,  Loss/total: 0.69657  ,  Loss/giou: 0.16966  ,  Loss/l1: 0.01558  ,  Loss/location: 0.27936  ,  IoU: 0.84311[train: 33, 50 / 117] FPS: 63.6 (82.4)  ,  DataTime: 0.218 (0.092)  ,  ForwardTime: 0.697  ,  TotalTime: 1.007  ,  Loss/total: 0.70195  ,  Loss/giou: 0.16960  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28568  ,  IoU: 0.84276

[train: 33, 100 / 117] FPS: 72.5 (109.0)  ,  DataTime: 0.140 (0.085)  ,  ForwardTime: 0.658  ,  TotalTime: 0.882  ,  Loss/total: 0.68325  ,  Loss/giou: 0.16683  ,  Loss/l1: 0.01497  ,  Loss/location: 0.27475  ,  IoU: 0.84458[train: 33, 100 / 117] FPS: 72.5 (108.7)  ,  DataTime: 0.128 (0.082)  ,  ForwardTime: 0.673  ,  TotalTime: 0.883  ,  Loss/total: 0.70461  ,  Loss/giou: 0.17244  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28237  ,  IoU: 0.83926

[train: 33, 100 / 117] FPS: 72.5 (108.7)  ,  DataTime: 0.123 (0.085)  ,  ForwardTime: 0.676  ,  TotalTime: 0.883  ,  Loss/total: 0.69391  ,  Loss/giou: 0.16842  ,  Loss/l1: 0.01502  ,  Loss/location: 0.28199  ,  IoU: 0.84317
[train: 33, 100 / 117] FPS: 72.5 (108.7)  ,  DataTime: 0.128 (0.084)  ,  ForwardTime: 0.672  ,  TotalTime: 0.883  ,  Loss/total: 0.72526  ,  Loss/giou: 0.17579  ,  Loss/l1: 0.01610  ,  Loss/location: 0.29319  ,  IoU: 0.83710
[train: 33, 100 / 117] FPS: 72.5 (108.6)  ,  DataTime: 0.127 (0.077)  ,  ForwardTime: 0.679  ,  TotalTime: 0.883  ,  Loss/total: 0.70311  ,  Loss/giou: 0.17223  ,  Loss/l1: 0.01523  ,  Loss/location: 0.28250  ,  IoU: 0.83946
[train: 33, 100 / 117] FPS: 72.5 (108.6)  ,  DataTime: 0.130 (0.077)  ,  ForwardTime: 0.675  ,  TotalTime: 0.883  ,  Loss/total: 0.71337  ,  Loss/giou: 0.17221  ,  Loss/l1: 0.01558  ,  Loss/location: 0.29107  ,  IoU: 0.83972
[train: 33, 100 / 117] FPS: 72.5 (108.6)  ,  DataTime: 0.139 (0.084)  ,  ForwardTime: 0.660  ,  TotalTime: 0.883  ,  Loss/total: 0.70362  ,  Loss/giou: 0.17113  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28510  ,  IoU: 0.84044
[train: 33, 100 / 117] FPS: 72.5 (108.6)  ,  DataTime: 0.138 (0.085)  ,  ForwardTime: 0.661  ,  TotalTime: 0.883  ,  Loss/total: 0.69691  ,  Loss/giou: 0.16845  ,  Loss/l1: 0.01504  ,  Loss/location: 0.28482  ,  IoU: 0.84318
[train: 33, 117 / 117] FPS: 76.2 (106.8)  ,  DataTime: 0.114 (0.077)  ,  ForwardTime: 0.649  ,  TotalTime: 0.840  ,  Loss/total: 0.70594  ,  Loss/giou: 0.17313  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28110  ,  IoU: 0.83910[train: 33, 117 / 117] FPS: 76.3 (106.8)  ,  DataTime: 0.124 (0.079)  ,  ForwardTime: 0.636  ,  TotalTime: 0.839  ,  Loss/total: 0.68630  ,  Loss/giou: 0.16776  ,  Loss/l1: 0.01510  ,  Loss/location: 0.27530  ,  IoU: 0.84390

[train: 33, 117 / 117] FPS: 76.2 (106.8)  ,  DataTime: 0.114 (0.073)  ,  ForwardTime: 0.653  ,  TotalTime: 0.839  ,  Loss/total: 0.69809  ,  Loss/giou: 0.17105  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28035  ,  IoU: 0.84042
[train: 33, 117 / 117] FPS: 76.2 (106.7)  ,  DataTime: 0.123 (0.079)  ,  ForwardTime: 0.637  ,  TotalTime: 0.839  ,  Loss/total: 0.70090  ,  Loss/giou: 0.17031  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28498  ,  IoU: 0.84100
[train: 33, 117 / 117] FPS: 76.2 (106.5)  ,  DataTime: 0.123 (0.079)  ,  ForwardTime: 0.638  ,  TotalTime: 0.839  ,  Loss/total: 0.70009  ,  Loss/giou: 0.16943  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28543  ,  IoU: 0.84239
[train: 33, 117 / 117] FPS: 76.3 (106.1)  ,  DataTime: 0.117 (0.072)  ,  ForwardTime: 0.650  ,  TotalTime: 0.839  ,  Loss/total: 0.71172  ,  Loss/giou: 0.17206  ,  Loss/l1: 0.01547  ,  Loss/location: 0.29023  ,  IoU: 0.83943
[train: 33, 117 / 117] FPS: 76.2 (106.1)  ,  DataTime: 0.114 (0.078)  ,  ForwardTime: 0.648  ,  TotalTime: 0.839  ,  Loss/total: 0.72569  ,  Loss/giou: 0.17604  ,  Loss/l1: 0.01620  ,  Loss/location: 0.29262  ,  IoU: 0.83711
[train: 33, 117 / 117] FPS: 76.2 (106.1)  ,  DataTime: 0.110 (0.080)  ,  ForwardTime: 0.650  ,  TotalTime: 0.840  ,  Loss/total: 0.69941  ,  Loss/giou: 0.16963  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28407  ,  IoU: 0.84224
Epoch Time: 0:01:38.218494
Avg Data Time: 0.11371
Avg GPU Trans Time: 0.07807
Avg Forward Time: 0.64769
Epoch Time: 0:01:38.227874
Avg Data Time: 0.10996
Avg GPU Trans Time: 0.07956
Avg Forward Time: 0.65004
Epoch Time: 0:01:38.209301
Avg Data Time: 0.12348
Avg GPU Trans Time: 0.07875
Avg Forward Time: 0.63717
Epoch Time: 0:01:38.235293
Avg Data Time: 0.11402
Avg GPU Trans Time: 0.07685
Avg Forward Time: 0.64875
Epoch Time: 0:01:38.217923
Avg Data Time: 0.11350
Avg GPU Trans Time: 0.07266
Avg Forward Time: 0.65330
Epoch Time: 0:01:38.168588
Avg Data Time: 0.12436
Avg GPU Trans Time: 0.07917
Avg Forward Time: 0.63552
Epoch Time: 0:01:38.216992
Avg Data Time: 0.12299
Avg GPU Trans Time: 0.07874
Avg Forward Time: 0.63773
Epoch Time: 0:01:38.200132
Avg Data Time: 0.11671
Avg GPU Trans Time: 0.07238
Avg Forward Time: 0.65022
[train: 34, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.195 (0.088)  ,  ForwardTime: 0.711  ,  TotalTime: 0.994  ,  Loss/total: 0.70615  ,  Loss/giou: 0.17281  ,  Loss/l1: 0.01544  ,  Loss/location: 0.28331  ,  IoU: 0.83919
[train: 34, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.195 (0.087)  ,  ForwardTime: 0.712  ,  TotalTime: 0.994  ,  Loss/total: 0.71022  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28915  ,  IoU: 0.84066
[train: 34, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.185 (0.092)  ,  ForwardTime: 0.717  ,  TotalTime: 0.994  ,  Loss/total: 0.72441  ,  Loss/giou: 0.17706  ,  Loss/l1: 0.01607  ,  Loss/location: 0.28992  ,  IoU: 0.83593
[train: 34, 50 / 117] FPS: 64.4 (84.4)  ,  DataTime: 0.207 (0.083)  ,  ForwardTime: 0.705  ,  TotalTime: 0.994  ,  Loss/total: 0.70722  ,  Loss/giou: 0.17150  ,  Loss/l1: 0.01592  ,  Loss/location: 0.28464  ,  IoU: 0.84160
[train: 34, 50 / 117] FPS: 64.4 (84.5)  ,  DataTime: 0.185 (0.087)  ,  ForwardTime: 0.722  ,  TotalTime: 0.994  ,  Loss/total: 0.73104  ,  Loss/giou: 0.17474  ,  Loss/l1: 0.01577  ,  Loss/location: 0.30274  ,  IoU: 0.83716
[train: 34, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.212 (0.091)  ,  ForwardTime: 0.692  ,  TotalTime: 0.994  ,  Loss/total: 0.70911  ,  Loss/giou: 0.16959  ,  Loss/l1: 0.01495  ,  Loss/location: 0.29520  ,  IoU: 0.84121
[train: 34, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.184 (0.082)  ,  ForwardTime: 0.729  ,  TotalTime: 0.994  ,  Loss/total: 0.69819  ,  Loss/giou: 0.16870  ,  Loss/l1: 0.01493  ,  Loss/location: 0.28615  ,  IoU: 0.84236
[train: 34, 50 / 117] FPS: 64.4 (84.4)  ,  DataTime: 0.204 (0.077)  ,  ForwardTime: 0.713  ,  TotalTime: 0.994  ,  Loss/total: 0.70648  ,  Loss/giou: 0.17346  ,  Loss/l1: 0.01600  ,  Loss/location: 0.27956  ,  IoU: 0.83954
[train: 34, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.125 (0.084)  ,  ForwardTime: 0.667  ,  TotalTime: 0.876  ,  Loss/total: 0.71300  ,  Loss/giou: 0.17417  ,  Loss/l1: 0.01590  ,  Loss/location: 0.28515  ,  IoU: 0.83894
[train: 34, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.123 (0.081)  ,  ForwardTime: 0.672  ,  TotalTime: 0.876  ,  Loss/total: 0.70794  ,  Loss/giou: 0.17236  ,  Loss/l1: 0.01534  ,  Loss/location: 0.28649  ,  IoU: 0.83952
[train: 34, 100 / 117] FPS: 73.1 (106.6)  ,  DataTime: 0.129 (0.079)  ,  ForwardTime: 0.667  ,  TotalTime: 0.876  ,  Loss/total: 0.70058  ,  Loss/giou: 0.16944  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28487  ,  IoU: 0.84275
[train: 34, 100 / 117] FPS: 73.1 (106.4)  ,  DataTime: 0.121 (0.085)  ,  ForwardTime: 0.670  ,  TotalTime: 0.876  ,  Loss/total: 0.72601  ,  Loss/giou: 0.17424  ,  Loss/l1: 0.01579  ,  Loss/location: 0.29860  ,  IoU: 0.83811
[train: 34, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.119 (0.081)  ,  ForwardTime: 0.676  ,  TotalTime: 0.876  ,  Loss/total: 0.68953  ,  Loss/giou: 0.16792  ,  Loss/l1: 0.01508  ,  Loss/location: 0.27830  ,  IoU: 0.84354
[train: 34, 100 / 117] FPS: 73.1 (106.3)  ,  DataTime: 0.120 (0.086)  ,  ForwardTime: 0.670  ,  TotalTime: 0.876  ,  Loss/total: 0.73611  ,  Loss/giou: 0.17905  ,  Loss/l1: 0.01667  ,  Loss/location: 0.29466  ,  IoU: 0.83500[train: 34, 100 / 117] FPS: 73.1 (106.9)  ,  DataTime: 0.129 (0.072)  ,  ForwardTime: 0.675  ,  TotalTime: 0.876  ,  Loss/total: 0.71872  ,  Loss/giou: 0.17521  ,  Loss/l1: 0.01637  ,  Loss/location: 0.28642  ,  IoU: 0.83828

[train: 34, 100 / 117] FPS: 73.1 (106.5)  ,  DataTime: 0.132 (0.085)  ,  ForwardTime: 0.659  ,  TotalTime: 0.876  ,  Loss/total: 0.71449  ,  Loss/giou: 0.17134  ,  Loss/l1: 0.01548  ,  Loss/location: 0.29442  ,  IoU: 0.84043
[train: 34, 117 / 117] FPS: 76.8 (107.1)  ,  DataTime: 0.110 (0.076)  ,  ForwardTime: 0.648  ,  TotalTime: 0.834  ,  Loss/total: 0.70162  ,  Loss/giou: 0.17150  ,  Loss/l1: 0.01527  ,  Loss/location: 0.28225  ,  IoU: 0.84051[train: 34, 117 / 117] FPS: 76.8 (107.1)  ,  DataTime: 0.112 (0.078)  ,  ForwardTime: 0.644  ,  TotalTime: 0.834  ,  Loss/total: 0.71081  ,  Loss/giou: 0.17366  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28471  ,  IoU: 0.83930

[train: 34, 117 / 117] FPS: 76.8 (107.0)  ,  DataTime: 0.115 (0.074)  ,  ForwardTime: 0.644  ,  TotalTime: 0.834  ,  Loss/total: 0.70203  ,  Loss/giou: 0.16981  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28542  ,  IoU: 0.84240
[train: 34, 117 / 117] FPS: 76.8 (107.1)  ,  DataTime: 0.108 (0.081)  ,  ForwardTime: 0.645  ,  TotalTime: 0.833  ,  Loss/total: 0.72773  ,  Loss/giou: 0.17703  ,  Loss/l1: 0.01629  ,  Loss/location: 0.29220  ,  IoU: 0.83645
[train: 34, 117 / 117] FPS: 76.8 (107.1)  ,  DataTime: 0.115 (0.068)  ,  ForwardTime: 0.650  ,  TotalTime: 0.833  ,  Loss/total: 0.71947  ,  Loss/giou: 0.17535  ,  Loss/l1: 0.01634  ,  Loss/location: 0.28705  ,  IoU: 0.83797
[train: 34, 117 / 117] FPS: 76.8 (107.3)  ,  DataTime: 0.109 (0.079)  ,  ForwardTime: 0.645  ,  TotalTime: 0.834  ,  Loss/total: 0.71550  ,  Loss/giou: 0.17221  ,  Loss/l1: 0.01546  ,  Loss/location: 0.29378  ,  IoU: 0.83968
[train: 34, 117 / 117] FPS: 76.8 (107.1)  ,  DataTime: 0.118 (0.081)  ,  ForwardTime: 0.635  ,  TotalTime: 0.834  ,  Loss/total: 0.70479  ,  Loss/giou: 0.16943  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28988  ,  IoU: 0.84187
[train: 34, 117 / 117] FPS: 76.8 (106.7)  ,  DataTime: 0.107 (0.076)  ,  ForwardTime: 0.651  ,  TotalTime: 0.834  ,  Loss/total: 0.69131  ,  Loss/giou: 0.16762  ,  Loss/l1: 0.01491  ,  Loss/location: 0.28153  ,  IoU: 0.84355
Epoch Time: 0:01:37.549347
Avg Data Time: 0.11513
Avg GPU Trans Time: 0.07429
Avg Forward Time: 0.64434
Epoch Time: 0:01:37.517053
Avg Data Time: 0.10786
Avg GPU Trans Time: 0.08075
Avg Forward Time: 0.64487
Epoch Time: 0:01:37.552392
Avg Data Time: 0.11771
Avg GPU Trans Time: 0.08082
Avg Forward Time: 0.63525
Epoch Time: 0:01:37.552292
Avg Data Time: 0.10894
Avg GPU Trans Time: 0.07937
Avg Forward Time: 0.64546
Epoch Time: 0:01:37.524270
Avg Data Time: 0.11193
Avg GPU Trans Time: 0.07789
Avg Forward Time: 0.64373
Epoch Time: 0:01:37.542922
Avg Data Time: 0.10684
Avg GPU Trans Time: 0.07616
Avg Forward Time: 0.65071
Epoch Time: 0:01:37.559162
Avg Data Time: 0.10973
Avg GPU Trans Time: 0.07592
Avg Forward Time: 0.64820
Epoch Time: 0:01:37.518087
Avg Data Time: 0.11524
Avg GPU Trans Time: 0.06795
Avg Forward Time: 0.65030
[train: 35, 50 / 117] FPS: 63.8 (83.8)  ,  DataTime: 0.207 (0.094)  ,  ForwardTime: 0.702  ,  TotalTime: 1.003  ,  Loss/total: 0.74181  ,  Loss/giou: 0.18046  ,  Loss/l1: 0.01704  ,  Loss/location: 0.29569  ,  IoU: 0.83406
[train: 35, 50 / 117] FPS: 63.8 (83.8)  ,  DataTime: 0.178 (0.080)  ,  ForwardTime: 0.745  ,  TotalTime: 1.003  ,  Loss/total: 0.69054  ,  Loss/giou: 0.16841  ,  Loss/l1: 0.01510  ,  Loss/location: 0.27821  ,  IoU: 0.84423
[train: 35, 50 / 117] FPS: 63.8 (83.7)  ,  DataTime: 0.190 (0.087)  ,  ForwardTime: 0.726  ,  TotalTime: 1.003  ,  Loss/total: 0.71558  ,  Loss/giou: 0.17402  ,  Loss/l1: 0.01608  ,  Loss/location: 0.28714  ,  IoU: 0.83929
[train: 35, 50 / 117] FPS: 63.7 (83.7)  ,  DataTime: 0.225 (0.089)  ,  ForwardTime: 0.691  ,  TotalTime: 1.004  ,  Loss/total: 0.70946  ,  Loss/giou: 0.17357  ,  Loss/l1: 0.01592  ,  Loss/location: 0.28272  ,  IoU: 0.83894
[train: 35, 50 / 117] FPS: 63.8 (83.7)  ,  DataTime: 0.193 (0.087)  ,  ForwardTime: 0.724  ,  TotalTime: 1.003  ,  Loss/total: 0.70611  ,  Loss/giou: 0.17054  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28668  ,  IoU: 0.84207
[train: 35, 50 / 117] FPS: 63.8 (83.7)  ,  DataTime: 0.210 (0.079)  ,  ForwardTime: 0.715  ,  TotalTime: 1.004  ,  Loss/total: 0.73020  ,  Loss/giou: 0.17628  ,  Loss/l1: 0.01633  ,  Loss/location: 0.29602  ,  IoU: 0.83747
[train: 35, 50 / 117] FPS: 63.8 (83.2)  ,  DataTime: 0.206 (0.092)  ,  ForwardTime: 0.705  ,  TotalTime: 1.004  ,  Loss/total: 0.72288  ,  Loss/giou: 0.17499  ,  Loss/l1: 0.01636  ,  Loss/location: 0.29111  ,  IoU: 0.83906
[train: 35, 50 / 117] FPS: 63.8 (82.8)  ,  DataTime: 0.221 (0.086)  ,  ForwardTime: 0.696  ,  TotalTime: 1.003  ,  Loss/total: 0.73348  ,  Loss/giou: 0.17910  ,  Loss/l1: 0.01727  ,  Loss/location: 0.28893  ,  IoU: 0.83510
[train: 35, 100 / 117] FPS: 72.6 (103.4)  ,  DataTime: 0.140 (0.085)  ,  ForwardTime: 0.657  ,  TotalTime: 0.882  ,  Loss/total: 0.70099  ,  Loss/giou: 0.17206  ,  Loss/l1: 0.01561  ,  Loss/location: 0.27883  ,  IoU: 0.84017
[train: 35, 100 / 117] FPS: 72.6 (103.5)  ,  DataTime: 0.122 (0.084)  ,  ForwardTime: 0.675  ,  TotalTime: 0.882  ,  Loss/total: 0.70376  ,  Loss/giou: 0.17036  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28565  ,  IoU: 0.84154[train: 35, 100 / 117] FPS: 72.6 (103.4)  ,  DataTime: 0.114 (0.078)  ,  ForwardTime: 0.690  ,  TotalTime: 0.882  ,  Loss/total: 0.69952  ,  Loss/giou: 0.17025  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28250  ,  IoU: 0.84230

[train: 35, 100 / 117] FPS: 72.6 (103.6)  ,  DataTime: 0.139 (0.085)  ,  ForwardTime: 0.659  ,  TotalTime: 0.882  ,  Loss/total: 0.71890  ,  Loss/giou: 0.17505  ,  Loss/l1: 0.01631  ,  Loss/location: 0.28725  ,  IoU: 0.83794
[train: 35, 100 / 117] FPS: 72.6 (103.5)  ,  DataTime: 0.132 (0.074)  ,  ForwardTime: 0.676  ,  TotalTime: 0.882  ,  Loss/total: 0.71460  ,  Loss/giou: 0.17368  ,  Loss/l1: 0.01607  ,  Loss/location: 0.28691  ,  IoU: 0.83948[train: 35, 100 / 117] FPS: 72.6 (103.4)  ,  DataTime: 0.123 (0.082)  ,  ForwardTime: 0.677  ,  TotalTime: 0.882  ,  Loss/total: 0.71620  ,  Loss/giou: 0.17365  ,  Loss/l1: 0.01588  ,  Loss/location: 0.28951  ,  IoU: 0.83925

[train: 35, 100 / 117] FPS: 72.6 (103.6)  ,  DataTime: 0.130 (0.089)  ,  ForwardTime: 0.663  ,  TotalTime: 0.882  ,  Loss/total: 0.73044  ,  Loss/giou: 0.17792  ,  Loss/l1: 0.01651  ,  Loss/location: 0.29204  ,  IoU: 0.83570
[train: 35, 100 / 117] FPS: 72.6 (103.8)  ,  DataTime: 0.131 (0.087)  ,  ForwardTime: 0.663  ,  TotalTime: 0.882  ,  Loss/total: 0.71143  ,  Loss/giou: 0.17350  ,  Loss/l1: 0.01593  ,  Loss/location: 0.28479  ,  IoU: 0.83962
[train: 35, 117 / 117] FPS: 76.3 (106.7)  ,  DataTime: 0.125 (0.080)  ,  ForwardTime: 0.634  ,  TotalTime: 0.839  ,  Loss/total: 0.69950  ,  Loss/giou: 0.17197  ,  Loss/l1: 0.01557  ,  Loss/location: 0.27772  ,  IoU: 0.84017
[train: 35, 117 / 117] FPS: 76.3 (106.7)  ,  DataTime: 0.102 (0.074)  ,  ForwardTime: 0.663  ,  TotalTime: 0.839  ,  Loss/total: 0.69715  ,  Loss/giou: 0.17007  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28046  ,  IoU: 0.84255
[train: 35, 117 / 117] FPS: 76.3 (106.7)  ,  DataTime: 0.110 (0.078)  ,  ForwardTime: 0.651  ,  TotalTime: 0.839  ,  Loss/total: 0.69817  ,  Loss/giou: 0.16931  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28335  ,  IoU: 0.84229[train: 35, 117 / 117] FPS: 76.3 (106.6)  ,  DataTime: 0.110 (0.077)  ,  ForwardTime: 0.652  ,  TotalTime: 0.839  ,  Loss/total: 0.70937  ,  Loss/giou: 0.17199  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28750  ,  IoU: 0.84048

[train: 35, 117 / 117] FPS: 76.3 (106.6)  ,  DataTime: 0.118 (0.081)  ,  ForwardTime: 0.640  ,  TotalTime: 0.839  ,  Loss/total: 0.71174  ,  Loss/giou: 0.17362  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28455  ,  IoU: 0.83959
[train: 35, 117 / 117] FPS: 76.3 (106.5)  ,  DataTime: 0.124 (0.079)  ,  ForwardTime: 0.636  ,  TotalTime: 0.839  ,  Loss/total: 0.71070  ,  Loss/giou: 0.17363  ,  Loss/l1: 0.01610  ,  Loss/location: 0.28294  ,  IoU: 0.83917
[train: 35, 117 / 117] FPS: 76.3 (106.6)  ,  DataTime: 0.117 (0.084)  ,  ForwardTime: 0.638  ,  TotalTime: 0.839  ,  Loss/total: 0.72915  ,  Loss/giou: 0.17778  ,  Loss/l1: 0.01641  ,  Loss/location: 0.29156  ,  IoU: 0.83555[train: 35, 117 / 117] FPS: 76.3 (106.6)  ,  DataTime: 0.117 (0.070)  ,  ForwardTime: 0.651  ,  TotalTime: 0.839  ,  Loss/total: 0.71070  ,  Loss/giou: 0.17339  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28378  ,  IoU: 0.83971

Epoch Time: 0:01:38.147831
Avg Data Time: 0.10229
Avg GPU Trans Time: 0.07359
Avg Forward Time: 0.66299
Epoch Time: 0:01:38.133741
Avg Data Time: 0.11665
Avg GPU Trans Time: 0.08373
Avg Forward Time: 0.63837
Epoch Time: 0:01:38.153783
Avg Data Time: 0.11001
Avg GPU Trans Time: 0.07709
Avg Forward Time: 0.65182
Epoch Time: 0:01:38.168968
Avg Data Time: 0.11749
Avg GPU Trans Time: 0.07025
Avg Forward Time: 0.65132
Epoch Time: 0:01:38.177781
Avg Data Time: 0.12513
Avg GPU Trans Time: 0.07978
Avg Forward Time: 0.63422
Epoch Time: 0:01:38.141014
Avg Data Time: 0.12376
Avg GPU Trans Time: 0.07908
Avg Forward Time: 0.63597
Epoch Time: 0:01:38.135939
Avg Data Time: 0.10965
Avg GPU Trans Time: 0.07820
Avg Forward Time: 0.65092
Epoch Time: 0:01:38.158216
Avg Data Time: 0.11754
Avg GPU Trans Time: 0.08134
Avg Forward Time: 0.64008
[val: 35, 50 / 78] FPS: 28.5 (198.2)  ,  DataTime: 1.961 (0.052)  ,  ForwardTime: 0.232  ,  TotalTime: 2.244  ,  Loss/total: 0.86390  ,  Loss/giou: 0.20918  ,  Loss/l1: 0.02244  ,  Loss/location: 0.33332  ,  IoU: 0.81369
[val: 35, 50 / 78] FPS: 28.1 (168.7)  ,  DataTime: 1.968 (0.052)  ,  ForwardTime: 0.261  ,  TotalTime: 2.281  ,  Loss/total: 0.84092  ,  Loss/giou: 0.19903  ,  Loss/l1: 0.02100  ,  Loss/location: 0.33787  ,  IoU: 0.82202
[val: 35, 50 / 78] FPS: 27.5 (198.7)  ,  DataTime: 2.042 (0.053)  ,  ForwardTime: 0.232  ,  TotalTime: 2.327  ,  Loss/total: 0.85181  ,  Loss/giou: 0.20399  ,  Loss/l1: 0.02217  ,  Loss/location: 0.33300  ,  IoU: 0.81891
[val: 35, 50 / 78] FPS: 27.4 (12.5)  ,  DataTime: 1.936 (0.051)  ,  ForwardTime: 0.349  ,  TotalTime: 2.336  ,  Loss/total: 0.80444  ,  Loss/giou: 0.19502  ,  Loss/l1: 0.01996  ,  Loss/location: 0.31463  ,  IoU: 0.82386
[val: 35, 50 / 78] FPS: 26.5 (195.9)  ,  DataTime: 1.981 (0.049)  ,  ForwardTime: 0.387  ,  TotalTime: 2.417  ,  Loss/total: 0.79464  ,  Loss/giou: 0.19226  ,  Loss/l1: 0.01960  ,  Loss/location: 0.31214  ,  IoU: 0.82604
[val: 35, 50 / 78] FPS: 25.7 (213.6)  ,  DataTime: 2.214 (0.048)  ,  ForwardTime: 0.231  ,  TotalTime: 2.494  ,  Loss/total: 0.85989  ,  Loss/giou: 0.20734  ,  Loss/l1: 0.02235  ,  Loss/location: 0.33346  ,  IoU: 0.81660
[val: 35, 50 / 78] FPS: 25.3 (224.2)  ,  DataTime: 2.184 (0.046)  ,  ForwardTime: 0.297  ,  TotalTime: 2.527  ,  Loss/total: 0.85886  ,  Loss/giou: 0.20692  ,  Loss/l1: 0.02183  ,  Loss/location: 0.33587  ,  IoU: 0.81604
[val: 35, 50 / 78] FPS: 25.0 (221.6)  ,  DataTime: 2.271 (0.051)  ,  ForwardTime: 0.232  ,  TotalTime: 2.555  ,  Loss/total: 0.86255  ,  Loss/giou: 0.20762  ,  Loss/l1: 0.02286  ,  Loss/location: 0.33304  ,  IoU: 0.81631
[val: 35, 78 / 78] FPS: 31.8 (202.4)  ,  DataTime: 1.654 (0.052)  ,  ForwardTime: 0.306  ,  TotalTime: 2.012  ,  Loss/total: 0.81279  ,  Loss/giou: 0.19579  ,  Loss/l1: 0.02001  ,  Loss/location: 0.32114  ,  IoU: 0.82354
Epoch Time: 0:02:36.937631
Avg Data Time: 1.65379
Avg GPU Trans Time: 0.05249
Avg Forward Time: 0.30574
[val: 35, 78 / 78] FPS: 31.1 (217.0)  ,  DataTime: 1.771 (0.053)  ,  ForwardTime: 0.232  ,  TotalTime: 2.056  ,  Loss/total: 0.85431  ,  Loss/giou: 0.20343  ,  Loss/l1: 0.02192  ,  Loss/location: 0.33786  ,  IoU: 0.81888
[val: 35, 78 / 78] FPS: 31.1 (200.7)  ,  DataTime: 1.775 (0.050)  ,  ForwardTime: 0.231  ,  TotalTime: 2.056  ,  Loss/total: 0.83858  ,  Loss/giou: 0.20305  ,  Loss/l1: 0.02133  ,  Loss/location: 0.32579  ,  IoU: 0.81807
Epoch Time: 0:02:40.380788
Avg Data Time: 1.77113
Avg GPU Trans Time: 0.05270
Avg Forward Time: 0.23234
Epoch Time: 0:02:40.355280
Avg Data Time: 1.77499
Avg GPU Trans Time: 0.05018
Avg Forward Time: 0.23067
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 35, 78 / 78] FPS: 31.1 (205.2)  ,  DataTime: 1.679 (0.050)  ,  ForwardTime: 0.331  ,  TotalTime: 2.060  ,  Loss/total: 0.80345  ,  Loss/giou: 0.19560  ,  Loss/l1: 0.02028  ,  Loss/location: 0.31087  ,  IoU: 0.82427
Epoch Time: 0:02:40.665381
Avg Data Time: 1.67869
Avg GPU Trans Time: 0.04999
Avg Forward Time: 0.33113
[val: 35, 78 / 78] FPS: 31.0 (206.4)  ,  DataTime: 1.782 (0.049)  ,  ForwardTime: 0.232  ,  TotalTime: 2.063  ,  Loss/total: 0.86698  ,  Loss/giou: 0.20964  ,  Loss/l1: 0.02260  ,  Loss/location: 0.33471  ,  IoU: 0.81460
Epoch Time: 0:02:40.892403
Avg Data Time: 1.78212
Avg GPU Trans Time: 0.04886
Avg Forward Time: 0.23175
[val: 35, 78 / 78] FPS: 30.9 (202.4)  ,  DataTime: 1.764 (0.053)  ,  ForwardTime: 0.253  ,  TotalTime: 2.069  ,  Loss/total: 0.84459  ,  Loss/giou: 0.20057  ,  Loss/l1: 0.02127  ,  Loss/location: 0.33713  ,  IoU: 0.82047
Epoch Time: 0:02:41.420429
Avg Data Time: 1.76381
Avg GPU Trans Time: 0.05258
Avg Forward Time: 0.25311
[val: 35, 78 / 78] FPS: 30.6 (178.7)  ,  DataTime: 1.807 (0.051)  ,  ForwardTime: 0.232  ,  TotalTime: 2.090  ,  Loss/total: 0.86034  ,  Loss/giou: 0.20659  ,  Loss/l1: 0.02258  ,  Loss/location: 0.33427  ,  IoU: 0.81736
[val: 35, 78 / 78] FPS: 30.6 (161.7)  ,  DataTime: 1.770 (0.048)  ,  ForwardTime: 0.275  ,  TotalTime: 2.092  ,  Loss/total: 0.84723  ,  Loss/giou: 0.20275  ,  Loss/l1: 0.02136  ,  Loss/location: 0.33496  ,  IoU: 0.81939
Epoch Time: 0:02:43.010190
Avg Data Time: 1.80687
Avg GPU Trans Time: 0.05061
Avg Forward Time: 0.23239
Epoch Time: 0:02:43.214185
Avg Data Time: 1.77012
Avg GPU Trans Time: 0.04771
Avg Forward Time: 0.27466
[train: 36, 50 / 117] FPS: 65.3 (85.1)  ,  DataTime: 0.169 (0.084)  ,  ForwardTime: 0.727  ,  TotalTime: 0.980  ,  Loss/total: 0.69331  ,  Loss/giou: 0.16595  ,  Loss/l1: 0.01436  ,  Loss/location: 0.28961  ,  IoU: 0.84387
[train: 36, 50 / 117] FPS: 65.1 (85.1)  ,  DataTime: 0.180 (0.086)  ,  ForwardTime: 0.717  ,  TotalTime: 0.983  ,  Loss/total: 0.69187  ,  Loss/giou: 0.16963  ,  Loss/l1: 0.01523  ,  Loss/location: 0.27649  ,  IoU: 0.84215
[train: 36, 50 / 117] FPS: 59.9 (85.1)  ,  DataTime: 0.128 (0.087)  ,  ForwardTime: 0.854  ,  TotalTime: 1.069  ,  Loss/total: 0.71731  ,  Loss/giou: 0.17172  ,  Loss/l1: 0.01535  ,  Loss/location: 0.29711  ,  IoU: 0.83980
[train: 36, 50 / 117] FPS: 64.2 (85.1)  ,  DataTime: 0.154 (0.078)  ,  ForwardTime: 0.764  ,  TotalTime: 0.996  ,  Loss/total: 0.68454  ,  Loss/giou: 0.16587  ,  Loss/l1: 0.01464  ,  Loss/location: 0.27960  ,  IoU: 0.84498
[train: 36, 50 / 117] FPS: 68.0 (85.4)  ,  DataTime: 0.207 (0.084)  ,  ForwardTime: 0.650  ,  TotalTime: 0.942  ,  Loss/total: 0.71482  ,  Loss/giou: 0.17238  ,  Loss/l1: 0.01557  ,  Loss/location: 0.29221  ,  IoU: 0.83978
[train: 36, 50 / 117] FPS: 63.9 (85.0)  ,  DataTime: 0.151 (0.080)  ,  ForwardTime: 0.771  ,  TotalTime: 1.001  ,  Loss/total: 0.67255  ,  Loss/giou: 0.16599  ,  Loss/l1: 0.01448  ,  Loss/location: 0.26817  ,  IoU: 0.84475
[train: 36, 50 / 117] FPS: 64.6 (84.8)  ,  DataTime: 0.172 (0.085)  ,  ForwardTime: 0.733  ,  TotalTime: 0.990  ,  Loss/total: 0.73695  ,  Loss/giou: 0.17701  ,  Loss/l1: 0.01651  ,  Loss/location: 0.30038  ,  IoU: 0.83686
[train: 36, 50 / 117] FPS: 67.7 (84.9)  ,  DataTime: 0.200 (0.082)  ,  ForwardTime: 0.664  ,  TotalTime: 0.946  ,  Loss/total: 0.71718  ,  Loss/giou: 0.17443  ,  Loss/l1: 0.01585  ,  Loss/location: 0.28905  ,  IoU: 0.83807
[train: 36, 100 / 117] FPS: 72.6 (84.0)  ,  DataTime: 0.107 (0.077)  ,  ForwardTime: 0.698  ,  TotalTime: 0.882  ,  Loss/total: 0.69205  ,  Loss/giou: 0.16833  ,  Loss/l1: 0.01475  ,  Loss/location: 0.28165  ,  IoU: 0.84259
[train: 36, 100 / 117] FPS: 69.7 (84.1)  ,  DataTime: 0.097 (0.086)  ,  ForwardTime: 0.736  ,  TotalTime: 0.918  ,  Loss/total: 0.71435  ,  Loss/giou: 0.17183  ,  Loss/l1: 0.01537  ,  Loss/location: 0.29384  ,  IoU: 0.83992
[train: 36, 100 / 117] FPS: 72.8 (83.8)  ,  DataTime: 0.117 (0.084)  ,  ForwardTime: 0.679  ,  TotalTime: 0.879  ,  Loss/total: 0.73923  ,  Loss/giou: 0.17813  ,  Loss/l1: 0.01658  ,  Loss/location: 0.30007  ,  IoU: 0.83564
[train: 36, 100 / 117] FPS: 74.7 (84.1)  ,  DataTime: 0.125 (0.078)  ,  ForwardTime: 0.653  ,  TotalTime: 0.857  ,  Loss/total: 0.73189  ,  Loss/giou: 0.17797  ,  Loss/l1: 0.01665  ,  Loss/location: 0.29269  ,  IoU: 0.83610
[train: 36, 100 / 117] FPS: 72.4 (83.9)  ,  DataTime: 0.105 (0.078)  ,  ForwardTime: 0.701  ,  TotalTime: 0.885  ,  Loss/total: 0.68849  ,  Loss/giou: 0.16866  ,  Loss/l1: 0.01511  ,  Loss/location: 0.27560  ,  IoU: 0.84299
[train: 36, 100 / 117] FPS: 74.9 (84.2)  ,  DataTime: 0.135 (0.083)  ,  ForwardTime: 0.637  ,  TotalTime: 0.855  ,  Loss/total: 0.71473  ,  Loss/giou: 0.17245  ,  Loss/l1: 0.01554  ,  Loss/location: 0.29211  ,  IoU: 0.83975
[train: 36, 100 / 117] FPS: 73.1 (83.8)  ,  DataTime: 0.121 (0.085)  ,  ForwardTime: 0.670  ,  TotalTime: 0.875  ,  Loss/total: 0.71033  ,  Loss/giou: 0.17366  ,  Loss/l1: 0.01573  ,  Loss/location: 0.28436  ,  IoU: 0.83910
[train: 36, 100 / 117] FPS: 73.3 (84.0)  ,  DataTime: 0.115 (0.083)  ,  ForwardTime: 0.675  ,  TotalTime: 0.874  ,  Loss/total: 0.70394  ,  Loss/giou: 0.17025  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28786  ,  IoU: 0.84099
[train: 36, 117 / 117] FPS: 76.9 (109.8)  ,  DataTime: 0.103 (0.078)  ,  ForwardTime: 0.650  ,  TotalTime: 0.832  ,  Loss/total: 0.70018  ,  Loss/giou: 0.16973  ,  Loss/l1: 0.01508  ,  Loss/location: 0.28530  ,  IoU: 0.84163
[train: 36, 117 / 117] FPS: 73.6 (109.7)  ,  DataTime: 0.088 (0.080)  ,  ForwardTime: 0.702  ,  TotalTime: 0.870  ,  Loss/total: 0.71560  ,  Loss/giou: 0.17257  ,  Loss/l1: 0.01547  ,  Loss/location: 0.29313  ,  IoU: 0.83931
[train: 36, 117 / 117] FPS: 76.3 (109.7)  ,  DataTime: 0.096 (0.073)  ,  ForwardTime: 0.670  ,  TotalTime: 0.839  ,  Loss/total: 0.69556  ,  Loss/giou: 0.16918  ,  Loss/l1: 0.01484  ,  Loss/location: 0.28300  ,  IoU: 0.84183
[train: 36, 117 / 117] FPS: 78.5 (109.7)  ,  DataTime: 0.121 (0.077)  ,  ForwardTime: 0.617  ,  TotalTime: 0.815  ,  Loss/total: 0.71290  ,  Loss/giou: 0.17231  ,  Loss/l1: 0.01552  ,  Loss/location: 0.29067  ,  IoU: 0.83984
[train: 36, 117 / 117] FPS: 76.1 (109.7)  ,  DataTime: 0.095 (0.075)  ,  ForwardTime: 0.672  ,  TotalTime: 0.841  ,  Loss/total: 0.69523  ,  Loss/giou: 0.16993  ,  Loss/l1: 0.01527  ,  Loss/location: 0.27902  ,  IoU: 0.84190
[train: 36, 117 / 117] FPS: 78.3 (109.8)  ,  DataTime: 0.112 (0.074)  ,  ForwardTime: 0.631  ,  TotalTime: 0.817  ,  Loss/total: 0.72434  ,  Loss/giou: 0.17618  ,  Loss/l1: 0.01634  ,  Loss/location: 0.29029  ,  IoU: 0.83741[train: 36, 117 / 117] FPS: 76.8 (109.6)  ,  DataTime: 0.108 (0.079)  ,  ForwardTime: 0.646  ,  TotalTime: 0.833  ,  Loss/total: 0.71401  ,  Loss/giou: 0.17453  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28518  ,  IoU: 0.83869

[train: 36, 117 / 117] FPS: 76.5 (109.3)  ,  DataTime: 0.105 (0.078)  ,  ForwardTime: 0.654  ,  TotalTime: 0.836  ,  Loss/total: 0.73851  ,  Loss/giou: 0.17782  ,  Loss/l1: 0.01654  ,  Loss/location: 0.30019  ,  IoU: 0.83593
Epoch Time: 0:01:38.157148
Avg Data Time: 0.09642
Avg GPU Trans Time: 0.07263
Avg Forward Time: 0.66990
Epoch Time: 0:01:41.783697
Avg Data Time: 0.08783
Avg GPU Trans Time: 0.08029
Avg Forward Time: 0.70182
Epoch Time: 0:01:37.503608
Avg Data Time: 0.10842
Avg GPU Trans Time: 0.07932
Avg Forward Time: 0.64562
Epoch Time: 0:01:38.407919
Avg Data Time: 0.09492
Avg GPU Trans Time: 0.07460
Avg Forward Time: 0.67157
Epoch Time: 0:01:35.411578
Avg Data Time: 0.12095
Avg GPU Trans Time: 0.07722
Avg Forward Time: 0.61731
Epoch Time: 0:01:35.614258
Avg Data Time: 0.11227
Avg GPU Trans Time: 0.07391
Avg Forward Time: 0.63104
Epoch Time: 0:01:37.851241
Avg Data Time: 0.10471
Avg GPU Trans Time: 0.07805
Avg Forward Time: 0.65357
Epoch Time: 0:01:37.326051
Avg Data Time: 0.10336
Avg GPU Trans Time: 0.07833
Avg Forward Time: 0.65016
[train: 37, 50 / 117] FPS: 64.0 (86.6)  ,  DataTime: 0.192 (0.079)  ,  ForwardTime: 0.729  ,  TotalTime: 1.001  ,  Loss/total: 0.73602  ,  Loss/giou: 0.17770  ,  Loss/l1: 0.01644  ,  Loss/location: 0.29841  ,  IoU: 0.83585
[train: 37, 50 / 117] FPS: 64.0 (86.5)  ,  DataTime: 0.187 (0.092)  ,  ForwardTime: 0.721  ,  TotalTime: 1.001  ,  Loss/total: 0.68234  ,  Loss/giou: 0.16657  ,  Loss/l1: 0.01499  ,  Loss/location: 0.27425  ,  IoU: 0.84511
[train: 37, 50 / 117] FPS: 63.9 (86.4)  ,  DataTime: 0.185 (0.089)  ,  ForwardTime: 0.727  ,  TotalTime: 1.001  ,  Loss/total: 0.72735  ,  Loss/giou: 0.17669  ,  Loss/l1: 0.01653  ,  Loss/location: 0.29133  ,  IoU: 0.83749
[train: 37, 50 / 117] FPS: 64.0 (86.5)  ,  DataTime: 0.197 (0.084)  ,  ForwardTime: 0.720  ,  TotalTime: 1.000  ,  Loss/total: 0.68940  ,  Loss/giou: 0.16674  ,  Loss/l1: 0.01465  ,  Loss/location: 0.28267  ,  IoU: 0.84344
[train: 37, 50 / 117] FPS: 64.0 (86.3)  ,  DataTime: 0.206 (0.087)  ,  ForwardTime: 0.707  ,  TotalTime: 1.000  ,  Loss/total: 0.70837  ,  Loss/giou: 0.17177  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28810  ,  IoU: 0.83992
[train: 37, 50 / 117] FPS: 64.0 (86.3)  ,  DataTime: 0.171 (0.090)  ,  ForwardTime: 0.739  ,  TotalTime: 1.001  ,  Loss/total: 0.70225  ,  Loss/giou: 0.17326  ,  Loss/l1: 0.01611  ,  Loss/location: 0.27519  ,  IoU: 0.84004
[train: 37, 50 / 117] FPS: 64.0 (86.3)  ,  DataTime: 0.205 (0.079)  ,  ForwardTime: 0.717  ,  TotalTime: 1.000  ,  Loss/total: 0.70413  ,  Loss/giou: 0.17033  ,  Loss/l1: 0.01508  ,  Loss/location: 0.28805  ,  IoU: 0.84112
[train: 37, 50 / 117] FPS: 64.0 (85.9)  ,  DataTime: 0.209 (0.095)  ,  ForwardTime: 0.697  ,  TotalTime: 1.001  ,  Loss/total: 0.72639  ,  Loss/giou: 0.17560  ,  Loss/l1: 0.01597  ,  Loss/location: 0.29535  ,  IoU: 0.83738
[train: 37, 100 / 117] FPS: 72.8 (106.3)  ,  DataTime: 0.122 (0.075)  ,  ForwardTime: 0.682  ,  TotalTime: 0.879  ,  Loss/total: 0.72745  ,  Loss/giou: 0.17662  ,  Loss/l1: 0.01622  ,  Loss/location: 0.29310  ,  IoU: 0.83648
[train: 37, 100 / 117] FPS: 72.8 (106.2)  ,  DataTime: 0.121 (0.085)  ,  ForwardTime: 0.674  ,  TotalTime: 0.880  ,  Loss/total: 0.71091  ,  Loss/giou: 0.17194  ,  Loss/l1: 0.01573  ,  Loss/location: 0.28837  ,  IoU: 0.84081
[train: 37, 100 / 117] FPS: 72.8 (106.5)  ,  DataTime: 0.122 (0.087)  ,  ForwardTime: 0.671  ,  TotalTime: 0.879  ,  Loss/total: 0.69480  ,  Loss/giou: 0.17105  ,  Loss/l1: 0.01561  ,  Loss/location: 0.27463  ,  IoU: 0.84155[train: 37, 100 / 117] FPS: 72.8 (106.5)  ,  DataTime: 0.125 (0.082)  ,  ForwardTime: 0.673  ,  TotalTime: 0.879  ,  Loss/total: 0.70948  ,  Loss/giou: 0.17081  ,  Loss/l1: 0.01508  ,  Loss/location: 0.29245  ,  IoU: 0.83995

[train: 37, 100 / 117] FPS: 72.8 (106.3)  ,  DataTime: 0.132 (0.085)  ,  ForwardTime: 0.663  ,  TotalTime: 0.879  ,  Loss/total: 0.72513  ,  Loss/giou: 0.17617  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29233  ,  IoU: 0.83696
[train: 37, 100 / 117] FPS: 72.8 (106.2)  ,  DataTime: 0.129 (0.082)  ,  ForwardTime: 0.669  ,  TotalTime: 0.879  ,  Loss/total: 0.71953  ,  Loss/giou: 0.17427  ,  Loss/l1: 0.01565  ,  Loss/location: 0.29275  ,  IoU: 0.83754
[train: 37, 100 / 117] FPS: 72.8 (106.5)  ,  DataTime: 0.113 (0.086)  ,  ForwardTime: 0.681  ,  TotalTime: 0.879  ,  Loss/total: 0.70207  ,  Loss/giou: 0.17310  ,  Loss/l1: 0.01575  ,  Loss/location: 0.27714  ,  IoU: 0.83944
[train: 37, 100 / 117] FPS: 72.8 (106.5)  ,  DataTime: 0.127 (0.075)  ,  ForwardTime: 0.677  ,  TotalTime: 0.879  ,  Loss/total: 0.70211  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01507  ,  Loss/location: 0.28578  ,  IoU: 0.84079
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.118 (0.079)  ,  ForwardTime: 0.640  ,  TotalTime: 0.836  ,  Loss/total: 0.72021  ,  Loss/giou: 0.17468  ,  Loss/l1: 0.01585  ,  Loss/location: 0.29161  ,  IoU: 0.83811[train: 37, 117 / 117] FPS: 76.5 (110.3)  ,  DataTime: 0.110 (0.071)  ,  ForwardTime: 0.656  ,  TotalTime: 0.837  ,  Loss/total: 0.72344  ,  Loss/giou: 0.17540  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29221  ,  IoU: 0.83760

[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.109 (0.081)  ,  ForwardTime: 0.646  ,  TotalTime: 0.837  ,  Loss/total: 0.69575  ,  Loss/giou: 0.17107  ,  Loss/l1: 0.01552  ,  Loss/location: 0.27601  ,  IoU: 0.84142
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.113 (0.071)  ,  ForwardTime: 0.652  ,  TotalTime: 0.836  ,  Loss/total: 0.71091  ,  Loss/giou: 0.17238  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28879  ,  IoU: 0.83957
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.108 (0.079)  ,  ForwardTime: 0.650  ,  TotalTime: 0.837  ,  Loss/total: 0.70910  ,  Loss/giou: 0.17134  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28864  ,  IoU: 0.84107
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.115 (0.077)  ,  ForwardTime: 0.644  ,  TotalTime: 0.836  ,  Loss/total: 0.71700  ,  Loss/giou: 0.17363  ,  Loss/l1: 0.01560  ,  Loss/location: 0.29175  ,  IoU: 0.83804
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.101 (0.080)  ,  ForwardTime: 0.656  ,  TotalTime: 0.837  ,  Loss/total: 0.70745  ,  Loss/giou: 0.17451  ,  Loss/l1: 0.01594  ,  Loss/location: 0.27875  ,  IoU: 0.83837
[train: 37, 117 / 117] FPS: 76.5 (110.2)  ,  DataTime: 0.112 (0.077)  ,  ForwardTime: 0.647  ,  TotalTime: 0.836  ,  Loss/total: 0.70527  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01502  ,  Loss/location: 0.28921  ,  IoU: 0.84040
Epoch Time: 0:01:37.860978
Avg Data Time: 0.11319
Avg GPU Trans Time: 0.07136
Avg Forward Time: 0.65187
Epoch Time: 0:01:37.873292
Avg Data Time: 0.10120
Avg GPU Trans Time: 0.07979
Avg Forward Time: 0.65554
Epoch Time: 0:01:37.876850
Avg Data Time: 0.10913
Avg GPU Trans Time: 0.08110
Avg Forward Time: 0.64632
Epoch Time: 0:01:37.865912
Avg Data Time: 0.11545
Avg GPU Trans Time: 0.07675
Avg Forward Time: 0.64426
Epoch Time: 0:01:37.872313
Avg Data Time: 0.10995
Avg GPU Trans Time: 0.07060
Avg Forward Time: 0.65596
Epoch Time: 0:01:37.869635
Avg Data Time: 0.11764
Avg GPU Trans Time: 0.07925
Avg Forward Time: 0.63960
Epoch Time: 0:01:37.868541
Avg Data Time: 0.11245
Avg GPU Trans Time: 0.07670
Avg Forward Time: 0.64734
Epoch Time: 0:01:37.905891
Avg Data Time: 0.10823
Avg GPU Trans Time: 0.07894
Avg Forward Time: 0.64964
[train: 38, 50 / 117] FPS: 63.3 (85.9)  ,  DataTime: 0.185 (0.083)  ,  ForwardTime: 0.742  ,  TotalTime: 1.011  ,  Loss/total: 0.68885  ,  Loss/giou: 0.16652  ,  Loss/l1: 0.01496  ,  Loss/location: 0.28101  ,  IoU: 0.84496
[train: 38, 50 / 117] FPS: 63.3 (85.9)  ,  DataTime: 0.181 (0.078)  ,  ForwardTime: 0.752  ,  TotalTime: 1.011  ,  Loss/total: 0.68814  ,  Loss/giou: 0.16763  ,  Loss/l1: 0.01514  ,  Loss/location: 0.27721  ,  IoU: 0.84392
[train: 38, 50 / 117] FPS: 63.3 (85.7)  ,  DataTime: 0.225 (0.083)  ,  ForwardTime: 0.704  ,  TotalTime: 1.011  ,  Loss/total: 0.69728  ,  Loss/giou: 0.16714  ,  Loss/l1: 0.01459  ,  Loss/location: 0.29003  ,  IoU: 0.84362
[train: 38, 50 / 117] FPS: 63.3 (85.8)  ,  DataTime: 0.250 (0.085)  ,  ForwardTime: 0.676  ,  TotalTime: 1.011  ,  Loss/total: 0.68130  ,  Loss/giou: 0.16670  ,  Loss/l1: 0.01469  ,  Loss/location: 0.27446  ,  IoU: 0.84436
[train: 38, 50 / 117] FPS: 63.3 (85.9)  ,  DataTime: 0.151 (0.081)  ,  ForwardTime: 0.779  ,  TotalTime: 1.011  ,  Loss/total: 0.68571  ,  Loss/giou: 0.16747  ,  Loss/l1: 0.01508  ,  Loss/location: 0.27538  ,  IoU: 0.84416
[train: 38, 50 / 117] FPS: 63.3 (86.0)  ,  DataTime: 0.216 (0.085)  ,  ForwardTime: 0.710  ,  TotalTime: 1.011  ,  Loss/total: 0.67777  ,  Loss/giou: 0.16498  ,  Loss/l1: 0.01439  ,  Loss/location: 0.27587  ,  IoU: 0.84514
[train: 38, 50 / 117] FPS: 63.3 (86.5)  ,  DataTime: 0.183 (0.079)  ,  ForwardTime: 0.749  ,  TotalTime: 1.011  ,  Loss/total: 0.70658  ,  Loss/giou: 0.17185  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28439  ,  IoU: 0.84075
[train: 38, 50 / 117] FPS: 63.3 (85.8)  ,  DataTime: 0.197 (0.079)  ,  ForwardTime: 0.735  ,  TotalTime: 1.011  ,  Loss/total: 0.69898  ,  Loss/giou: 0.16940  ,  Loss/l1: 0.01514  ,  Loss/location: 0.28450  ,  IoU: 0.84240
[train: 38, 100 / 117] FPS: 72.3 (106.2)  ,  DataTime: 0.121 (0.082)  ,  ForwardTime: 0.683  ,  TotalTime: 0.886  ,  Loss/total: 0.70159  ,  Loss/giou: 0.16858  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28860  ,  IoU: 0.84310
[train: 38, 100 / 117] FPS: 72.3 (106.2)  ,  DataTime: 0.118 (0.071)  ,  ForwardTime: 0.697  ,  TotalTime: 0.886  ,  Loss/total: 0.71330  ,  Loss/giou: 0.17313  ,  Loss/l1: 0.01600  ,  Loss/location: 0.28705  ,  IoU: 0.83965
[train: 38, 100 / 117] FPS: 72.3 (106.2)  ,  DataTime: 0.117 (0.074)  ,  ForwardTime: 0.694  ,  TotalTime: 0.886  ,  Loss/total: 0.70082  ,  Loss/giou: 0.16964  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28453  ,  IoU: 0.84226
[train: 38, 100 / 117] FPS: 72.2 (106.1)  ,  DataTime: 0.140 (0.082)  ,  ForwardTime: 0.663  ,  TotalTime: 0.886  ,  Loss/total: 0.70517  ,  Loss/giou: 0.17103  ,  Loss/l1: 0.01530  ,  Loss/location: 0.28660  ,  IoU: 0.84089
[train: 38, 100 / 117] FPS: 72.3 (106.4)  ,  DataTime: 0.153 (0.083)  ,  ForwardTime: 0.650  ,  TotalTime: 0.886  ,  Loss/total: 0.69235  ,  Loss/giou: 0.16868  ,  Loss/l1: 0.01490  ,  Loss/location: 0.28051  ,  IoU: 0.84249
[train: 38, 100 / 117] FPS: 72.3 (106.5)  ,  DataTime: 0.135 (0.083)  ,  ForwardTime: 0.668  ,  TotalTime: 0.886  ,  Loss/total: 0.70265  ,  Loss/giou: 0.17054  ,  Loss/l1: 0.01518  ,  Loss/location: 0.28569  ,  IoU: 0.84089
[train: 38, 100 / 117] FPS: 72.3 (106.0)  ,  DataTime: 0.126 (0.077)  ,  ForwardTime: 0.682  ,  TotalTime: 0.886  ,  Loss/total: 0.70549  ,  Loss/giou: 0.17166  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28484  ,  IoU: 0.84076
[train: 38, 100 / 117] FPS: 72.3 (105.9)  ,  DataTime: 0.102 (0.078)  ,  ForwardTime: 0.705  ,  TotalTime: 0.886  ,  Loss/total: 0.69673  ,  Loss/giou: 0.17078  ,  Loss/l1: 0.01532  ,  Loss/location: 0.27857  ,  IoU: 0.84068
[train: 38, 117 / 117] FPS: 76.0 (110.7)  ,  DataTime: 0.106 (0.066)  ,  ForwardTime: 0.670  ,  TotalTime: 0.842  ,  Loss/total: 0.71825  ,  Loss/giou: 0.17399  ,  Loss/l1: 0.01611  ,  Loss/location: 0.28970  ,  IoU: 0.83897
[train: 38, 117 / 117] FPS: 76.0 (110.6)  ,  DataTime: 0.120 (0.078)  ,  ForwardTime: 0.644  ,  TotalTime: 0.842  ,  Loss/total: 0.70007  ,  Loss/giou: 0.17045  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28309  ,  IoU: 0.84093
[train: 38, 117 / 117] FPS: 76.0 (110.6)  ,  DataTime: 0.105 (0.070)  ,  ForwardTime: 0.667  ,  TotalTime: 0.842  ,  Loss/total: 0.69409  ,  Loss/giou: 0.16843  ,  Loss/l1: 0.01519  ,  Loss/location: 0.28127  ,  IoU: 0.84307
[train: 38, 117 / 117] FPS: 76.0 (110.7)  ,  DataTime: 0.135 (0.078)  ,  ForwardTime: 0.629  ,  TotalTime: 0.842  ,  Loss/total: 0.68867  ,  Loss/giou: 0.16781  ,  Loss/l1: 0.01486  ,  Loss/location: 0.27876  ,  IoU: 0.84335
[train: 38, 117 / 117] FPS: 76.0 (110.6)  ,  DataTime: 0.125 (0.078)  ,  ForwardTime: 0.640  ,  TotalTime: 0.842  ,  Loss/total: 0.70723  ,  Loss/giou: 0.17184  ,  Loss/l1: 0.01543  ,  Loss/location: 0.28638  ,  IoU: 0.84025
[train: 38, 117 / 117] FPS: 76.0 (110.6)  ,  DataTime: 0.108 (0.077)  ,  ForwardTime: 0.657  ,  TotalTime: 0.842  ,  Loss/total: 0.70614  ,  Loss/giou: 0.16972  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28983  ,  IoU: 0.84235
[train: 38, 117 / 117] FPS: 76.0 (110.7)  ,  DataTime: 0.113 (0.073)  ,  ForwardTime: 0.656  ,  TotalTime: 0.842  ,  Loss/total: 0.70451  ,  Loss/giou: 0.17093  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28559  ,  IoU: 0.84129
[train: 38, 117 / 117] FPS: 76.0 (110.8)  ,  DataTime: 0.092 (0.074)  ,  ForwardTime: 0.676  ,  TotalTime: 0.842  ,  Loss/total: 0.69845  ,  Loss/giou: 0.17114  ,  Loss/l1: 0.01544  ,  Loss/location: 0.27897  ,  IoU: 0.84060
Epoch Time: 0:01:38.548522
Avg Data Time: 0.12473
Avg GPU Trans Time: 0.07774
Avg Forward Time: 0.63983
Epoch Time: 0:01:38.531273
Avg Data Time: 0.12048
Avg GPU Trans Time: 0.07767
Avg Forward Time: 0.64399
Epoch Time: 0:01:38.535563
Avg Data Time: 0.10514
Avg GPU Trans Time: 0.07003
Avg Forward Time: 0.66702
Epoch Time: 0:01:38.545258
Avg Data Time: 0.13535
Avg GPU Trans Time: 0.07797
Avg Forward Time: 0.62894
Epoch Time: 0:01:38.522292
Avg Data Time: 0.11315
Avg GPU Trans Time: 0.07294
Avg Forward Time: 0.65599
Epoch Time: 0:01:38.544191
Avg Data Time: 0.09247
Avg GPU Trans Time: 0.07403
Avg Forward Time: 0.67575
Epoch Time: 0:01:38.543629
Avg Data Time: 0.10788
Avg GPU Trans Time: 0.07726
Avg Forward Time: 0.65712
Epoch Time: 0:01:38.532711
Avg Data Time: 0.10592
Avg GPU Trans Time: 0.06616
Avg Forward Time: 0.67008
[train: 39, 50 / 117] FPS: 63.8 (86.1)  ,  DataTime: 0.216 (0.085)  ,  ForwardTime: 0.701  ,  TotalTime: 1.003  ,  Loss/total: 0.71069  ,  Loss/giou: 0.17167  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28957  ,  IoU: 0.84075
[train: 39, 50 / 117] FPS: 63.8 (85.9)  ,  DataTime: 0.188 (0.085)  ,  ForwardTime: 0.730  ,  TotalTime: 1.003  ,  Loss/total: 0.68003  ,  Loss/giou: 0.16529  ,  Loss/l1: 0.01477  ,  Loss/location: 0.27561  ,  IoU: 0.84517
[train: 39, 50 / 117] FPS: 63.7 (86.0)  ,  DataTime: 0.191 (0.088)  ,  ForwardTime: 0.725  ,  TotalTime: 1.004  ,  Loss/total: 0.67046  ,  Loss/giou: 0.16393  ,  Loss/l1: 0.01508  ,  Loss/location: 0.26720  ,  IoU: 0.84783
[train: 39, 50 / 117] FPS: 63.8 (85.6)  ,  DataTime: 0.221 (0.086)  ,  ForwardTime: 0.696  ,  TotalTime: 1.003  ,  Loss/total: 0.68821  ,  Loss/giou: 0.16562  ,  Loss/l1: 0.01502  ,  Loss/location: 0.28189  ,  IoU: 0.84558
[train: 39, 50 / 117] FPS: 63.8 (85.6)  ,  DataTime: 0.189 (0.074)  ,  ForwardTime: 0.741  ,  TotalTime: 1.003  ,  Loss/total: 0.71225  ,  Loss/giou: 0.17495  ,  Loss/l1: 0.01586  ,  Loss/location: 0.28305  ,  IoU: 0.83791
[train: 39, 50 / 117] FPS: 63.9 (86.0)  ,  DataTime: 0.190 (0.082)  ,  ForwardTime: 0.731  ,  TotalTime: 1.002  ,  Loss/total: 0.72004  ,  Loss/giou: 0.17468  ,  Loss/l1: 0.01627  ,  Loss/location: 0.28932  ,  IoU: 0.83931
[train: 39, 50 / 117] FPS: 63.8 (85.5)  ,  DataTime: 0.207 (0.084)  ,  ForwardTime: 0.713  ,  TotalTime: 1.004  ,  Loss/total: 0.72435  ,  Loss/giou: 0.17584  ,  Loss/l1: 0.01608  ,  Loss/location: 0.29230  ,  IoU: 0.83741
[train: 39, 50 / 117] FPS: 63.8 (85.2)  ,  DataTime: 0.221 (0.074)  ,  ForwardTime: 0.708  ,  TotalTime: 1.003  ,  Loss/total: 0.70414  ,  Loss/giou: 0.17370  ,  Loss/l1: 0.01602  ,  Loss/location: 0.27666  ,  IoU: 0.83955
[train: 39, 100 / 117] FPS: 72.9 (109.1)  ,  DataTime: 0.122 (0.083)  ,  ForwardTime: 0.673  ,  TotalTime: 0.878  ,  Loss/total: 0.68794  ,  Loss/giou: 0.16868  ,  Loss/l1: 0.01552  ,  Loss/location: 0.27298  ,  IoU: 0.84384
[train: 39, 100 / 117] FPS: 72.9 (109.1)  ,  DataTime: 0.129 (0.082)  ,  ForwardTime: 0.666  ,  TotalTime: 0.877  ,  Loss/total: 0.72335  ,  Loss/giou: 0.17582  ,  Loss/l1: 0.01614  ,  Loss/location: 0.29100  ,  IoU: 0.83736
[train: 39, 100 / 117] FPS: 73.0 (109.1)  ,  DataTime: 0.137 (0.083)  ,  ForwardTime: 0.657  ,  TotalTime: 0.877  ,  Loss/total: 0.69032  ,  Loss/giou: 0.16665  ,  Loss/l1: 0.01518  ,  Loss/location: 0.28112  ,  IoU: 0.84492
[train: 39, 100 / 117] FPS: 73.0 (109.6)  ,  DataTime: 0.135 (0.082)  ,  ForwardTime: 0.660  ,  TotalTime: 0.877  ,  Loss/total: 0.71020  ,  Loss/giou: 0.17141  ,  Loss/l1: 0.01542  ,  Loss/location: 0.29025  ,  IoU: 0.84039
[train: 39, 100 / 117] FPS: 73.0 (109.1)  ,  DataTime: 0.121 (0.081)  ,  ForwardTime: 0.674  ,  TotalTime: 0.877  ,  Loss/total: 0.70216  ,  Loss/giou: 0.17001  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28401  ,  IoU: 0.84175
[train: 39, 100 / 117] FPS: 73.0 (109.1)  ,  DataTime: 0.120 (0.069)  ,  ForwardTime: 0.688  ,  TotalTime: 0.877  ,  Loss/total: 0.71427  ,  Loss/giou: 0.17383  ,  Loss/l1: 0.01588  ,  Loss/location: 0.28723  ,  IoU: 0.83889
[train: 39, 100 / 117] FPS: 73.0 (109.2)  ,  DataTime: 0.120 (0.075)  ,  ForwardTime: 0.682  ,  TotalTime: 0.877  ,  Loss/total: 0.71594  ,  Loss/giou: 0.17533  ,  Loss/l1: 0.01606  ,  Loss/location: 0.28496  ,  IoU: 0.83780
[train: 39, 100 / 117] FPS: 73.0 (109.3)  ,  DataTime: 0.137 (0.072)  ,  ForwardTime: 0.668  ,  TotalTime: 0.877  ,  Loss/total: 0.69466  ,  Loss/giou: 0.17120  ,  Loss/l1: 0.01559  ,  Loss/location: 0.27432  ,  IoU: 0.84149
[train: 39, 117 / 117] FPS: 76.7 (109.2)  ,  DataTime: 0.122 (0.077)  ,  ForwardTime: 0.634  ,  TotalTime: 0.834  ,  Loss/total: 0.69259  ,  Loss/giou: 0.16713  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28254  ,  IoU: 0.84418
[train: 39, 117 / 117] FPS: 76.7 (109.1)  ,  DataTime: 0.109 (0.078)  ,  ForwardTime: 0.648  ,  TotalTime: 0.835  ,  Loss/total: 0.68657  ,  Loss/giou: 0.16789  ,  Loss/l1: 0.01530  ,  Loss/location: 0.27425  ,  IoU: 0.84419
[train: 39, 117 / 117] FPS: 76.7 (109.1)  ,  DataTime: 0.115 (0.077)  ,  ForwardTime: 0.642  ,  TotalTime: 0.835  ,  Loss/total: 0.72182  ,  Loss/giou: 0.17583  ,  Loss/l1: 0.01611  ,  Loss/location: 0.28959  ,  IoU: 0.83737
[train: 39, 117 / 117] FPS: 76.7 (109.1)  ,  DataTime: 0.108 (0.077)  ,  ForwardTime: 0.649  ,  TotalTime: 0.834  ,  Loss/total: 0.70212  ,  Loss/giou: 0.17025  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28356  ,  IoU: 0.84148
[train: 39, 117 / 117] FPS: 76.7 (109.2)  ,  DataTime: 0.108 (0.066)  ,  ForwardTime: 0.661  ,  TotalTime: 0.834  ,  Loss/total: 0.71879  ,  Loss/giou: 0.17500  ,  Loss/l1: 0.01598  ,  Loss/location: 0.28888  ,  IoU: 0.83801
[train: 39, 117 / 117] FPS: 76.7 (109.1)  ,  DataTime: 0.122 (0.068)  ,  ForwardTime: 0.644  ,  TotalTime: 0.834  ,  Loss/total: 0.70262  ,  Loss/giou: 0.17249  ,  Loss/l1: 0.01580  ,  Loss/location: 0.27862  ,  IoU: 0.84056
[train: 39, 117 / 117] FPS: 76.8 (109.1)  ,  DataTime: 0.108 (0.070)  ,  ForwardTime: 0.656  ,  TotalTime: 0.834  ,  Loss/total: 0.70903  ,  Loss/giou: 0.17382  ,  Loss/l1: 0.01582  ,  Loss/location: 0.28229  ,  IoU: 0.83886
[train: 39, 117 / 117] FPS: 76.7 (109.1)  ,  DataTime: 0.120 (0.077)  ,  ForwardTime: 0.637  ,  TotalTime: 0.834  ,  Loss/total: 0.71267  ,  Loss/giou: 0.17254  ,  Loss/l1: 0.01562  ,  Loss/location: 0.28949  ,  IoU: 0.83969
Epoch Time: 0:01:37.637383
Avg Data Time: 0.11527
Avg GPU Trans Time: 0.07694
Avg Forward Time: 0.64230
Epoch Time: 0:01:37.575600
Avg Data Time: 0.12244
Avg GPU Trans Time: 0.07739
Avg Forward Time: 0.63415
Epoch Time: 0:01:37.590830
Avg Data Time: 0.12017
Avg GPU Trans Time: 0.07738
Avg Forward Time: 0.63656
Epoch Time: 0:01:37.594286
Avg Data Time: 0.10830
Avg GPU Trans Time: 0.07650
Avg Forward Time: 0.64934
Epoch Time: 0:01:37.663888
Avg Data Time: 0.10902
Avg GPU Trans Time: 0.07757
Avg Forward Time: 0.64815
Epoch Time: 0:01:37.589012
Avg Data Time: 0.12202
Avg GPU Trans Time: 0.06801
Avg Forward Time: 0.64407
Epoch Time: 0:01:37.596258
Avg Data Time: 0.10765
Avg GPU Trans Time: 0.06552
Avg Forward Time: 0.66098
Epoch Time: 0:01:37.562422
Avg Data Time: 0.10783
Avg GPU Trans Time: 0.07019
Avg Forward Time: 0.65585
[train: 40, 50 / 117] FPS: 63.8 (82.7)  ,  DataTime: 0.199 (0.080)  ,  ForwardTime: 0.724  ,  TotalTime: 1.003  ,  Loss/total: 0.72548  ,  Loss/giou: 0.17523  ,  Loss/l1: 0.01626  ,  Loss/location: 0.29372  ,  IoU: 0.83784
[train: 40, 50 / 117] FPS: 63.8 (82.8)  ,  DataTime: 0.179 (0.086)  ,  ForwardTime: 0.739  ,  TotalTime: 1.004  ,  Loss/total: 0.73481  ,  Loss/giou: 0.17654  ,  Loss/l1: 0.01615  ,  Loss/location: 0.30097  ,  IoU: 0.83658
[train: 40, 50 / 117] FPS: 63.8 (82.6)  ,  DataTime: 0.211 (0.079)  ,  ForwardTime: 0.713  ,  TotalTime: 1.003  ,  Loss/total: 0.70964  ,  Loss/giou: 0.17396  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28285  ,  IoU: 0.83863
[train: 40, 50 / 117] FPS: 63.8 (82.7)  ,  DataTime: 0.205 (0.084)  ,  ForwardTime: 0.713  ,  TotalTime: 1.002  ,  Loss/total: 0.69974  ,  Loss/giou: 0.16936  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28390  ,  IoU: 0.84245
[train: 40, 50 / 117] FPS: 63.8 (82.6)  ,  DataTime: 0.191 (0.084)  ,  ForwardTime: 0.728  ,  TotalTime: 1.003  ,  Loss/total: 0.73489  ,  Loss/giou: 0.17816  ,  Loss/l1: 0.01672  ,  Loss/location: 0.29497  ,  IoU: 0.83568
[train: 40, 50 / 117] FPS: 63.8 (82.4)  ,  DataTime: 0.191 (0.084)  ,  ForwardTime: 0.728  ,  TotalTime: 1.003  ,  Loss/total: 0.70410  ,  Loss/giou: 0.17154  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28280  ,  IoU: 0.84097
[train: 40, 50 / 117] FPS: 63.9 (82.6)  ,  DataTime: 0.235 (0.078)  ,  ForwardTime: 0.689  ,  TotalTime: 1.002  ,  Loss/total: 0.72092  ,  Loss/giou: 0.17510  ,  Loss/l1: 0.01627  ,  Loss/location: 0.28935  ,  IoU: 0.83811
[train: 40, 50 / 117] FPS: 63.8 (82.1)  ,  DataTime: 0.194 (0.080)  ,  ForwardTime: 0.729  ,  TotalTime: 1.003  ,  Loss/total: 0.70074  ,  Loss/giou: 0.17046  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28253  ,  IoU: 0.84165
[train: 40, 100 / 117] FPS: 72.8 (107.0)  ,  DataTime: 0.115 (0.082)  ,  ForwardTime: 0.682  ,  TotalTime: 0.879  ,  Loss/total: 0.72694  ,  Loss/giou: 0.17445  ,  Loss/l1: 0.01583  ,  Loss/location: 0.29891  ,  IoU: 0.83803
[train: 40, 100 / 117] FPS: 72.9 (106.7)  ,  DataTime: 0.122 (0.081)  ,  ForwardTime: 0.676  ,  TotalTime: 0.878  ,  Loss/total: 0.70702  ,  Loss/giou: 0.17131  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28636  ,  IoU: 0.84062[train: 40, 100 / 117] FPS: 72.9 (107.0)  ,  DataTime: 0.125 (0.080)  ,  ForwardTime: 0.673  ,  TotalTime: 0.878  ,  Loss/total: 0.70703  ,  Loss/giou: 0.17162  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28582  ,  IoU: 0.84099[train: 40, 100 / 117] FPS: 72.9 (106.8)  ,  DataTime: 0.123 (0.082)  ,  ForwardTime: 0.673  ,  TotalTime: 0.879  ,  Loss/total: 0.69538  ,  Loss/giou: 0.16966  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27954  ,  IoU: 0.84258


[train: 40, 100 / 117] FPS: 72.9 (106.9)  ,  DataTime: 0.123 (0.075)  ,  ForwardTime: 0.680  ,  TotalTime: 0.878  ,  Loss/total: 0.69289  ,  Loss/giou: 0.16902  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28011  ,  IoU: 0.84202
[train: 40, 100 / 117] FPS: 72.9 (106.7)  ,  DataTime: 0.128 (0.081)  ,  ForwardTime: 0.668  ,  TotalTime: 0.878  ,  Loss/total: 0.71460  ,  Loss/giou: 0.17427  ,  Loss/l1: 0.01584  ,  Loss/location: 0.28686  ,  IoU: 0.83832
[train: 40, 100 / 117] FPS: 72.9 (106.7)  ,  DataTime: 0.131 (0.073)  ,  ForwardTime: 0.674  ,  TotalTime: 0.878  ,  Loss/total: 0.70523  ,  Loss/giou: 0.17272  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28238  ,  IoU: 0.83958
[train: 40, 100 / 117] FPS: 72.9 (107.0)  ,  DataTime: 0.145 (0.076)  ,  ForwardTime: 0.657  ,  TotalTime: 0.878  ,  Loss/total: 0.70981  ,  Loss/giou: 0.17241  ,  Loss/l1: 0.01596  ,  Loss/location: 0.28518  ,  IoU: 0.84021
[train: 40, 117 / 117] FPS: 76.6 (110.5)  ,  DataTime: 0.110 (0.077)  ,  ForwardTime: 0.648  ,  TotalTime: 0.835  ,  Loss/total: 0.70130  ,  Loss/giou: 0.17041  ,  Loss/l1: 0.01544  ,  Loss/location: 0.28330  ,  IoU: 0.84205
[train: 40, 117 / 117] FPS: 76.6 (110.5)  ,  DataTime: 0.112 (0.075)  ,  ForwardTime: 0.648  ,  TotalTime: 0.835  ,  Loss/total: 0.70346  ,  Loss/giou: 0.17043  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28579  ,  IoU: 0.84177[train: 40, 117 / 117] FPS: 76.6 (110.5)  ,  DataTime: 0.103 (0.077)  ,  ForwardTime: 0.656  ,  TotalTime: 0.836  ,  Loss/total: 0.73337  ,  Loss/giou: 0.17630  ,  Loss/l1: 0.01624  ,  Loss/location: 0.29957  ,  IoU: 0.83683

[train: 40, 117 / 117] FPS: 76.6 (110.5)  ,  DataTime: 0.109 (0.076)  ,  ForwardTime: 0.650  ,  TotalTime: 0.835  ,  Loss/total: 0.71671  ,  Loss/giou: 0.17295  ,  Loss/l1: 0.01595  ,  Loss/location: 0.29105  ,  IoU: 0.83958
[train: 40, 117 / 117] FPS: 76.6 (110.6)  ,  DataTime: 0.129 (0.072)  ,  ForwardTime: 0.634  ,  TotalTime: 0.835  ,  Loss/total: 0.70612  ,  Loss/giou: 0.17147  ,  Loss/l1: 0.01582  ,  Loss/location: 0.28408  ,  IoU: 0.84099
[train: 40, 117 / 117] FPS: 76.6 (110.5)  ,  DataTime: 0.116 (0.069)  ,  ForwardTime: 0.650  ,  TotalTime: 0.835  ,  Loss/total: 0.70662  ,  Loss/giou: 0.17323  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28272  ,  IoU: 0.83909
[train: 40, 117 / 117] FPS: 76.6 (110.4)  ,  DataTime: 0.115 (0.077)  ,  ForwardTime: 0.643  ,  TotalTime: 0.835  ,  Loss/total: 0.71238  ,  Loss/giou: 0.17367  ,  Loss/l1: 0.01575  ,  Loss/location: 0.28630  ,  IoU: 0.83881
[train: 40, 117 / 117] FPS: 76.6 (110.4)  ,  DataTime: 0.110 (0.071)  ,  ForwardTime: 0.654  ,  TotalTime: 0.835  ,  Loss/total: 0.69541  ,  Loss/giou: 0.16912  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28194  ,  IoU: 0.84216
Epoch Time: 0:01:37.777178
Avg Data Time: 0.10269
Avg GPU Trans Time: 0.07704
Avg Forward Time: 0.65598
Epoch Time: 0:01:37.729224
Avg Data Time: 0.11203
Avg GPU Trans Time: 0.07529
Avg Forward Time: 0.64797
Epoch Time: 0:01:37.743114
Avg Data Time: 0.11009
Avg GPU Trans Time: 0.07690
Avg Forward Time: 0.64842
Epoch Time: 0:01:37.714829
Avg Data Time: 0.10858
Avg GPU Trans Time: 0.07615
Avg Forward Time: 0.65044
Epoch Time: 0:01:37.706745
Avg Data Time: 0.11477
Avg GPU Trans Time: 0.07696
Avg Forward Time: 0.64337
Epoch Time: 0:01:37.739208
Avg Data Time: 0.11627
Avg GPU Trans Time: 0.06900
Avg Forward Time: 0.65011
Epoch Time: 0:01:37.694095
Avg Data Time: 0.12881
Avg GPU Trans Time: 0.07187
Avg Forward Time: 0.63431
Epoch Time: 0:01:37.711766
Avg Data Time: 0.10975
Avg GPU Trans Time: 0.07105
Avg Forward Time: 0.65434
[val: 40, 50 / 78] FPS: 31.5 (214.6)  ,  DataTime: 1.622 (0.045)  ,  ForwardTime: 0.362  ,  TotalTime: 2.029  ,  Loss/total: 0.82691  ,  Loss/giou: 0.19817  ,  Loss/l1: 0.02157  ,  Loss/location: 0.32271  ,  IoU: 0.82461
[val: 40, 50 / 78] FPS: 29.3 (189.4)  ,  DataTime: 1.825 (0.041)  ,  ForwardTime: 0.317  ,  TotalTime: 2.183  ,  Loss/total: 0.82601  ,  Loss/giou: 0.19825  ,  Loss/l1: 0.02062  ,  Loss/location: 0.32643  ,  IoU: 0.82158
[val: 40, 50 / 78] FPS: 28.8 (201.2)  ,  DataTime: 1.948 (0.044)  ,  ForwardTime: 0.228  ,  TotalTime: 2.220  ,  Loss/total: 0.81477  ,  Loss/giou: 0.19527  ,  Loss/l1: 0.02075  ,  Loss/location: 0.32047  ,  IoU: 0.82503
[val: 40, 50 / 78] FPS: 28.7 (192.4)  ,  DataTime: 1.853 (0.046)  ,  ForwardTime: 0.328  ,  TotalTime: 2.226  ,  Loss/total: 0.82655  ,  Loss/giou: 0.20067  ,  Loss/l1: 0.02125  ,  Loss/location: 0.31895  ,  IoU: 0.82092
[val: 40, 50 / 78] FPS: 28.4 (202.8)  ,  DataTime: 1.915 (0.041)  ,  ForwardTime: 0.298  ,  TotalTime: 2.255  ,  Loss/total: 0.85811  ,  Loss/giou: 0.20548  ,  Loss/l1: 0.02213  ,  Loss/location: 0.33649  ,  IoU: 0.81698
[val: 40, 50 / 78] FPS: 28.2 (202.2)  ,  DataTime: 1.996 (0.046)  ,  ForwardTime: 0.231  ,  TotalTime: 2.273  ,  Loss/total: 0.86484  ,  Loss/giou: 0.20582  ,  Loss/l1: 0.02131  ,  Loss/location: 0.34662  ,  IoU: 0.81572
[val: 40, 50 / 78] FPS: 27.3 (194.2)  ,  DataTime: 2.069 (0.047)  ,  ForwardTime: 0.232  ,  TotalTime: 2.348  ,  Loss/total: 0.84121  ,  Loss/giou: 0.20215  ,  Loss/l1: 0.02134  ,  Loss/location: 0.33018  ,  IoU: 0.81962
[val: 40, 50 / 78] FPS: 27.2 (168.4)  ,  DataTime: 2.076 (0.047)  ,  ForwardTime: 0.234  ,  TotalTime: 2.357  ,  Loss/total: 0.82424  ,  Loss/giou: 0.19626  ,  Loss/l1: 0.02075  ,  Loss/location: 0.32797  ,  IoU: 0.82368
[val: 40, 78 / 78] FPS: 34.2 (88.9)  ,  DataTime: 1.533 (0.046)  ,  ForwardTime: 0.293  ,  TotalTime: 1.873  ,  Loss/total: 0.82707  ,  Loss/giou: 0.19916  ,  Loss/l1: 0.02102  ,  Loss/location: 0.32367  ,  IoU: 0.82223
Epoch Time: 0:02:26.106710
Avg Data Time: 1.53347
Avg GPU Trans Time: 0.04627
Avg Forward Time: 0.29343
[val: 40, 78 / 78] FPS: 34.1 (218.2)  ,  DataTime: 1.516 (0.046)  ,  ForwardTime: 0.315  ,  TotalTime: 1.876  ,  Loss/total: 0.82920  ,  Loss/giou: 0.19854  ,  Loss/l1: 0.02154  ,  Loss/location: 0.32441  ,  IoU: 0.82421
Epoch Time: 0:02:26.362365
Avg Data Time: 1.51570
Avg GPU Trans Time: 0.04617
Avg Forward Time: 0.31457
[val: 40, 78 / 78] FPS: 33.9 (221.0)  ,  DataTime: 1.572 (0.041)  ,  ForwardTime: 0.273  ,  TotalTime: 1.886  ,  Loss/total: 0.86176  ,  Loss/giou: 0.20814  ,  Loss/l1: 0.02260  ,  Loss/location: 0.33249  ,  IoU: 0.81562
[val: 40, 78 / 78] FPS: 33.9 (232.6)  ,  DataTime: 1.618 (0.043)  ,  ForwardTime: 0.227  ,  TotalTime: 1.888  ,  Loss/total: 0.80964  ,  Loss/giou: 0.19524  ,  Loss/l1: 0.02075  ,  Loss/location: 0.31542  ,  IoU: 0.82522
Epoch Time: 0:02:27.130949
Avg Data Time: 1.57229
Avg GPU Trans Time: 0.04139
Avg Forward Time: 0.27262
Epoch Time: 0:02:27.259161
Avg Data Time: 1.61846
Avg GPU Trans Time: 0.04293
Avg Forward Time: 0.22654
[val: 40, 78 / 78] FPS: 33.7 (183.3)  ,  DataTime: 1.621 (0.045)  ,  ForwardTime: 0.232  ,  TotalTime: 1.898  ,  Loss/total: 0.85396  ,  Loss/giou: 0.20491  ,  Loss/l1: 0.02159  ,  Loss/location: 0.33622  ,  IoU: 0.81675
[val: 40, 78 / 78] FPS: 33.7 (169.4)  ,  DataTime: 1.570 (0.042)  ,  ForwardTime: 0.287  ,  TotalTime: 1.900  ,  Loss/total: 0.81690  ,  Loss/giou: 0.19630  ,  Loss/l1: 0.02040  ,  Loss/location: 0.32231  ,  IoU: 0.82336
Epoch Time: 0:02:28.010291
Avg Data Time: 1.62059
Avg GPU Trans Time: 0.04508
Avg Forward Time: 0.23190
Epoch Time: 0:02:28.163447
Avg Data Time: 1.57002
Avg GPU Trans Time: 0.04204
Avg Forward Time: 0.28747
[val: 40, 78 / 78] FPS: 33.6 (179.1)  ,  DataTime: 1.630 (0.046)  ,  ForwardTime: 0.231  ,  TotalTime: 1.907  ,  Loss/total: 0.84434  ,  Loss/giou: 0.20301  ,  Loss/l1: 0.02176  ,  Loss/location: 0.32950  ,  IoU: 0.81933
Epoch Time: 0:02:28.771761
Avg Data Time: 1.62991
Avg GPU Trans Time: 0.04610
Avg Forward Time: 0.23132
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 40, 78 / 78] FPS: 33.3 (175.9)  ,  DataTime: 1.642 (0.047)  ,  ForwardTime: 0.233  ,  TotalTime: 1.921  ,  Loss/total: 0.82993  ,  Loss/giou: 0.19909  ,  Loss/l1: 0.02110  ,  Loss/location: 0.32626  ,  IoU: 0.82179
Epoch Time: 0:02:29.866108
Avg Data Time: 1.64162
Avg GPU Trans Time: 0.04668
Avg Forward Time: 0.23306
[train: 41, 50 / 117] FPS: 68.0 (85.1)  ,  DataTime: 0.177 (0.080)  ,  ForwardTime: 0.684  ,  TotalTime: 0.942  ,  Loss/total: 0.71882  ,  Loss/giou: 0.17379  ,  Loss/l1: 0.01531  ,  Loss/location: 0.29469  ,  IoU: 0.83699
[train: 41, 50 / 117] FPS: 62.8 (84.9)  ,  DataTime: 0.134 (0.082)  ,  ForwardTime: 0.802  ,  TotalTime: 1.019  ,  Loss/total: 0.70169  ,  Loss/giou: 0.17153  ,  Loss/l1: 0.01604  ,  Loss/location: 0.27842  ,  IoU: 0.84170
[train: 41, 50 / 117] FPS: 64.3 (84.6)  ,  DataTime: 0.152 (0.078)  ,  ForwardTime: 0.766  ,  TotalTime: 0.996  ,  Loss/total: 0.71623  ,  Loss/giou: 0.17347  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28933  ,  IoU: 0.83950
[train: 41, 50 / 117] FPS: 65.3 (84.3)  ,  DataTime: 0.190 (0.083)  ,  ForwardTime: 0.707  ,  TotalTime: 0.980  ,  Loss/total: 0.70497  ,  Loss/giou: 0.17001  ,  Loss/l1: 0.01519  ,  Loss/location: 0.28902  ,  IoU: 0.84118
[train: 41, 50 / 117] FPS: 64.2 (84.6)  ,  DataTime: 0.174 (0.079)  ,  ForwardTime: 0.744  ,  TotalTime: 0.997  ,  Loss/total: 0.69388  ,  Loss/giou: 0.16835  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28361  ,  IoU: 0.84253
[train: 41, 50 / 117] FPS: 68.1 (84.4)  ,  DataTime: 0.218 (0.075)  ,  ForwardTime: 0.648  ,  TotalTime: 0.940  ,  Loss/total: 0.69032  ,  Loss/giou: 0.16801  ,  Loss/l1: 0.01460  ,  Loss/location: 0.28129  ,  IoU: 0.84234
[train: 41, 50 / 117] FPS: 65.6 (84.6)  ,  DataTime: 0.198 (0.076)  ,  ForwardTime: 0.701  ,  TotalTime: 0.975  ,  Loss/total: 0.69334  ,  Loss/giou: 0.16674  ,  Loss/l1: 0.01452  ,  Loss/location: 0.28728  ,  IoU: 0.84363
[train: 41, 50 / 117] FPS: 63.2 (84.6)  ,  DataTime: 0.146 (0.079)  ,  ForwardTime: 0.788  ,  TotalTime: 1.013  ,  Loss/total: 0.69872  ,  Loss/giou: 0.16851  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28550  ,  IoU: 0.84318
[train: 41, 100 / 117] FPS: 73.7 (98.7)  ,  DataTime: 0.123 (0.082)  ,  ForwardTime: 0.664  ,  TotalTime: 0.869  ,  Loss/total: 0.69915  ,  Loss/giou: 0.16883  ,  Loss/l1: 0.01479  ,  Loss/location: 0.28755  ,  IoU: 0.84193
[train: 41, 100 / 117] FPS: 73.0 (98.8)  ,  DataTime: 0.112 (0.077)  ,  ForwardTime: 0.688  ,  TotalTime: 0.877  ,  Loss/total: 0.69568  ,  Loss/giou: 0.16916  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28207  ,  IoU: 0.84218
[train: 41, 100 / 117] FPS: 72.3 (98.8)  ,  DataTime: 0.101 (0.078)  ,  ForwardTime: 0.706  ,  TotalTime: 0.885  ,  Loss/total: 0.70426  ,  Loss/giou: 0.17070  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28544  ,  IoU: 0.84137[train: 41, 100 / 117] FPS: 72.1 (98.7)  ,  DataTime: 0.097 (0.082)  ,  ForwardTime: 0.709  ,  TotalTime: 0.888  ,  Loss/total: 0.71025  ,  Loss/giou: 0.17278  ,  Loss/l1: 0.01617  ,  Loss/location: 0.28385  ,  IoU: 0.84066

[train: 41, 100 / 117] FPS: 75.3 (98.8)  ,  DataTime: 0.115 (0.080)  ,  ForwardTime: 0.655  ,  TotalTime: 0.850  ,  Loss/total: 0.70998  ,  Loss/giou: 0.17231  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28853  ,  IoU: 0.83909[train: 41, 100 / 117] FPS: 75.4 (98.9)  ,  DataTime: 0.137 (0.075)  ,  ForwardTime: 0.637  ,  TotalTime: 0.849  ,  Loss/total: 0.68820  ,  Loss/giou: 0.16903  ,  Loss/l1: 0.01494  ,  Loss/location: 0.27544  ,  IoU: 0.84209

[train: 41, 100 / 117] FPS: 73.0 (98.7)  ,  DataTime: 0.102 (0.078)  ,  ForwardTime: 0.697  ,  TotalTime: 0.877  ,  Loss/total: 0.72199  ,  Loss/giou: 0.17573  ,  Loss/l1: 0.01634  ,  Loss/location: 0.28883  ,  IoU: 0.83778
[train: 41, 100 / 117] FPS: 73.9 (98.7)  ,  DataTime: 0.128 (0.072)  ,  ForwardTime: 0.667  ,  TotalTime: 0.866  ,  Loss/total: 0.72164  ,  Loss/giou: 0.17435  ,  Loss/l1: 0.01615  ,  Loss/location: 0.29218  ,  IoU: 0.83889
[train: 41, 117 / 117] FPS: 76.7 (110.7)  ,  DataTime: 0.092 (0.073)  ,  ForwardTime: 0.669  ,  TotalTime: 0.834  ,  Loss/total: 0.72222  ,  Loss/giou: 0.17563  ,  Loss/l1: 0.01634  ,  Loss/location: 0.28928  ,  IoU: 0.83795
[train: 41, 117 / 117] FPS: 78.9 (110.7)  ,  DataTime: 0.103 (0.075)  ,  ForwardTime: 0.633  ,  TotalTime: 0.811  ,  Loss/total: 0.70397  ,  Loss/giou: 0.17099  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28615  ,  IoU: 0.84028
[train: 41, 117 / 117] FPS: 76.7 (110.7)  ,  DataTime: 0.100 (0.073)  ,  ForwardTime: 0.662  ,  TotalTime: 0.834  ,  Loss/total: 0.69520  ,  Loss/giou: 0.16893  ,  Loss/l1: 0.01511  ,  Loss/location: 0.28179  ,  IoU: 0.84258
[train: 41, 117 / 117] FPS: 76.1 (110.7)  ,  DataTime: 0.092 (0.073)  ,  ForwardTime: 0.676  ,  TotalTime: 0.841  ,  Loss/total: 0.70193  ,  Loss/giou: 0.17026  ,  Loss/l1: 0.01535  ,  Loss/location: 0.28468  ,  IoU: 0.84158
[train: 41, 117 / 117] FPS: 79.0 (110.9)  ,  DataTime: 0.122 (0.070)  ,  ForwardTime: 0.618  ,  TotalTime: 0.810  ,  Loss/total: 0.68844  ,  Loss/giou: 0.16928  ,  Loss/l1: 0.01498  ,  Loss/location: 0.27497  ,  IoU: 0.84194
[train: 41, 117 / 117] FPS: 77.4 (110.8)  ,  DataTime: 0.110 (0.077)  ,  ForwardTime: 0.640  ,  TotalTime: 0.827  ,  Loss/total: 0.70921  ,  Loss/giou: 0.17091  ,  Loss/l1: 0.01525  ,  Loss/location: 0.29112  ,  IoU: 0.84054
[train: 41, 117 / 117] FPS: 77.6 (110.7)  ,  DataTime: 0.114 (0.067)  ,  ForwardTime: 0.644  ,  TotalTime: 0.825  ,  Loss/total: 0.71620  ,  Loss/giou: 0.17337  ,  Loss/l1: 0.01592  ,  Loss/location: 0.28984  ,  IoU: 0.83950
[train: 41, 117 / 117] FPS: 75.9 (110.7)  ,  DataTime: 0.088 (0.076)  ,  ForwardTime: 0.680  ,  TotalTime: 0.844  ,  Loss/total: 0.70477  ,  Loss/giou: 0.17201  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28082  ,  IoU: 0.84117
Epoch Time: 0:01:37.576746
Avg Data Time: 0.09156
Avg GPU Trans Time: 0.07344
Avg Forward Time: 0.66900
Epoch Time: 0:01:37.619722
Avg Data Time: 0.09986
Avg GPU Trans Time: 0.07286
Avg Forward Time: 0.66163
Epoch Time: 0:01:36.780693
Avg Data Time: 0.11012
Avg GPU Trans Time: 0.07662
Avg Forward Time: 0.64044
Epoch Time: 0:01:34.868941
Avg Data Time: 0.10332
Avg GPU Trans Time: 0.07478
Avg Forward Time: 0.63275
Epoch Time: 0:01:34.794406
Avg Data Time: 0.12231
Avg GPU Trans Time: 0.07022
Avg Forward Time: 0.61768
Epoch Time: 0:01:38.705500
Avg Data Time: 0.08771
Avg GPU Trans Time: 0.07610
Avg Forward Time: 0.67983
Epoch Time: 0:01:38.439936
Avg Data Time: 0.09178
Avg GPU Trans Time: 0.07334
Avg Forward Time: 0.67625
Epoch Time: 0:01:36.536270
Avg Data Time: 0.11411
Avg GPU Trans Time: 0.06701
Avg Forward Time: 0.64398
[train: 42, 50 / 117] FPS: 64.6 (84.3)  ,  DataTime: 0.186 (0.078)  ,  ForwardTime: 0.727  ,  TotalTime: 0.991  ,  Loss/total: 0.67790  ,  Loss/giou: 0.16800  ,  Loss/l1: 0.01565  ,  Loss/location: 0.26363  ,  IoU: 0.84511
[train: 42, 50 / 117] FPS: 64.6 (84.3)  ,  DataTime: 0.194 (0.085)  ,  ForwardTime: 0.711  ,  TotalTime: 0.991  ,  Loss/total: 0.69198  ,  Loss/giou: 0.16882  ,  Loss/l1: 0.01497  ,  Loss/location: 0.27947  ,  IoU: 0.84236
[train: 42, 50 / 117] FPS: 64.7 (84.3)  ,  DataTime: 0.184 (0.078)  ,  ForwardTime: 0.726  ,  TotalTime: 0.989  ,  Loss/total: 0.69313  ,  Loss/giou: 0.16965  ,  Loss/l1: 0.01508  ,  Loss/location: 0.27844  ,  IoU: 0.84200
[train: 42, 50 / 117] FPS: 64.7 (84.3)  ,  DataTime: 0.213 (0.082)  ,  ForwardTime: 0.694  ,  TotalTime: 0.989  ,  Loss/total: 0.72203  ,  Loss/giou: 0.17571  ,  Loss/l1: 0.01594  ,  Loss/location: 0.29092  ,  IoU: 0.83773
[train: 42, 50 / 117] FPS: 64.7 (85.0)  ,  DataTime: 0.202 (0.074)  ,  ForwardTime: 0.712  ,  TotalTime: 0.989  ,  Loss/total: 0.71103  ,  Loss/giou: 0.17311  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28689  ,  IoU: 0.83926
[train: 42, 50 / 117] FPS: 64.7 (84.2)  ,  DataTime: 0.191 (0.079)  ,  ForwardTime: 0.718  ,  TotalTime: 0.989  ,  Loss/total: 0.69377  ,  Loss/giou: 0.16970  ,  Loss/l1: 0.01500  ,  Loss/location: 0.27938  ,  IoU: 0.84156
[train: 42, 50 / 117] FPS: 64.7 (84.4)  ,  DataTime: 0.213 (0.073)  ,  ForwardTime: 0.703  ,  TotalTime: 0.989  ,  Loss/total: 0.73571  ,  Loss/giou: 0.17808  ,  Loss/l1: 0.01724  ,  Loss/location: 0.29333  ,  IoU: 0.83675
[train: 42, 50 / 117] FPS: 64.7 (84.4)  ,  DataTime: 0.203 (0.087)  ,  ForwardTime: 0.699  ,  TotalTime: 0.989  ,  Loss/total: 0.72390  ,  Loss/giou: 0.17550  ,  Loss/l1: 0.01576  ,  Loss/location: 0.29409  ,  IoU: 0.83642
[train: 42, 100 / 117] FPS: 73.8 (105.0)  ,  DataTime: 0.121 (0.074)  ,  ForwardTime: 0.673  ,  TotalTime: 0.868  ,  Loss/total: 0.70907  ,  Loss/giou: 0.17404  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28213  ,  IoU: 0.83822[train: 42, 100 / 117] FPS: 73.7 (105.0)  ,  DataTime: 0.116 (0.078)  ,  ForwardTime: 0.674  ,  TotalTime: 0.868  ,  Loss/total: 0.71029  ,  Loss/giou: 0.17407  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28300  ,  IoU: 0.83880[train: 42, 100 / 117] FPS: 73.7 (105.2)  ,  DataTime: 0.125 (0.067)  ,  ForwardTime: 0.676  ,  TotalTime: 0.868  ,  Loss/total: 0.69824  ,  Loss/giou: 0.16989  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28206  ,  IoU: 0.84197


[train: 42, 100 / 117] FPS: 73.7 (104.9)  ,  DataTime: 0.122 (0.082)  ,  ForwardTime: 0.665  ,  TotalTime: 0.869  ,  Loss/total: 0.69028  ,  Loss/giou: 0.16801  ,  Loss/l1: 0.01493  ,  Loss/location: 0.27963  ,  IoU: 0.84307
[train: 42, 100 / 117] FPS: 73.7 (105.0)  ,  DataTime: 0.132 (0.069)  ,  ForwardTime: 0.667  ,  TotalTime: 0.868  ,  Loss/total: 0.71773  ,  Loss/giou: 0.17417  ,  Loss/l1: 0.01637  ,  Loss/location: 0.28755  ,  IoU: 0.83904
[train: 42, 100 / 117] FPS: 73.8 (104.9)  ,  DataTime: 0.132 (0.081)  ,  ForwardTime: 0.655  ,  TotalTime: 0.868  ,  Loss/total: 0.71983  ,  Loss/giou: 0.17497  ,  Loss/l1: 0.01591  ,  Loss/location: 0.29035  ,  IoU: 0.83803
[train: 42, 100 / 117] FPS: 73.7 (105.0)  ,  DataTime: 0.115 (0.075)  ,  ForwardTime: 0.680  ,  TotalTime: 0.869  ,  Loss/total: 0.70953  ,  Loss/giou: 0.17414  ,  Loss/l1: 0.01639  ,  Loss/location: 0.27931  ,  IoU: 0.83966
[train: 42, 100 / 117] FPS: 73.7 (104.8)  ,  DataTime: 0.128 (0.082)  ,  ForwardTime: 0.658  ,  TotalTime: 0.868  ,  Loss/total: 0.70234  ,  Loss/giou: 0.17218  ,  Loss/l1: 0.01523  ,  Loss/location: 0.28185  ,  IoU: 0.83926
[train: 42, 117 / 117] FPS: 77.4 (108.5)  ,  DataTime: 0.104 (0.073)  ,  ForwardTime: 0.649  ,  TotalTime: 0.827  ,  Loss/total: 0.71123  ,  Loss/giou: 0.17429  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28331  ,  IoU: 0.83866
[train: 42, 117 / 117] FPS: 77.4 (108.5)  ,  DataTime: 0.112 (0.063)  ,  ForwardTime: 0.651  ,  TotalTime: 0.826  ,  Loss/total: 0.69618  ,  Loss/giou: 0.16921  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28156  ,  IoU: 0.84259
[train: 42, 117 / 117] FPS: 77.4 (108.5)  ,  DataTime: 0.118 (0.065)  ,  ForwardTime: 0.643  ,  TotalTime: 0.826  ,  Loss/total: 0.71400  ,  Loss/giou: 0.17367  ,  Loss/l1: 0.01618  ,  Loss/location: 0.28577  ,  IoU: 0.83929
[train: 42, 117 / 117] FPS: 77.4 (108.5)  ,  DataTime: 0.114 (0.077)  ,  ForwardTime: 0.635  ,  TotalTime: 0.827  ,  Loss/total: 0.70539  ,  Loss/giou: 0.17216  ,  Loss/l1: 0.01530  ,  Loss/location: 0.28458  ,  IoU: 0.83945
[train: 42, 117 / 117] FPS: 77.4 (108.5)  ,  DataTime: 0.102 (0.071)  ,  ForwardTime: 0.654  ,  TotalTime: 0.827  ,  Loss/total: 0.71474  ,  Loss/giou: 0.17513  ,  Loss/l1: 0.01647  ,  Loss/location: 0.28214  ,  IoU: 0.83880
[train: 42, 117 / 117] FPS: 77.4 (108.4)  ,  DataTime: 0.109 (0.070)  ,  ForwardTime: 0.648  ,  TotalTime: 0.826  ,  Loss/total: 0.71062  ,  Loss/giou: 0.17432  ,  Loss/l1: 0.01585  ,  Loss/location: 0.28275  ,  IoU: 0.83823
[train: 42, 117 / 117] FPS: 77.4 (108.6)  ,  DataTime: 0.109 (0.077)  ,  ForwardTime: 0.641  ,  TotalTime: 0.827  ,  Loss/total: 0.68630  ,  Loss/giou: 0.16750  ,  Loss/l1: 0.01482  ,  Loss/location: 0.27717  ,  IoU: 0.84347
[train: 42, 117 / 117] FPS: 77.4 (108.9)  ,  DataTime: 0.118 (0.076)  ,  ForwardTime: 0.633  ,  TotalTime: 0.826  ,  Loss/total: 0.72108  ,  Loss/giou: 0.17456  ,  Loss/l1: 0.01586  ,  Loss/location: 0.29268  ,  IoU: 0.83827
Epoch Time: 0:01:36.793352
Avg Data Time: 0.10238
Avg GPU Trans Time: 0.07068
Avg Forward Time: 0.65423
Epoch Time: 0:01:36.786323
Avg Data Time: 0.10918
Avg GPU Trans Time: 0.07708
Avg Forward Time: 0.64097
Epoch Time: 0:01:36.710032
Avg Data Time: 0.10403
Avg GPU Trans Time: 0.07319
Avg Forward Time: 0.64936
Epoch Time: 0:01:36.695957
Avg Data Time: 0.11204
Avg GPU Trans Time: 0.06303
Avg Forward Time: 0.65138
Epoch Time: 0:01:36.684869
Avg Data Time: 0.10865
Avg GPU Trans Time: 0.06950
Avg Forward Time: 0.64821
Epoch Time: 0:01:36.713036
Avg Data Time: 0.11437
Avg GPU Trans Time: 0.07690
Avg Forward Time: 0.63534
Epoch Time: 0:01:36.690009
Avg Data Time: 0.11767
Avg GPU Trans Time: 0.07595
Avg Forward Time: 0.63279
Epoch Time: 0:01:36.698158
Avg Data Time: 0.11783
Avg GPU Trans Time: 0.06548
Avg Forward Time: 0.64318
[train: 43, 50 / 117] FPS: 64.3 (84.9)  ,  DataTime: 0.198 (0.077)  ,  ForwardTime: 0.719  ,  TotalTime: 0.995  ,  Loss/total: 0.70451  ,  Loss/giou: 0.17306  ,  Loss/l1: 0.01586  ,  Loss/location: 0.27907  ,  IoU: 0.83978
[train: 43, 50 / 117] FPS: 64.3 (84.3)  ,  DataTime: 0.198 (0.086)  ,  ForwardTime: 0.712  ,  TotalTime: 0.996  ,  Loss/total: 0.68097  ,  Loss/giou: 0.16769  ,  Loss/l1: 0.01493  ,  Loss/location: 0.27093  ,  IoU: 0.84345
[train: 43, 50 / 117] FPS: 64.3 (84.5)  ,  DataTime: 0.189 (0.087)  ,  ForwardTime: 0.719  ,  TotalTime: 0.995  ,  Loss/total: 0.71798  ,  Loss/giou: 0.17613  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28658  ,  IoU: 0.83666
[train: 43, 50 / 117] FPS: 64.3 (84.4)  ,  DataTime: 0.185 (0.080)  ,  ForwardTime: 0.730  ,  TotalTime: 0.995  ,  Loss/total: 0.70878  ,  Loss/giou: 0.17168  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28818  ,  IoU: 0.84093
[train: 43, 50 / 117] FPS: 64.3 (84.0)  ,  DataTime: 0.204 (0.080)  ,  ForwardTime: 0.711  ,  TotalTime: 0.995  ,  Loss/total: 0.70215  ,  Loss/giou: 0.16942  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28723  ,  IoU: 0.84269
[train: 43, 50 / 117] FPS: 64.3 (83.9)  ,  DataTime: 0.196 (0.081)  ,  ForwardTime: 0.717  ,  TotalTime: 0.995  ,  Loss/total: 0.69628  ,  Loss/giou: 0.16738  ,  Loss/l1: 0.01442  ,  Loss/location: 0.28944  ,  IoU: 0.84267
[train: 43, 50 / 117] FPS: 64.3 (84.1)  ,  DataTime: 0.214 (0.091)  ,  ForwardTime: 0.690  ,  TotalTime: 0.995  ,  Loss/total: 0.70543  ,  Loss/giou: 0.17106  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28564  ,  IoU: 0.84086
[train: 43, 50 / 117] FPS: 64.3 (84.3)  ,  DataTime: 0.219 (0.086)  ,  ForwardTime: 0.691  ,  TotalTime: 0.996  ,  Loss/total: 0.71171  ,  Loss/giou: 0.17368  ,  Loss/l1: 0.01612  ,  Loss/location: 0.28374  ,  IoU: 0.83980
[train: 43, 100 / 117] FPS: 73.3 (104.9)  ,  DataTime: 0.117 (0.080)  ,  ForwardTime: 0.675  ,  TotalTime: 0.873  ,  Loss/total: 0.70593  ,  Loss/giou: 0.17140  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28550  ,  IoU: 0.84093[train: 43, 100 / 117] FPS: 73.3 (104.7)  ,  DataTime: 0.125 (0.073)  ,  ForwardTime: 0.675  ,  TotalTime: 0.873  ,  Loss/total: 0.69974  ,  Loss/giou: 0.17142  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28002  ,  IoU: 0.84049

[train: 43, 100 / 117] FPS: 73.3 (104.6)  ,  DataTime: 0.132 (0.085)  ,  ForwardTime: 0.656  ,  TotalTime: 0.873  ,  Loss/total: 0.70910  ,  Loss/giou: 0.17085  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28936  ,  IoU: 0.84115
[train: 43, 100 / 117] FPS: 73.3 (104.6)  ,  DataTime: 0.121 (0.084)  ,  ForwardTime: 0.669  ,  TotalTime: 0.873  ,  Loss/total: 0.70301  ,  Loss/giou: 0.17116  ,  Loss/l1: 0.01532  ,  Loss/location: 0.28408  ,  IoU: 0.84088
[train: 43, 100 / 117] FPS: 73.3 (104.7)  ,  DataTime: 0.124 (0.075)  ,  ForwardTime: 0.674  ,  TotalTime: 0.873  ,  Loss/total: 0.69446  ,  Loss/giou: 0.16730  ,  Loss/l1: 0.01476  ,  Loss/location: 0.28609  ,  IoU: 0.84362[train: 43, 100 / 117] FPS: 73.3 (104.6)  ,  DataTime: 0.127 (0.075)  ,  ForwardTime: 0.671  ,  TotalTime: 0.873  ,  Loss/total: 0.69732  ,  Loss/giou: 0.16884  ,  Loss/l1: 0.01520  ,  Loss/location: 0.28366  ,  IoU: 0.84311

[train: 43, 100 / 117] FPS: 73.2 (104.9)  ,  DataTime: 0.125 (0.084)  ,  ForwardTime: 0.665  ,  TotalTime: 0.874  ,  Loss/total: 0.69203  ,  Loss/giou: 0.16982  ,  Loss/l1: 0.01522  ,  Loss/location: 0.27628  ,  IoU: 0.84183
[train: 43, 100 / 117] FPS: 73.2 (104.7)  ,  DataTime: 0.136 (0.084)  ,  ForwardTime: 0.654  ,  TotalTime: 0.874  ,  Loss/total: 0.69999  ,  Loss/giou: 0.17024  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28239  ,  IoU: 0.84182
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.121 (0.079)  ,  ForwardTime: 0.632  ,  TotalTime: 0.832  ,  Loss/total: 0.70458  ,  Loss/giou: 0.17096  ,  Loss/l1: 0.01550  ,  Loss/location: 0.28515  ,  IoU: 0.84110
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.112 (0.069)  ,  ForwardTime: 0.650  ,  TotalTime: 0.831  ,  Loss/total: 0.70863  ,  Loss/giou: 0.17310  ,  Loss/l1: 0.01567  ,  Loss/location: 0.28406  ,  IoU: 0.83931
[train: 43, 117 / 117] FPS: 77.0 (111.9)  ,  DataTime: 0.114 (0.071)  ,  ForwardTime: 0.647  ,  TotalTime: 0.831  ,  Loss/total: 0.70016  ,  Loss/giou: 0.16937  ,  Loss/l1: 0.01521  ,  Loss/location: 0.28536  ,  IoU: 0.84235
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.112 (0.079)  ,  ForwardTime: 0.641  ,  TotalTime: 0.832  ,  Loss/total: 0.68961  ,  Loss/giou: 0.16961  ,  Loss/l1: 0.01527  ,  Loss/location: 0.27405  ,  IoU: 0.84212
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.111 (0.071)  ,  ForwardTime: 0.649  ,  TotalTime: 0.831  ,  Loss/total: 0.69176  ,  Loss/giou: 0.16738  ,  Loss/l1: 0.01471  ,  Loss/location: 0.28348  ,  IoU: 0.84345
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.108 (0.079)  ,  ForwardTime: 0.645  ,  TotalTime: 0.831  ,  Loss/total: 0.70098  ,  Loss/giou: 0.17070  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28337  ,  IoU: 0.84122
[train: 43, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.105 (0.076)  ,  ForwardTime: 0.650  ,  TotalTime: 0.831  ,  Loss/total: 0.71185  ,  Loss/giou: 0.17347  ,  Loss/l1: 0.01584  ,  Loss/location: 0.28571  ,  IoU: 0.83944
[train: 43, 117 / 117] FPS: 77.0 (112.0)  ,  DataTime: 0.118 (0.079)  ,  ForwardTime: 0.634  ,  TotalTime: 0.831  ,  Loss/total: 0.70724  ,  Loss/giou: 0.17013  ,  Loss/l1: 0.01552  ,  Loss/location: 0.28937  ,  IoU: 0.84171
Epoch Time: 0:01:37.301920
Avg Data Time: 0.11196
Avg GPU Trans Time: 0.07889
Avg Forward Time: 0.64079
Epoch Time: 0:01:37.307598
Avg Data Time: 0.12079
Avg GPU Trans Time: 0.07927
Avg Forward Time: 0.63163
Epoch Time: 0:01:37.269284
Avg Data Time: 0.10811
Avg GPU Trans Time: 0.07862
Avg Forward Time: 0.64464
Epoch Time: 0:01:37.244830
Avg Data Time: 0.11801
Avg GPU Trans Time: 0.07882
Avg Forward Time: 0.63433
Epoch Time: 0:01:37.250601
Avg Data Time: 0.11370
Avg GPU Trans Time: 0.07070
Avg Forward Time: 0.64680
Epoch Time: 0:01:37.249744
Avg Data Time: 0.10523
Avg GPU Trans Time: 0.07557
Avg Forward Time: 0.65039
Epoch Time: 0:01:37.241229
Avg Data Time: 0.11054
Avg GPU Trans Time: 0.07137
Avg Forward Time: 0.64921
Epoch Time: 0:01:37.256820
Avg Data Time: 0.11180
Avg GPU Trans Time: 0.06912
Avg Forward Time: 0.65034
[train: 44, 50 / 117] FPS: 64.3 (79.7)  ,  DataTime: 0.210 (0.087)  ,  ForwardTime: 0.697  ,  TotalTime: 0.995  ,  Loss/total: 0.75380  ,  Loss/giou: 0.18155  ,  Loss/l1: 0.01713  ,  Loss/location: 0.30503  ,  IoU: 0.83348
[train: 44, 50 / 117] FPS: 64.3 (79.9)  ,  DataTime: 0.178 (0.086)  ,  ForwardTime: 0.731  ,  TotalTime: 0.995  ,  Loss/total: 0.69550  ,  Loss/giou: 0.17187  ,  Loss/l1: 0.01574  ,  Loss/location: 0.27307  ,  IoU: 0.84077
[train: 44, 50 / 117] FPS: 64.3 (79.8)  ,  DataTime: 0.178 (0.089)  ,  ForwardTime: 0.728  ,  TotalTime: 0.995  ,  Loss/total: 0.68335  ,  Loss/giou: 0.16550  ,  Loss/l1: 0.01433  ,  Loss/location: 0.28070  ,  IoU: 0.84543
[train: 44, 50 / 117] FPS: 64.3 (79.9)  ,  DataTime: 0.164 (0.071)  ,  ForwardTime: 0.760  ,  TotalTime: 0.995  ,  Loss/total: 0.70463  ,  Loss/giou: 0.17081  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28470  ,  IoU: 0.84146
[train: 44, 50 / 117] FPS: 64.3 (79.9)  ,  DataTime: 0.228 (0.085)  ,  ForwardTime: 0.681  ,  TotalTime: 0.995  ,  Loss/total: 0.73365  ,  Loss/giou: 0.17824  ,  Loss/l1: 0.01662  ,  Loss/location: 0.29406  ,  IoU: 0.83587
[train: 44, 50 / 117] FPS: 64.3 (79.8)  ,  DataTime: 0.201 (0.072)  ,  ForwardTime: 0.722  ,  TotalTime: 0.995  ,  Loss/total: 0.68565  ,  Loss/giou: 0.16675  ,  Loss/l1: 0.01490  ,  Loss/location: 0.27767  ,  IoU: 0.84454
[train: 44, 50 / 117] FPS: 64.3 (79.8)  ,  DataTime: 0.217 (0.090)  ,  ForwardTime: 0.689  ,  TotalTime: 0.995  ,  Loss/total: 0.69216  ,  Loss/giou: 0.16833  ,  Loss/l1: 0.01543  ,  Loss/location: 0.27838  ,  IoU: 0.84380
[train: 44, 50 / 117] FPS: 64.3 (79.7)  ,  DataTime: 0.220 (0.071)  ,  ForwardTime: 0.704  ,  TotalTime: 0.995  ,  Loss/total: 0.68340  ,  Loss/giou: 0.16673  ,  Loss/l1: 0.01507  ,  Loss/location: 0.27461  ,  IoU: 0.84404
[train: 44, 100 / 117] FPS: 73.4 (107.6)  ,  DataTime: 0.106 (0.066)  ,  ForwardTime: 0.700  ,  TotalTime: 0.872  ,  Loss/total: 0.69988  ,  Loss/giou: 0.16948  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28528  ,  IoU: 0.84187
[train: 44, 100 / 117] FPS: 73.4 (107.8)  ,  DataTime: 0.134 (0.071)  ,  ForwardTime: 0.666  ,  TotalTime: 0.872  ,  Loss/total: 0.70351  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28339  ,  IoU: 0.84057
[train: 44, 100 / 117] FPS: 73.4 (107.5)  ,  DataTime: 0.124 (0.067)  ,  ForwardTime: 0.681  ,  TotalTime: 0.872  ,  Loss/total: 0.68792  ,  Loss/giou: 0.16707  ,  Loss/l1: 0.01481  ,  Loss/location: 0.27975  ,  IoU: 0.84388
[train: 44, 100 / 117] FPS: 73.4 (107.5)  ,  DataTime: 0.139 (0.082)  ,  ForwardTime: 0.650  ,  TotalTime: 0.872  ,  Loss/total: 0.71795  ,  Loss/giou: 0.17478  ,  Loss/l1: 0.01586  ,  Loss/location: 0.28909  ,  IoU: 0.83816
[train: 44, 100 / 117] FPS: 73.4 (107.5)  ,  DataTime: 0.115 (0.083)  ,  ForwardTime: 0.674  ,  TotalTime: 0.872  ,  Loss/total: 0.71378  ,  Loss/giou: 0.17508  ,  Loss/l1: 0.01609  ,  Loss/location: 0.28315  ,  IoU: 0.83798
[train: 44, 100 / 117] FPS: 73.4 (107.6)  ,  DataTime: 0.134 (0.085)  ,  ForwardTime: 0.653  ,  TotalTime: 0.872  ,  Loss/total: 0.68709  ,  Loss/giou: 0.16794  ,  Loss/l1: 0.01502  ,  Loss/location: 0.27613  ,  IoU: 0.84355
[train: 44, 100 / 117] FPS: 73.4 (107.5)  ,  DataTime: 0.115 (0.087)  ,  ForwardTime: 0.671  ,  TotalTime: 0.872  ,  Loss/total: 0.69806  ,  Loss/giou: 0.16897  ,  Loss/l1: 0.01503  ,  Loss/location: 0.28500  ,  IoU: 0.84272
[train: 44, 100 / 117] FPS: 73.4 (107.5)  ,  DataTime: 0.131 (0.085)  ,  ForwardTime: 0.656  ,  TotalTime: 0.872  ,  Loss/total: 0.73298  ,  Loss/giou: 0.17748  ,  Loss/l1: 0.01645  ,  Loss/location: 0.29578  ,  IoU: 0.83634
[train: 44, 117 / 117] FPS: 77.2 (110.3)  ,  DataTime: 0.095 (0.063)  ,  ForwardTime: 0.671  ,  TotalTime: 0.830  ,  Loss/total: 0.70052  ,  Loss/giou: 0.17003  ,  Loss/l1: 0.01515  ,  Loss/location: 0.28473  ,  IoU: 0.84133[train: 44, 117 / 117] FPS: 77.2 (110.3)  ,  DataTime: 0.124 (0.078)  ,  ForwardTime: 0.628  ,  TotalTime: 0.829  ,  Loss/total: 0.71535  ,  Loss/giou: 0.17371  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28947  ,  IoU: 0.83872

[train: 44, 117 / 117] FPS: 77.2 (110.3)  ,  DataTime: 0.120 (0.068)  ,  ForwardTime: 0.642  ,  TotalTime: 0.829  ,  Loss/total: 0.69781  ,  Loss/giou: 0.16977  ,  Loss/l1: 0.01532  ,  Loss/location: 0.28167  ,  IoU: 0.84158
[train: 44, 117 / 117] FPS: 77.2 (110.3)  ,  DataTime: 0.111 (0.063)  ,  ForwardTime: 0.655  ,  TotalTime: 0.829  ,  Loss/total: 0.68596  ,  Loss/giou: 0.16702  ,  Loss/l1: 0.01470  ,  Loss/location: 0.27840  ,  IoU: 0.84378
[train: 44, 117 / 117] FPS: 77.2 (110.3)  ,  DataTime: 0.103 (0.081)  ,  ForwardTime: 0.646  ,  TotalTime: 0.830  ,  Loss/total: 0.69995  ,  Loss/giou: 0.16930  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28568  ,  IoU: 0.84263[train: 44, 117 / 117] FPS: 77.1 (110.3)  ,  DataTime: 0.119 (0.080)  ,  ForwardTime: 0.631  ,  TotalTime: 0.830  ,  Loss/total: 0.69485  ,  Loss/giou: 0.16950  ,  Loss/l1: 0.01520  ,  Loss/location: 0.27983  ,  IoU: 0.84211[train: 44, 117 / 117] FPS: 77.2 (110.6)  ,  DataTime: 0.103 (0.078)  ,  ForwardTime: 0.649  ,  TotalTime: 0.829  ,  Loss/total: 0.71196  ,  Loss/giou: 0.17450  ,  Loss/l1: 0.01596  ,  Loss/location: 0.28315  ,  IoU: 0.83855


[train: 44, 117 / 117] FPS: 77.2 (110.4)  ,  DataTime: 0.117 (0.080)  ,  ForwardTime: 0.633  ,  TotalTime: 0.830  ,  Loss/total: 0.73052  ,  Loss/giou: 0.17739  ,  Loss/l1: 0.01647  ,  Loss/location: 0.29337  ,  IoU: 0.83650
Epoch Time: 0:01:37.053551
Avg Data Time: 0.11659
Avg GPU Trans Time: 0.07980
Avg Forward Time: 0.63313
Epoch Time: 0:01:37.052916
Avg Data Time: 0.10292
Avg GPU Trans Time: 0.08091
Avg Forward Time: 0.64568
Epoch Time: 0:01:37.061980
Avg Data Time: 0.11901
Avg GPU Trans Time: 0.07994
Avg Forward Time: 0.63064
Epoch Time: 0:01:37.056098
Avg Data Time: 0.09507
Avg GPU Trans Time: 0.06305
Avg Forward Time: 0.67142
Epoch Time: 0:01:37.051275
Avg Data Time: 0.11102
Avg GPU Trans Time: 0.06323
Avg Forward Time: 0.65524
Epoch Time: 0:01:37.037230
Avg Data Time: 0.11952
Avg GPU Trans Time: 0.06764
Avg Forward Time: 0.64222
Epoch Time: 0:01:37.047867
Avg Data Time: 0.10293
Avg GPU Trans Time: 0.07756
Avg Forward Time: 0.64898
Epoch Time: 0:01:37.043005
Avg Data Time: 0.12384
Avg GPU Trans Time: 0.07794
Avg Forward Time: 0.62765
[train: 45, 50 / 117] FPS: 64.2 (84.3)  ,  DataTime: 0.176 (0.082)  ,  ForwardTime: 0.739  ,  TotalTime: 0.997  ,  Loss/total: 0.71667  ,  Loss/giou: 0.17304  ,  Loss/l1: 0.01548  ,  Loss/location: 0.29319  ,  IoU: 0.83892
[train: 45, 50 / 117] FPS: 64.2 (84.6)  ,  DataTime: 0.175 (0.082)  ,  ForwardTime: 0.741  ,  TotalTime: 0.998  ,  Loss/total: 0.70993  ,  Loss/giou: 0.17194  ,  Loss/l1: 0.01534  ,  Loss/location: 0.28933  ,  IoU: 0.83930
[train: 45, 50 / 117] FPS: 64.2 (84.6)  ,  DataTime: 0.201 (0.082)  ,  ForwardTime: 0.715  ,  TotalTime: 0.997  ,  Loss/total: 0.72053  ,  Loss/giou: 0.17618  ,  Loss/l1: 0.01609  ,  Loss/location: 0.28774  ,  IoU: 0.83648
[train: 45, 50 / 117] FPS: 64.1 (84.6)  ,  DataTime: 0.250 (0.084)  ,  ForwardTime: 0.664  ,  TotalTime: 0.998  ,  Loss/total: 0.69956  ,  Loss/giou: 0.17241  ,  Loss/l1: 0.01597  ,  Loss/location: 0.27487  ,  IoU: 0.84022
[train: 45, 50 / 117] FPS: 64.1 (84.4)  ,  DataTime: 0.186 (0.083)  ,  ForwardTime: 0.729  ,  TotalTime: 0.998  ,  Loss/total: 0.71466  ,  Loss/giou: 0.17314  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28961  ,  IoU: 0.83975
[train: 45, 50 / 117] FPS: 64.2 (84.1)  ,  DataTime: 0.196 (0.068)  ,  ForwardTime: 0.733  ,  TotalTime: 0.997  ,  Loss/total: 0.68741  ,  Loss/giou: 0.16792  ,  Loss/l1: 0.01555  ,  Loss/location: 0.27379  ,  IoU: 0.84485
[train: 45, 50 / 117] FPS: 64.1 (84.4)  ,  DataTime: 0.180 (0.082)  ,  ForwardTime: 0.737  ,  TotalTime: 0.998  ,  Loss/total: 0.70309  ,  Loss/giou: 0.17141  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28144  ,  IoU: 0.84112
[train: 45, 50 / 117] FPS: 64.2 (84.2)  ,  DataTime: 0.178 (0.072)  ,  ForwardTime: 0.747  ,  TotalTime: 0.997  ,  Loss/total: 0.67659  ,  Loss/giou: 0.16573  ,  Loss/l1: 0.01448  ,  Loss/location: 0.27275  ,  IoU: 0.84464
[train: 45, 100 / 117] FPS: 73.2 (103.1)  ,  DataTime: 0.117 (0.082)  ,  ForwardTime: 0.675  ,  TotalTime: 0.874  ,  Loss/total: 0.72518  ,  Loss/giou: 0.17592  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29288  ,  IoU: 0.83723
[train: 45, 100 / 117] FPS: 73.2 (103.1)  ,  DataTime: 0.152 (0.081)  ,  ForwardTime: 0.641  ,  TotalTime: 0.874  ,  Loss/total: 0.68760  ,  Loss/giou: 0.16859  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27386  ,  IoU: 0.84291[train: 45, 100 / 117] FPS: 73.2 (103.1)  ,  DataTime: 0.114 (0.081)  ,  ForwardTime: 0.678  ,  TotalTime: 0.874  ,  Loss/total: 0.73910  ,  Loss/giou: 0.17809  ,  Loss/l1: 0.01649  ,  Loss/location: 0.30049  ,  IoU: 0.83557

[train: 45, 100 / 117] FPS: 73.2 (103.2)  ,  DataTime: 0.114 (0.080)  ,  ForwardTime: 0.680  ,  TotalTime: 0.874  ,  Loss/total: 0.70756  ,  Loss/giou: 0.17272  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28364  ,  IoU: 0.83969
[train: 45, 100 / 117] FPS: 73.2 (103.1)  ,  DataTime: 0.113 (0.079)  ,  ForwardTime: 0.682  ,  TotalTime: 0.874  ,  Loss/total: 0.70594  ,  Loss/giou: 0.17112  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28688  ,  IoU: 0.84042
[train: 45, 100 / 117] FPS: 73.3 (103.1)  ,  DataTime: 0.114 (0.069)  ,  ForwardTime: 0.691  ,  TotalTime: 0.874  ,  Loss/total: 0.68947  ,  Loss/giou: 0.16893  ,  Loss/l1: 0.01500  ,  Loss/location: 0.27662  ,  IoU: 0.84240[train: 45, 100 / 117] FPS: 73.2 (103.1)  ,  DataTime: 0.123 (0.066)  ,  ForwardTime: 0.685  ,  TotalTime: 0.874  ,  Loss/total: 0.69717  ,  Loss/giou: 0.17103  ,  Loss/l1: 0.01575  ,  Loss/location: 0.27638  ,  IoU: 0.84188

[train: 45, 100 / 117] FPS: 73.2 (103.3)  ,  DataTime: 0.126 (0.079)  ,  ForwardTime: 0.669  ,  TotalTime: 0.874  ,  Loss/total: 0.73182  ,  Loss/giou: 0.17870  ,  Loss/l1: 0.01649  ,  Loss/location: 0.29200  ,  IoU: 0.83456
[train: 45, 117 / 117] FPS: 76.9 (109.3)  ,  DataTime: 0.134 (0.076)  ,  ForwardTime: 0.622  ,  TotalTime: 0.832  ,  Loss/total: 0.68868  ,  Loss/giou: 0.16866  ,  Loss/l1: 0.01530  ,  Loss/location: 0.27485  ,  IoU: 0.84302
[train: 45, 117 / 117] FPS: 76.9 (109.3)  ,  DataTime: 0.102 (0.076)  ,  ForwardTime: 0.653  ,  TotalTime: 0.832  ,  Loss/total: 0.73730  ,  Loss/giou: 0.17858  ,  Loss/l1: 0.01656  ,  Loss/location: 0.29733  ,  IoU: 0.83526
[train: 45, 117 / 117] FPS: 76.9 (109.3)  ,  DataTime: 0.105 (0.077)  ,  ForwardTime: 0.650  ,  TotalTime: 0.832  ,  Loss/total: 0.72086  ,  Loss/giou: 0.17474  ,  Loss/l1: 0.01600  ,  Loss/location: 0.29138  ,  IoU: 0.83827
[train: 45, 117 / 117] FPS: 76.9 (109.3)  ,  DataTime: 0.110 (0.063)  ,  ForwardTime: 0.660  ,  TotalTime: 0.832  ,  Loss/total: 0.69962  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01580  ,  Loss/location: 0.27677  ,  IoU: 0.84107
[train: 45, 117 / 117] FPS: 76.9 (109.4)  ,  DataTime: 0.101 (0.075)  ,  ForwardTime: 0.656  ,  TotalTime: 0.832  ,  Loss/total: 0.71007  ,  Loss/giou: 0.17206  ,  Loss/l1: 0.01544  ,  Loss/location: 0.28873  ,  IoU: 0.83964[train: 45, 117 / 117] FPS: 76.9 (109.3)  ,  DataTime: 0.113 (0.075)  ,  ForwardTime: 0.644  ,  TotalTime: 0.832  ,  Loss/total: 0.72754  ,  Loss/giou: 0.17759  ,  Loss/l1: 0.01628  ,  Loss/location: 0.29099  ,  IoU: 0.83538

[train: 45, 117 / 117] FPS: 76.9 (109.1)  ,  DataTime: 0.102 (0.076)  ,  ForwardTime: 0.654  ,  TotalTime: 0.832  ,  Loss/total: 0.70937  ,  Loss/giou: 0.17265  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28526  ,  IoU: 0.83988
[train: 45, 117 / 117] FPS: 76.9 (109.1)  ,  DataTime: 0.102 (0.065)  ,  ForwardTime: 0.664  ,  TotalTime: 0.832  ,  Loss/total: 0.69121  ,  Loss/giou: 0.16913  ,  Loss/l1: 0.01502  ,  Loss/location: 0.27785  ,  IoU: 0.84230
Epoch Time: 0:01:37.334421
Avg Data Time: 0.10230
Avg GPU Trans Time: 0.07612
Avg Forward Time: 0.65349
Epoch Time: 0:01:37.358898
Avg Data Time: 0.10450
Avg GPU Trans Time: 0.07713
Avg Forward Time: 0.65050
Epoch Time: 0:01:37.374611
Avg Data Time: 0.13431
Avg GPU Trans Time: 0.07642
Avg Forward Time: 0.62153
Epoch Time: 0:01:37.351699
Avg Data Time: 0.10137
Avg GPU Trans Time: 0.07481
Avg Forward Time: 0.65588
Epoch Time: 0:01:37.336482
Avg Data Time: 0.10966
Avg GPU Trans Time: 0.06275
Avg Forward Time: 0.65953
Epoch Time: 0:01:37.367194
Avg Data Time: 0.10217
Avg GPU Trans Time: 0.07565
Avg Forward Time: 0.65438
Epoch Time: 0:01:37.341504
Avg Data Time: 0.11284
Avg GPU Trans Time: 0.07496
Avg Forward Time: 0.64418
Epoch Time: 0:01:37.331141
Avg Data Time: 0.10211
Avg GPU Trans Time: 0.06546
Avg Forward Time: 0.66431
[val: 45, 50 / 78] FPS: 37.4 (215.4)  ,  DataTime: 1.296 (0.041)  ,  ForwardTime: 0.376  ,  TotalTime: 1.713  ,  Loss/total: 0.89520  ,  Loss/giou: 0.21402  ,  Loss/l1: 0.02329  ,  Loss/location: 0.35073  ,  IoU: 0.81068
[val: 45, 50 / 78] FPS: 36.9 (208.4)  ,  DataTime: 1.343 (0.042)  ,  ForwardTime: 0.350  ,  TotalTime: 1.736  ,  Loss/total: 0.80666  ,  Loss/giou: 0.19217  ,  Loss/l1: 0.01976  ,  Loss/location: 0.32354  ,  IoU: 0.82711
[val: 45, 50 / 78] FPS: 36.5 (220.7)  ,  DataTime: 1.365 (0.045)  ,  ForwardTime: 0.343  ,  TotalTime: 1.752  ,  Loss/total: 0.85947  ,  Loss/giou: 0.20559  ,  Loss/l1: 0.02183  ,  Loss/location: 0.33915  ,  IoU: 0.81656
[val: 45, 50 / 78] FPS: 35.9 (214.2)  ,  DataTime: 1.513 (0.043)  ,  ForwardTime: 0.228  ,  TotalTime: 1.785  ,  Loss/total: 0.83853  ,  Loss/giou: 0.20093  ,  Loss/l1: 0.02162  ,  Loss/location: 0.32856  ,  IoU: 0.82049
[val: 45, 50 / 78] FPS: 34.6 (223.7)  ,  DataTime: 1.521 (0.041)  ,  ForwardTime: 0.286  ,  TotalTime: 1.849  ,  Loss/total: 0.82005  ,  Loss/giou: 0.19635  ,  Loss/l1: 0.02030  ,  Loss/location: 0.32585  ,  IoU: 0.82361
[val: 45, 50 / 78] FPS: 34.3 (186.0)  ,  DataTime: 1.595 (0.041)  ,  ForwardTime: 0.232  ,  TotalTime: 1.867  ,  Loss/total: 0.82758  ,  Loss/giou: 0.19534  ,  Loss/l1: 0.01989  ,  Loss/location: 0.33745  ,  IoU: 0.82371
[val: 45, 50 / 78] FPS: 33.0 (222.8)  ,  DataTime: 1.667 (0.042)  ,  ForwardTime: 0.228  ,  TotalTime: 1.937  ,  Loss/total: 0.80414  ,  Loss/giou: 0.19579  ,  Loss/l1: 0.02052  ,  Loss/location: 0.30998  ,  IoU: 0.82404
[val: 45, 50 / 78] FPS: 32.7 (205.9)  ,  DataTime: 1.651 (0.040)  ,  ForwardTime: 0.265  ,  TotalTime: 1.956  ,  Loss/total: 0.83522  ,  Loss/giou: 0.19904  ,  Loss/l1: 0.02105  ,  Loss/location: 0.33189  ,  IoU: 0.82187
[val: 45, 78 / 78] FPS: 42.3 (40.6)  ,  DataTime: 1.148 (0.042)  ,  ForwardTime: 0.324  ,  TotalTime: 1.514  ,  Loss/total: 0.88308  ,  Loss/giou: 0.21236  ,  Loss/l1: 0.02300  ,  Loss/location: 0.34336  ,  IoU: 0.81183
Epoch Time: 0:01:58.061879
Avg Data Time: 1.14849
Avg GPU Trans Time: 0.04153
Avg Forward Time: 0.32360
[val: 45, 78 / 78] FPS: 42.1 (217.2)  ,  DataTime: 1.174 (0.043)  ,  ForwardTime: 0.302  ,  TotalTime: 1.519  ,  Loss/total: 0.85793  ,  Loss/giou: 0.20635  ,  Loss/l1: 0.02211  ,  Loss/location: 0.33469  ,  IoU: 0.81661
[val: 45, 78 / 78] FPS: 42.1 (215.7)  ,  DataTime: 1.250 (0.041)  ,  ForwardTime: 0.228  ,  TotalTime: 1.520  ,  Loss/total: 0.83174  ,  Loss/giou: 0.20091  ,  Loss/l1: 0.02134  ,  Loss/location: 0.32320  ,  IoU: 0.82030
Epoch Time: 0:01:58.469077
Avg Data Time: 1.17381
Avg GPU Trans Time: 0.04326
Avg Forward Time: 0.30176
Epoch Time: 0:01:58.550844
Avg Data Time: 1.25039
Avg GPU Trans Time: 0.04133
Avg Forward Time: 0.22816
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 45, 78 / 78] FPS: 42.0 (212.7)  ,  DataTime: 1.252 (0.042)  ,  ForwardTime: 0.229  ,  TotalTime: 1.522  ,  Loss/total: 0.84282  ,  Loss/giou: 0.20246  ,  Loss/l1: 0.02164  ,  Loss/location: 0.32970  ,  IoU: 0.81939
Epoch Time: 0:01:58.731562
Avg Data Time: 1.25155
Avg GPU Trans Time: 0.04198
Avg Forward Time: 0.22867
[val: 45, 78 / 78] FPS: 41.9 (214.3)  ,  DataTime: 1.178 (0.042)  ,  ForwardTime: 0.306  ,  TotalTime: 1.527  ,  Loss/total: 0.79558  ,  Loss/giou: 0.19097  ,  Loss/l1: 0.01953  ,  Loss/location: 0.31597  ,  IoU: 0.82781
Epoch Time: 0:01:59.097123
Avg Data Time: 1.17818
Avg GPU Trans Time: 0.04227
Avg Forward Time: 0.30644
[val: 45, 78 / 78] FPS: 41.3 (166.2)  ,  DataTime: 1.277 (0.041)  ,  ForwardTime: 0.232  ,  TotalTime: 1.550  ,  Loss/total: 0.82978  ,  Loss/giou: 0.19504  ,  Loss/l1: 0.01988  ,  Loss/location: 0.34029  ,  IoU: 0.82382
[val: 45, 78 / 78] FPS: 41.3 (181.4)  ,  DataTime: 1.256 (0.041)  ,  ForwardTime: 0.253  ,  TotalTime: 1.551  ,  Loss/total: 0.84330  ,  Loss/giou: 0.20226  ,  Loss/l1: 0.02155  ,  Loss/location: 0.33103  ,  IoU: 0.81932
Epoch Time: 0:02:00.924550
Avg Data Time: 1.27679
Avg GPU Trans Time: 0.04136
Avg Forward Time: 0.23217
Epoch Time: 0:02:00.941402
Avg Data Time: 1.25620
Avg GPU Trans Time: 0.04129
Avg Forward Time: 0.25304
[val: 45, 78 / 78] FPS: 41.1 (162.2)  ,  DataTime: 1.247 (0.043)  ,  ForwardTime: 0.267  ,  TotalTime: 1.558  ,  Loss/total: 0.81480  ,  Loss/giou: 0.19544  ,  Loss/l1: 0.02014  ,  Loss/location: 0.32320  ,  IoU: 0.82419
Epoch Time: 0:02:01.494752
Avg Data Time: 1.24740
Avg GPU Trans Time: 0.04302
Avg Forward Time: 0.26720
[train: 46, 50 / 117] FPS: 63.9 (87.5)  ,  DataTime: 0.165 (0.085)  ,  ForwardTime: 0.751  ,  TotalTime: 1.002  ,  Loss/total: 0.73926  ,  Loss/giou: 0.17968  ,  Loss/l1: 0.01629  ,  Loss/location: 0.29843  ,  IoU: 0.83390
[train: 46, 50 / 117] FPS: 64.3 (87.5)  ,  DataTime: 0.171 (0.086)  ,  ForwardTime: 0.739  ,  TotalTime: 0.995  ,  Loss/total: 0.73741  ,  Loss/giou: 0.17939  ,  Loss/l1: 0.01711  ,  Loss/location: 0.29308  ,  IoU: 0.83531
[train: 46, 50 / 117] FPS: 67.0 (87.2)  ,  DataTime: 0.212 (0.084)  ,  ForwardTime: 0.660  ,  TotalTime: 0.956  ,  Loss/total: 0.70116  ,  Loss/giou: 0.17043  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28378  ,  IoU: 0.84085
[train: 46, 50 / 117] FPS: 67.0 (87.5)  ,  DataTime: 0.192 (0.080)  ,  ForwardTime: 0.682  ,  TotalTime: 0.955  ,  Loss/total: 0.70797  ,  Loss/giou: 0.17261  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28472  ,  IoU: 0.83985
[train: 46, 50 / 117] FPS: 63.1 (87.7)  ,  DataTime: 0.151 (0.086)  ,  ForwardTime: 0.777  ,  TotalTime: 1.014  ,  Loss/total: 0.69478  ,  Loss/giou: 0.17244  ,  Loss/l1: 0.01543  ,  Loss/location: 0.27274  ,  IoU: 0.84016[train: 46, 50 / 117] FPS: 67.8 (87.0)  ,  DataTime: 0.216 (0.086)  ,  ForwardTime: 0.641  ,  TotalTime: 0.944  ,  Loss/total: 0.71055  ,  Loss/giou: 0.17272  ,  Loss/l1: 0.01580  ,  Loss/location: 0.28613  ,  IoU: 0.84041

[train: 46, 50 / 117] FPS: 64.5 (87.1)  ,  DataTime: 0.181 (0.087)  ,  ForwardTime: 0.725  ,  TotalTime: 0.993  ,  Loss/total: 0.69515  ,  Loss/giou: 0.16760  ,  Loss/l1: 0.01447  ,  Loss/location: 0.28758  ,  IoU: 0.84243
[train: 46, 50 / 117] FPS: 63.6 (87.3)  ,  DataTime: 0.161 (0.085)  ,  ForwardTime: 0.760  ,  TotalTime: 1.006  ,  Loss/total: 0.71955  ,  Loss/giou: 0.17557  ,  Loss/l1: 0.01646  ,  Loss/location: 0.28610  ,  IoU: 0.83869
[train: 46, 100 / 117] FPS: 74.7 (103.9)  ,  DataTime: 0.131 (0.081)  ,  ForwardTime: 0.645  ,  TotalTime: 0.857  ,  Loss/total: 0.70111  ,  Loss/giou: 0.17021  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28457  ,  IoU: 0.84111[train: 46, 100 / 117] FPS: 72.7 (103.6)  ,  DataTime: 0.108 (0.084)  ,  ForwardTime: 0.689  ,  TotalTime: 0.880  ,  Loss/total: 0.71878  ,  Loss/giou: 0.17619  ,  Loss/l1: 0.01591  ,  Loss/location: 0.28682  ,  IoU: 0.83680

[train: 46, 100 / 117] FPS: 73.0 (103.6)  ,  DataTime: 0.112 (0.084)  ,  ForwardTime: 0.681  ,  TotalTime: 0.877  ,  Loss/total: 0.72026  ,  Loss/giou: 0.17495  ,  Loss/l1: 0.01633  ,  Loss/location: 0.28869  ,  IoU: 0.83846
[train: 46, 100 / 117] FPS: 74.7 (103.8)  ,  DataTime: 0.123 (0.080)  ,  ForwardTime: 0.654  ,  TotalTime: 0.857  ,  Loss/total: 0.69415  ,  Loss/giou: 0.16938  ,  Loss/l1: 0.01529  ,  Loss/location: 0.27892  ,  IoU: 0.84269
[train: 46, 100 / 117] FPS: 73.1 (103.5)  ,  DataTime: 0.116 (0.085)  ,  ForwardTime: 0.675  ,  TotalTime: 0.876  ,  Loss/total: 0.71169  ,  Loss/giou: 0.17201  ,  Loss/l1: 0.01544  ,  Loss/location: 0.29050  ,  IoU: 0.83997
[train: 46, 100 / 117] FPS: 75.2 (103.6)  ,  DataTime: 0.136 (0.086)  ,  ForwardTime: 0.629  ,  TotalTime: 0.851  ,  Loss/total: 0.72091  ,  Loss/giou: 0.17570  ,  Loss/l1: 0.01624  ,  Loss/location: 0.28834  ,  IoU: 0.83789
[train: 46, 100 / 117] FPS: 72.5 (103.7)  ,  DataTime: 0.107 (0.083)  ,  ForwardTime: 0.692  ,  TotalTime: 0.883  ,  Loss/total: 0.72245  ,  Loss/giou: 0.17445  ,  Loss/l1: 0.01633  ,  Loss/location: 0.29190  ,  IoU: 0.83961
[train: 46, 100 / 117] FPS: 72.2 (103.4)  ,  DataTime: 0.103 (0.085)  ,  ForwardTime: 0.699  ,  TotalTime: 0.886  ,  Loss/total: 0.70160  ,  Loss/giou: 0.17307  ,  Loss/l1: 0.01574  ,  Loss/location: 0.27677  ,  IoU: 0.83976
[train: 46, 117 / 117] FPS: 76.4 (108.6)  ,  DataTime: 0.097 (0.080)  ,  ForwardTime: 0.661  ,  TotalTime: 0.838  ,  Loss/total: 0.71991  ,  Loss/giou: 0.17615  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28786  ,  IoU: 0.83695
[train: 46, 117 / 117] FPS: 76.2 (108.7)  ,  DataTime: 0.096 (0.079)  ,  ForwardTime: 0.665  ,  TotalTime: 0.840  ,  Loss/total: 0.71844  ,  Loss/giou: 0.17359  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29082  ,  IoU: 0.83990
[train: 46, 117 / 117] FPS: 76.6 (108.7)  ,  DataTime: 0.101 (0.078)  ,  ForwardTime: 0.656  ,  TotalTime: 0.835  ,  Loss/total: 0.72477  ,  Loss/giou: 0.17590  ,  Loss/l1: 0.01639  ,  Loss/location: 0.29100  ,  IoU: 0.83759
[train: 46, 117 / 117] FPS: 78.7 (108.6)  ,  DataTime: 0.121 (0.080)  ,  ForwardTime: 0.612  ,  TotalTime: 0.813  ,  Loss/total: 0.71452  ,  Loss/giou: 0.17407  ,  Loss/l1: 0.01593  ,  Loss/location: 0.28675  ,  IoU: 0.83889
[train: 46, 117 / 117] FPS: 76.7 (108.6)  ,  DataTime: 0.104 (0.080)  ,  ForwardTime: 0.650  ,  TotalTime: 0.834  ,  Loss/total: 0.71434  ,  Loss/giou: 0.17247  ,  Loss/l1: 0.01561  ,  Loss/location: 0.29136  ,  IoU: 0.83982
[train: 46, 117 / 117] FPS: 75.9 (108.6)  ,  DataTime: 0.093 (0.079)  ,  ForwardTime: 0.672  ,  TotalTime: 0.843  ,  Loss/total: 0.70260  ,  Loss/giou: 0.17269  ,  Loss/l1: 0.01565  ,  Loss/location: 0.27896  ,  IoU: 0.83970
[train: 46, 117 / 117] FPS: 78.3 (108.4)  ,  DataTime: 0.110 (0.075)  ,  ForwardTime: 0.633  ,  TotalTime: 0.818  ,  Loss/total: 0.69711  ,  Loss/giou: 0.17043  ,  Loss/l1: 0.01535  ,  Loss/location: 0.27948  ,  IoU: 0.84164
[train: 46, 117 / 117] FPS: 78.2 (108.4)  ,  DataTime: 0.117 (0.076)  ,  ForwardTime: 0.625  ,  TotalTime: 0.818  ,  Loss/total: 0.70188  ,  Loss/giou: 0.17028  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28523  ,  IoU: 0.84119
Epoch Time: 0:01:38.056828
Avg Data Time: 0.09696
Avg GPU Trans Time: 0.07977
Avg Forward Time: 0.66136
Epoch Time: 0:01:35.146482
Avg Data Time: 0.12105
Avg GPU Trans Time: 0.08025
Avg Forward Time: 0.61192
Epoch Time: 0:01:37.595712
Avg Data Time: 0.10424
Avg GPU Trans Time: 0.07987
Avg Forward Time: 0.65004
Epoch Time: 0:01:38.658978Epoch Time: 0:01:38.261717

Avg Data Time: 0.09645Avg Data Time: 0.09268

Avg GPU Trans Time: 0.07882
Avg GPU Trans Time: 0.07866Avg Forward Time: 0.66457

Avg Forward Time: 0.67190
Epoch Time: 0:01:37.728358
Avg Data Time: 0.10079
Avg GPU Trans Time: 0.07837
Avg Forward Time: 0.65613
Epoch Time: 0:01:35.686628
Avg Data Time: 0.11030
Avg GPU Trans Time: 0.07474
Avg Forward Time: 0.63280
Epoch Time: 0:01:35.735872
Avg Data Time: 0.11716
Avg GPU Trans Time: 0.07643
Avg Forward Time: 0.62467
[train: 47, 50 / 117] FPS: 64.4 (85.2)  ,  DataTime: 0.192 (0.088)  ,  ForwardTime: 0.714  ,  TotalTime: 0.993  ,  Loss/total: 0.69584  ,  Loss/giou: 0.16866  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28267  ,  IoU: 0.84347
[train: 47, 50 / 117] FPS: 64.4 (85.2)  ,  DataTime: 0.181 (0.087)  ,  ForwardTime: 0.725  ,  TotalTime: 0.993  ,  Loss/total: 0.71241  ,  Loss/giou: 0.17216  ,  Loss/l1: 0.01548  ,  Loss/location: 0.29070  ,  IoU: 0.84016
[train: 47, 50 / 117] FPS: 64.4 (85.5)  ,  DataTime: 0.187 (0.088)  ,  ForwardTime: 0.719  ,  TotalTime: 0.993  ,  Loss/total: 0.71278  ,  Loss/giou: 0.17237  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28869  ,  IoU: 0.83997
[train: 47, 50 / 117] FPS: 64.4 (85.5)  ,  DataTime: 0.220 (0.070)  ,  ForwardTime: 0.703  ,  TotalTime: 0.993  ,  Loss/total: 0.70675  ,  Loss/giou: 0.17171  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28680  ,  IoU: 0.83976
[train: 47, 50 / 117] FPS: 64.4 (84.8)  ,  DataTime: 0.191 (0.084)  ,  ForwardTime: 0.718  ,  TotalTime: 0.993  ,  Loss/total: 0.73979  ,  Loss/giou: 0.18223  ,  Loss/l1: 0.01764  ,  Loss/location: 0.28711  ,  IoU: 0.83392[train: 47, 50 / 117] FPS: 64.4 (85.2)  ,  DataTime: 0.222 (0.077)  ,  ForwardTime: 0.695  ,  TotalTime: 0.993  ,  Loss/total: 0.69167  ,  Loss/giou: 0.16800  ,  Loss/l1: 0.01523  ,  Loss/location: 0.27950  ,  IoU: 0.84388

[train: 47, 50 / 117] FPS: 64.4 (85.2)  ,  DataTime: 0.202 (0.085)  ,  ForwardTime: 0.706  ,  TotalTime: 0.993  ,  Loss/total: 0.68858  ,  Loss/giou: 0.16928  ,  Loss/l1: 0.01510  ,  Loss/location: 0.27450  ,  IoU: 0.84166
[train: 47, 50 / 117] FPS: 64.4 (84.5)  ,  DataTime: 0.177 (0.072)  ,  ForwardTime: 0.744  ,  TotalTime: 0.993  ,  Loss/total: 0.72666  ,  Loss/giou: 0.17756  ,  Loss/l1: 0.01680  ,  Loss/location: 0.28755  ,  IoU: 0.83698
[train: 47, 100 / 117] FPS: 73.4 (105.5)  ,  DataTime: 0.119 (0.083)  ,  ForwardTime: 0.671  ,  TotalTime: 0.872  ,  Loss/total: 0.70324  ,  Loss/giou: 0.17056  ,  Loss/l1: 0.01537  ,  Loss/location: 0.28528  ,  IoU: 0.84091[train: 47, 100 / 117] FPS: 73.4 (105.4)  ,  DataTime: 0.138 (0.078)  ,  ForwardTime: 0.657  ,  TotalTime: 0.873  ,  Loss/total: 0.71509  ,  Loss/giou: 0.17448  ,  Loss/l1: 0.01625  ,  Loss/location: 0.28488  ,  IoU: 0.83900

[train: 47, 100 / 117] FPS: 73.4 (105.2)  ,  DataTime: 0.115 (0.083)  ,  ForwardTime: 0.674  ,  TotalTime: 0.872  ,  Loss/total: 0.69519  ,  Loss/giou: 0.16926  ,  Loss/l1: 0.01519  ,  Loss/location: 0.28070  ,  IoU: 0.84256
[train: 47, 100 / 117] FPS: 73.4 (105.1)  ,  DataTime: 0.120 (0.081)  ,  ForwardTime: 0.671  ,  TotalTime: 0.872  ,  Loss/total: 0.74841  ,  Loss/giou: 0.18186  ,  Loss/l1: 0.01745  ,  Loss/location: 0.29745  ,  IoU: 0.83392
[train: 47, 100 / 117] FPS: 73.4 (105.4)  ,  DataTime: 0.126 (0.082)  ,  ForwardTime: 0.664  ,  TotalTime: 0.872  ,  Loss/total: 0.69535  ,  Loss/giou: 0.16911  ,  Loss/l1: 0.01510  ,  Loss/location: 0.28162  ,  IoU: 0.84205
[train: 47, 100 / 117] FPS: 73.4 (105.5)  ,  DataTime: 0.116 (0.071)  ,  ForwardTime: 0.686  ,  TotalTime: 0.872  ,  Loss/total: 0.70168  ,  Loss/giou: 0.17132  ,  Loss/l1: 0.01574  ,  Loss/location: 0.28035  ,  IoU: 0.84143
[train: 47, 100 / 117] FPS: 73.4 (104.8)  ,  DataTime: 0.121 (0.082)  ,  ForwardTime: 0.669  ,  TotalTime: 0.872  ,  Loss/total: 0.70973  ,  Loss/giou: 0.17264  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28555  ,  IoU: 0.84019
[train: 47, 100 / 117] FPS: 73.4 (104.6)  ,  DataTime: 0.136 (0.068)  ,  ForwardTime: 0.668  ,  TotalTime: 0.872  ,  Loss/total: 0.68628  ,  Loss/giou: 0.16678  ,  Loss/l1: 0.01462  ,  Loss/location: 0.27961  ,  IoU: 0.84371
[train: 47, 117 / 117] FPS: 77.0 (111.7)  ,  DataTime: 0.106 (0.078)  ,  ForwardTime: 0.647  ,  TotalTime: 0.831  ,  Loss/total: 0.71087  ,  Loss/giou: 0.17274  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28661  ,  IoU: 0.83945
[train: 47, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.104 (0.067)  ,  ForwardTime: 0.660  ,  TotalTime: 0.831  ,  Loss/total: 0.70911  ,  Loss/giou: 0.17261  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28452  ,  IoU: 0.84037
[train: 47, 117 / 117] FPS: 77.0 (111.7)  ,  DataTime: 0.121 (0.065)  ,  ForwardTime: 0.645  ,  TotalTime: 0.831  ,  Loss/total: 0.68885  ,  Loss/giou: 0.16764  ,  Loss/l1: 0.01483  ,  Loss/location: 0.27942  ,  IoU: 0.84333
[train: 47, 117 / 117] FPS: 77.0 (111.9)  ,  DataTime: 0.103 (0.077)  ,  ForwardTime: 0.650  ,  TotalTime: 0.831  ,  Loss/total: 0.69662  ,  Loss/giou: 0.17019  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27958  ,  IoU: 0.84183
[train: 47, 117 / 117] FPS: 77.0 (111.7)  ,  DataTime: 0.112 (0.077)  ,  ForwardTime: 0.641  ,  TotalTime: 0.831  ,  Loss/total: 0.69676  ,  Loss/giou: 0.16934  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28242  ,  IoU: 0.84197
[train: 47, 117 / 117] FPS: 77.0 (111.7)  ,  DataTime: 0.108 (0.077)  ,  ForwardTime: 0.645  ,  TotalTime: 0.831  ,  Loss/total: 0.70566  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28393  ,  IoU: 0.84047
[train: 47, 117 / 117] FPS: 77.1 (111.7)  ,  DataTime: 0.107 (0.077)  ,  ForwardTime: 0.647  ,  TotalTime: 0.831  ,  Loss/total: 0.74654  ,  Loss/giou: 0.18108  ,  Loss/l1: 0.01714  ,  Loss/location: 0.29868  ,  IoU: 0.83406
[train: 47, 117 / 117] FPS: 77.0 (111.8)  ,  DataTime: 0.123 (0.073)  ,  ForwardTime: 0.635  ,  TotalTime: 0.831  ,  Loss/total: 0.70518  ,  Loss/giou: 0.17274  ,  Loss/l1: 0.01596  ,  Loss/location: 0.27988  ,  IoU: 0.84032
Epoch Time: 0:01:37.189530
Avg Data Time: 0.11225
Avg GPU Trans Time: 0.07715
Avg Forward Time: 0.64128
Epoch Time: 0:01:37.181622
Avg Data Time: 0.10700
Avg GPU Trans Time: 0.07657
Avg Forward Time: 0.64704
Epoch Time: 0:01:37.191193
Avg Data Time: 0.10611
Avg GPU Trans Time: 0.07753
Avg Forward Time: 0.64705
Epoch Time: 0:01:37.185748
Avg Data Time: 0.10322
Avg GPU Trans Time: 0.07730
Avg Forward Time: 0.65012
Epoch Time: 0:01:37.187668
Avg Data Time: 0.10824
Avg GPU Trans Time: 0.07731
Avg Forward Time: 0.64511
Epoch Time: 0:01:37.197911
Avg Data Time: 0.12282
Avg GPU Trans Time: 0.07276
Avg Forward Time: 0.63517
Epoch Time: 0:01:37.187649
Avg Data Time: 0.12087
Avg GPU Trans Time: 0.06504
Avg Forward Time: 0.64475
Epoch Time: 0:01:37.184984
Avg Data Time: 0.10399
Avg GPU Trans Time: 0.06702
Avg Forward Time: 0.65963
[train: 48, 50 / 117] FPS: 64.3 (85.7)  ,  DataTime: 0.216 (0.082)  ,  ForwardTime: 0.697  ,  TotalTime: 0.996  ,  Loss/total: 0.70178  ,  Loss/giou: 0.16890  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28872  ,  IoU: 0.84245
[train: 48, 50 / 117] FPS: 64.3 (85.7)  ,  DataTime: 0.197 (0.086)  ,  ForwardTime: 0.713  ,  TotalTime: 0.996  ,  Loss/total: 0.71550  ,  Loss/giou: 0.17358  ,  Loss/l1: 0.01574  ,  Loss/location: 0.28964  ,  IoU: 0.83851
[train: 48, 50 / 117] FPS: 64.3 (85.6)  ,  DataTime: 0.201 (0.088)  ,  ForwardTime: 0.707  ,  TotalTime: 0.996  ,  Loss/total: 0.70636  ,  Loss/giou: 0.17270  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28203  ,  IoU: 0.84033
[train: 48, 50 / 117] FPS: 64.2 (86.3)  ,  DataTime: 0.219 (0.091)  ,  ForwardTime: 0.686  ,  TotalTime: 0.996  ,  Loss/total: 0.70549  ,  Loss/giou: 0.16941  ,  Loss/l1: 0.01516  ,  Loss/location: 0.29086  ,  IoU: 0.84164
[train: 48, 50 / 117] FPS: 64.2 (85.7)  ,  DataTime: 0.185 (0.091)  ,  ForwardTime: 0.720  ,  TotalTime: 0.996  ,  Loss/total: 0.69331  ,  Loss/giou: 0.16853  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28094  ,  IoU: 0.84262
[train: 48, 50 / 117] FPS: 64.2 (85.7)  ,  DataTime: 0.194 (0.092)  ,  ForwardTime: 0.711  ,  TotalTime: 0.997  ,  Loss/total: 0.70454  ,  Loss/giou: 0.17227  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28113  ,  IoU: 0.83995
[train: 48, 50 / 117] FPS: 64.2 (85.6)  ,  DataTime: 0.195 (0.087)  ,  ForwardTime: 0.715  ,  TotalTime: 0.997  ,  Loss/total: 0.68340  ,  Loss/giou: 0.16779  ,  Loss/l1: 0.01526  ,  Loss/location: 0.27150  ,  IoU: 0.84384
[train: 48, 50 / 117] FPS: 64.3 (85.4)  ,  DataTime: 0.189 (0.076)  ,  ForwardTime: 0.731  ,  TotalTime: 0.996  ,  Loss/total: 0.71133  ,  Loss/giou: 0.17363  ,  Loss/l1: 0.01588  ,  Loss/location: 0.28469  ,  IoU: 0.83940
[train: 48, 100 / 117] FPS: 72.8 (108.8)  ,  DataTime: 0.124 (0.083)  ,  ForwardTime: 0.672  ,  TotalTime: 0.879  ,  Loss/total: 0.71640  ,  Loss/giou: 0.17415  ,  Loss/l1: 0.01593  ,  Loss/location: 0.28847  ,  IoU: 0.83839[train: 48, 100 / 117] FPS: 72.8 (108.8)  ,  DataTime: 0.124 (0.082)  ,  ForwardTime: 0.674  ,  TotalTime: 0.879  ,  Loss/total: 0.69955  ,  Loss/giou: 0.17027  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28160  ,  IoU: 0.84164

[train: 48, 100 / 117] FPS: 72.8 (108.8)  ,  DataTime: 0.124 (0.087)  ,  ForwardTime: 0.668  ,  TotalTime: 0.879  ,  Loss/total: 0.70766  ,  Loss/giou: 0.17269  ,  Loss/l1: 0.01575  ,  Loss/location: 0.28356  ,  IoU: 0.83991
[train: 48, 100 / 117] FPS: 72.8 (108.8)  ,  DataTime: 0.137 (0.086)  ,  ForwardTime: 0.656  ,  TotalTime: 0.879  ,  Loss/total: 0.70436  ,  Loss/giou: 0.17004  ,  Loss/l1: 0.01515  ,  Loss/location: 0.28854  ,  IoU: 0.84109
[train: 48, 100 / 117] FPS: 72.9 (108.7)  ,  DataTime: 0.134 (0.078)  ,  ForwardTime: 0.666  ,  TotalTime: 0.878  ,  Loss/total: 0.70695  ,  Loss/giou: 0.17132  ,  Loss/l1: 0.01560  ,  Loss/location: 0.28629  ,  IoU: 0.84119
[train: 48, 100 / 117] FPS: 72.8 (108.8)  ,  DataTime: 0.125 (0.079)  ,  ForwardTime: 0.674  ,  TotalTime: 0.879  ,  Loss/total: 0.70440  ,  Loss/giou: 0.17123  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28404  ,  IoU: 0.84128
[train: 48, 100 / 117] FPS: 72.8 (109.2)  ,  DataTime: 0.119 (0.087)  ,  ForwardTime: 0.673  ,  TotalTime: 0.879  ,  Loss/total: 0.69837  ,  Loss/giou: 0.17013  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28102  ,  IoU: 0.84187
[train: 48, 100 / 117] FPS: 72.8 (109.4)  ,  DataTime: 0.121 (0.075)  ,  ForwardTime: 0.683  ,  TotalTime: 0.879  ,  Loss/total: 0.71675  ,  Loss/giou: 0.17437  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28807  ,  IoU: 0.83863
[train: 48, 117 / 117] FPS: 76.5 (107.5)  ,  DataTime: 0.111 (0.078)  ,  ForwardTime: 0.648  ,  TotalTime: 0.837  ,  Loss/total: 0.71807  ,  Loss/giou: 0.17447  ,  Loss/l1: 0.01603  ,  Loss/location: 0.28897  ,  IoU: 0.83832[train: 48, 117 / 117] FPS: 76.4 (107.5)  ,  DataTime: 0.110 (0.077)  ,  ForwardTime: 0.650  ,  TotalTime: 0.837  ,  Loss/total: 0.69728  ,  Loss/giou: 0.16916  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28258  ,  IoU: 0.84241[train: 48, 117 / 117] FPS: 76.4 (107.5)  ,  DataTime: 0.122 (0.080)  ,  ForwardTime: 0.635  ,  TotalTime: 0.837  ,  Loss/total: 0.70861  ,  Loss/giou: 0.17082  ,  Loss/l1: 0.01529  ,  Loss/location: 0.29050  ,  IoU: 0.84062


[train: 48, 117 / 117] FPS: 76.4 (107.5)  ,  DataTime: 0.111 (0.082)  ,  ForwardTime: 0.645  ,  TotalTime: 0.837  ,  Loss/total: 0.70646  ,  Loss/giou: 0.17254  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28278  ,  IoU: 0.84003[train: 48, 117 / 117] FPS: 76.5 (107.5)  ,  DataTime: 0.107 (0.081)  ,  ForwardTime: 0.649  ,  TotalTime: 0.837  ,  Loss/total: 0.70335  ,  Loss/giou: 0.17100  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28361  ,  IoU: 0.84141

[train: 48, 117 / 117] FPS: 76.5 (107.5)  ,  DataTime: 0.120 (0.073)  ,  ForwardTime: 0.643  ,  TotalTime: 0.837  ,  Loss/total: 0.71385  ,  Loss/giou: 0.17364  ,  Loss/l1: 0.01599  ,  Loss/location: 0.28662  ,  IoU: 0.83956
[train: 48, 117 / 117] FPS: 76.5 (107.2)  ,  DataTime: 0.109 (0.070)  ,  ForwardTime: 0.658  ,  TotalTime: 0.837  ,  Loss/total: 0.72007  ,  Loss/giou: 0.17533  ,  Loss/l1: 0.01621  ,  Loss/location: 0.28839  ,  IoU: 0.83799
[train: 48, 117 / 117] FPS: 76.5 (107.3)  ,  DataTime: 0.113 (0.074)  ,  ForwardTime: 0.650  ,  TotalTime: 0.837  ,  Loss/total: 0.70620  ,  Loss/giou: 0.17123  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28548  ,  IoU: 0.84139
Epoch Time: 0:01:37.957944
Avg Data Time: 0.11045
Avg GPU Trans Time: 0.07659
Avg Forward Time: 0.65020
Epoch Time: 0:01:37.923422
Avg Data Time: 0.11085
Avg GPU Trans Time: 0.07812
Avg Forward Time: 0.64799
Epoch Time: 0:01:37.946823
Avg Data Time: 0.12233
Avg GPU Trans Time: 0.07951
Avg Forward Time: 0.63531
Epoch Time: 0:01:37.908829
Avg Data Time: 0.12026
Avg GPU Trans Time: 0.07335
Avg Forward Time: 0.64321
Epoch Time: 0:01:37.973840
Avg Data Time: 0.11080
Avg GPU Trans Time: 0.08159
Avg Forward Time: 0.64500
Epoch Time: 0:01:37.918541
Avg Data Time: 0.10860
Avg GPU Trans Time: 0.07028
Avg Forward Time: 0.65803
Epoch Time: 0:01:37.931589
Avg Data Time: 0.11282
Avg GPU Trans Time: 0.07409
Avg Forward Time: 0.65012
Epoch Time: 0:01:37.942486
Avg Data Time: 0.10666
Avg GPU Trans Time: 0.08147
Avg Forward Time: 0.64899
[train: 49, 50 / 117] FPS: 64.5 (84.8)  ,  DataTime: 0.177 (0.074)  ,  ForwardTime: 0.742  ,  TotalTime: 0.993  ,  Loss/total: 0.69871  ,  Loss/giou: 0.16975  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28197  ,  IoU: 0.84282
[train: 49, 50 / 117] FPS: 64.5 (84.8)  ,  DataTime: 0.183 (0.082)  ,  ForwardTime: 0.728  ,  TotalTime: 0.993  ,  Loss/total: 0.69773  ,  Loss/giou: 0.17072  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28050  ,  IoU: 0.84012
[train: 49, 50 / 117] FPS: 64.4 (84.7)  ,  DataTime: 0.187 (0.079)  ,  ForwardTime: 0.728  ,  TotalTime: 0.994  ,  Loss/total: 0.72107  ,  Loss/giou: 0.17422  ,  Loss/l1: 0.01609  ,  Loss/location: 0.29221  ,  IoU: 0.83863
[train: 49, 50 / 117] FPS: 64.4 (84.6)  ,  DataTime: 0.204 (0.079)  ,  ForwardTime: 0.711  ,  TotalTime: 0.994  ,  Loss/total: 0.70778  ,  Loss/giou: 0.17165  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28758  ,  IoU: 0.83985
[train: 49, 50 / 117] FPS: 64.4 (84.5)  ,  DataTime: 0.189 (0.078)  ,  ForwardTime: 0.726  ,  TotalTime: 0.993  ,  Loss/total: 0.68636  ,  Loss/giou: 0.16777  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27428  ,  IoU: 0.84460
[train: 49, 50 / 117] FPS: 64.5 (84.2)  ,  DataTime: 0.258 (0.071)  ,  ForwardTime: 0.664  ,  TotalTime: 0.993  ,  Loss/total: 0.71252  ,  Loss/giou: 0.17097  ,  Loss/l1: 0.01563  ,  Loss/location: 0.29243  ,  IoU: 0.84134
[train: 49, 50 / 117] FPS: 64.4 (84.4)  ,  DataTime: 0.169 (0.081)  ,  ForwardTime: 0.743  ,  TotalTime: 0.993  ,  Loss/total: 0.67690  ,  Loss/giou: 0.16557  ,  Loss/l1: 0.01480  ,  Loss/location: 0.27175  ,  IoU: 0.84565
[train: 49, 50 / 117] FPS: 64.4 (84.3)  ,  DataTime: 0.170 (0.076)  ,  ForwardTime: 0.748  ,  TotalTime: 0.993  ,  Loss/total: 0.70409  ,  Loss/giou: 0.17028  ,  Loss/l1: 0.01539  ,  Loss/location: 0.28660  ,  IoU: 0.84160
[train: 49, 100 / 117] FPS: 73.4 (101.5)  ,  DataTime: 0.118 (0.078)  ,  ForwardTime: 0.677  ,  TotalTime: 0.872  ,  Loss/total: 0.72991  ,  Loss/giou: 0.17695  ,  Loss/l1: 0.01619  ,  Loss/location: 0.29504  ,  IoU: 0.83593[train: 49, 100 / 117] FPS: 73.4 (101.7)  ,  DataTime: 0.119 (0.082)  ,  ForwardTime: 0.670  ,  TotalTime: 0.872  ,  Loss/total: 0.69153  ,  Loss/giou: 0.16932  ,  Loss/l1: 0.01516  ,  Loss/location: 0.27706  ,  IoU: 0.84194[train: 49, 100 / 117] FPS: 73.4 (101.6)  ,  DataTime: 0.110 (0.080)  ,  ForwardTime: 0.683  ,  TotalTime: 0.872  ,  Loss/total: 0.68598  ,  Loss/giou: 0.16764  ,  Loss/l1: 0.01492  ,  Loss/location: 0.27610  ,  IoU: 0.84347


[train: 49, 100 / 117] FPS: 73.4 (101.5)  ,  DataTime: 0.111 (0.076)  ,  ForwardTime: 0.685  ,  TotalTime: 0.872  ,  Loss/total: 0.70798  ,  Loss/giou: 0.17217  ,  Loss/l1: 0.01583  ,  Loss/location: 0.28450  ,  IoU: 0.84042[train: 49, 100 / 117] FPS: 73.4 (101.8)  ,  DataTime: 0.119 (0.077)  ,  ForwardTime: 0.677  ,  TotalTime: 0.872  ,  Loss/total: 0.67545  ,  Loss/giou: 0.16471  ,  Loss/l1: 0.01459  ,  Loss/location: 0.27305  ,  IoU: 0.84633

[train: 49, 100 / 117] FPS: 73.4 (101.8)  ,  DataTime: 0.155 (0.073)  ,  ForwardTime: 0.644  ,  TotalTime: 0.872  ,  Loss/total: 0.71141  ,  Loss/giou: 0.17173  ,  Loss/l1: 0.01584  ,  Loss/location: 0.28873  ,  IoU: 0.84088[train: 49, 100 / 117] FPS: 73.4 (101.8)  ,  DataTime: 0.115 (0.072)  ,  ForwardTime: 0.685  ,  TotalTime: 0.872  ,  Loss/total: 0.71053  ,  Loss/giou: 0.17250  ,  Loss/l1: 0.01592  ,  Loss/location: 0.28591  ,  IoU: 0.84043

[train: 49, 100 / 117] FPS: 73.4 (101.3)  ,  DataTime: 0.127 (0.079)  ,  ForwardTime: 0.667  ,  TotalTime: 0.873  ,  Loss/total: 0.70240  ,  Loss/giou: 0.17038  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28585  ,  IoU: 0.84115
[train: 49, 117 / 117] FPS: 77.0 (107.8)  ,  DataTime: 0.106 (0.073)  ,  ForwardTime: 0.653  ,  TotalTime: 0.831  ,  Loss/total: 0.72762  ,  Loss/giou: 0.17669  ,  Loss/l1: 0.01622  ,  Loss/location: 0.29313  ,  IoU: 0.83632
[train: 49, 117 / 117] FPS: 77.0 (107.8)  ,  DataTime: 0.099 (0.075)  ,  ForwardTime: 0.657  ,  TotalTime: 0.831  ,  Loss/total: 0.69287  ,  Loss/giou: 0.16953  ,  Loss/l1: 0.01515  ,  Loss/location: 0.27804  ,  IoU: 0.84195
[train: 49, 117 / 117] FPS: 77.0 (108.0)  ,  DataTime: 0.114 (0.075)  ,  ForwardTime: 0.642  ,  TotalTime: 0.832  ,  Loss/total: 0.70533  ,  Loss/giou: 0.17112  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28609  ,  IoU: 0.84083
[train: 49, 117 / 117] FPS: 77.0 (107.9)  ,  DataTime: 0.107 (0.077)  ,  ForwardTime: 0.647  ,  TotalTime: 0.831  ,  Loss/total: 0.69782  ,  Loss/giou: 0.17060  ,  Loss/l1: 0.01534  ,  Loss/location: 0.27989  ,  IoU: 0.84099
[train: 49, 117 / 117] FPS: 77.0 (107.9)  ,  DataTime: 0.137 (0.068)  ,  ForwardTime: 0.626  ,  TotalTime: 0.831  ,  Loss/total: 0.70967  ,  Loss/giou: 0.17103  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28944  ,  IoU: 0.84117[train: 49, 117 / 117] FPS: 77.0 (107.9)  ,  DataTime: 0.099 (0.073)  ,  ForwardTime: 0.659  ,  TotalTime: 0.831  ,  Loss/total: 0.70072  ,  Loss/giou: 0.17069  ,  Loss/l1: 0.01551  ,  Loss/location: 0.28178  ,  IoU: 0.84151

[train: 49, 117 / 117] FPS: 77.0 (107.9)  ,  DataTime: 0.103 (0.068)  ,  ForwardTime: 0.660  ,  TotalTime: 0.831  ,  Loss/total: 0.71748  ,  Loss/giou: 0.17325  ,  Loss/l1: 0.01608  ,  Loss/location: 0.29056  ,  IoU: 0.84012
[train: 49, 117 / 117] FPS: 77.0 (107.9)  ,  DataTime: 0.106 (0.072)  ,  ForwardTime: 0.653  ,  TotalTime: 0.831  ,  Loss/total: 0.68127  ,  Loss/giou: 0.16653  ,  Loss/l1: 0.01487  ,  Loss/location: 0.27386  ,  IoU: 0.84503
Epoch Time: 0:01:37.288817
Avg Data Time: 0.11356
Avg GPU Trans Time: 0.07547
Avg Forward Time: 0.64250
Epoch Time: 0:01:37.281667
Avg Data Time: 0.10563
Avg GPU Trans Time: 0.07325
Avg Forward Time: 0.65258
Epoch Time: 0:01:37.266015
Avg Data Time: 0.09947
Avg GPU Trans Time: 0.07294
Avg Forward Time: 0.65892
Epoch Time: 0:01:37.257602
Avg Data Time: 0.10615
Avg GPU Trans Time: 0.07242
Avg Forward Time: 0.65270
Epoch Time: 0:01:37.268457
Avg Data Time: 0.09880
Avg GPU Trans Time: 0.07520
Avg Forward Time: 0.65736
Epoch Time: 0:01:37.242803
Avg Data Time: 0.10334
Avg GPU Trans Time: 0.06789
Avg Forward Time: 0.65990
Epoch Time: 0:01:37.243087
Avg Data Time: 0.10724
Avg GPU Trans Time: 0.07685
Avg Forward Time: 0.64704
Epoch Time: 0:01:37.234166
Avg Data Time: 0.13715
Avg GPU Trans Time: 0.06840
Avg Forward Time: 0.62551
[train: 50, 50 / 117] FPS: 64.1 (83.9)  ,  DataTime: 0.178 (0.079)  ,  ForwardTime: 0.742  ,  TotalTime: 0.999  ,  Loss/total: 0.71085  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01572  ,  Loss/location: 0.28843  ,  IoU: 0.84094[train: 50, 50 / 117] FPS: 64.1 (84.2)  ,  DataTime: 0.211 (0.085)  ,  ForwardTime: 0.702  ,  TotalTime: 0.998  ,  Loss/total: 0.71383  ,  Loss/giou: 0.17323  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28942  ,  IoU: 0.83943

[train: 50, 50 / 117] FPS: 64.1 (84.1)  ,  DataTime: 0.189 (0.080)  ,  ForwardTime: 0.731  ,  TotalTime: 0.999  ,  Loss/total: 0.68473  ,  Loss/giou: 0.16674  ,  Loss/l1: 0.01448  ,  Loss/location: 0.27883  ,  IoU: 0.84341[train: 50, 50 / 117] FPS: 64.1 (84.3)  ,  DataTime: 0.234 (0.087)  ,  ForwardTime: 0.678  ,  TotalTime: 0.999  ,  Loss/total: 0.69993  ,  Loss/giou: 0.17137  ,  Loss/l1: 0.01527  ,  Loss/location: 0.28085  ,  IoU: 0.84061
[train: 50, 50 / 117] FPS: 64.1 (84.1)  ,  DataTime: 0.196 (0.068)  ,  ForwardTime: 0.735  ,  TotalTime: 0.998  ,  Loss/total: 0.69343  ,  Loss/giou: 0.17010  ,  Loss/l1: 0.01514  ,  Loss/location: 0.27753  ,  IoU: 0.84169

[train: 50, 50 / 117] FPS: 64.1 (84.0)  ,  DataTime: 0.178 (0.080)  ,  ForwardTime: 0.741  ,  TotalTime: 0.999  ,  Loss/total: 0.71076  ,  Loss/giou: 0.17433  ,  Loss/l1: 0.01576  ,  Loss/location: 0.28332  ,  IoU: 0.83833
[train: 50, 50 / 117] FPS: 64.1 (84.1)  ,  DataTime: 0.249 (0.086)  ,  ForwardTime: 0.663  ,  TotalTime: 0.998  ,  Loss/total: 0.71415  ,  Loss/giou: 0.17574  ,  Loss/l1: 0.01678  ,  Loss/location: 0.27878  ,  IoU: 0.83853
[train: 50, 50 / 117] FPS: 64.0 (84.1)  ,  DataTime: 0.169 (0.073)  ,  ForwardTime: 0.758  ,  TotalTime: 1.000  ,  Loss/total: 0.70614  ,  Loss/giou: 0.17107  ,  Loss/l1: 0.01569  ,  Loss/location: 0.28557  ,  IoU: 0.84133
[train: 50, 100 / 117] FPS: 73.0 (99.5)  ,  DataTime: 0.115 (0.080)  ,  ForwardTime: 0.682  ,  TotalTime: 0.877  ,  Loss/total: 0.70099  ,  Loss/giou: 0.17054  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28291  ,  IoU: 0.84157[train: 50, 100 / 117] FPS: 73.0 (99.2)  ,  DataTime: 0.131 (0.084)  ,  ForwardTime: 0.662  ,  TotalTime: 0.876  ,  Loss/total: 0.70431  ,  Loss/giou: 0.17169  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28438  ,  IoU: 0.84004

[train: 50, 100 / 117] FPS: 73.0 (99.2)  ,  DataTime: 0.117 (0.078)  ,  ForwardTime: 0.681  ,  TotalTime: 0.877  ,  Loss/total: 0.69502  ,  Loss/giou: 0.16996  ,  Loss/l1: 0.01539  ,  Loss/location: 0.27813  ,  IoU: 0.84230
[train: 50, 100 / 117] FPS: 72.9 (99.3)  ,  DataTime: 0.111 (0.071)  ,  ForwardTime: 0.696  ,  TotalTime: 0.877  ,  Loss/total: 0.69356  ,  Loss/giou: 0.16883  ,  Loss/l1: 0.01520  ,  Loss/location: 0.27989  ,  IoU: 0.84281
[train: 50, 100 / 117] FPS: 73.0 (99.3)  ,  DataTime: 0.120 (0.076)  ,  ForwardTime: 0.681  ,  TotalTime: 0.877  ,  Loss/total: 0.69365  ,  Loss/giou: 0.16826  ,  Loss/l1: 0.01485  ,  Loss/location: 0.28288  ,  IoU: 0.84254
[train: 50, 100 / 117] FPS: 73.0 (99.2)  ,  DataTime: 0.150 (0.083)  ,  ForwardTime: 0.643  ,  TotalTime: 0.876  ,  Loss/total: 0.71513  ,  Loss/giou: 0.17537  ,  Loss/l1: 0.01659  ,  Loss/location: 0.28141  ,  IoU: 0.83878
[train: 50, 100 / 117] FPS: 73.0 (99.2)  ,  DataTime: 0.124 (0.066)  ,  ForwardTime: 0.687  ,  TotalTime: 0.877  ,  Loss/total: 0.69047  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01505  ,  Loss/location: 0.27801  ,  IoU: 0.84302
[train: 50, 100 / 117] FPS: 73.0 (99.0)  ,  DataTime: 0.142 (0.085)  ,  ForwardTime: 0.650  ,  TotalTime: 0.877  ,  Loss/total: 0.69459  ,  Loss/giou: 0.16953  ,  Loss/l1: 0.01520  ,  Loss/location: 0.27953  ,  IoU: 0.84237
[train: 50, 117 / 117] FPS: 76.6 (111.1)  ,  DataTime: 0.105 (0.073)  ,  ForwardTime: 0.657  ,  TotalTime: 0.835  ,  Loss/total: 0.69779  ,  Loss/giou: 0.17097  ,  Loss/l1: 0.01555  ,  Loss/location: 0.27808  ,  IoU: 0.84157[train: 50, 117 / 117] FPS: 76.7 (111.1)  ,  DataTime: 0.117 (0.078)  ,  ForwardTime: 0.640  ,  TotalTime: 0.835  ,  Loss/total: 0.70757  ,  Loss/giou: 0.17250  ,  Loss/l1: 0.01545  ,  Loss/location: 0.28534  ,  IoU: 0.83951

[train: 50, 117 / 117] FPS: 76.6 (111.1)  ,  DataTime: 0.100 (0.067)  ,  ForwardTime: 0.669  ,  TotalTime: 0.836  ,  Loss/total: 0.68282  ,  Loss/giou: 0.16641  ,  Loss/l1: 0.01483  ,  Loss/location: 0.27586  ,  IoU: 0.84467
[train: 50, 117 / 117] FPS: 76.6 (111.0)  ,  DataTime: 0.103 (0.075)  ,  ForwardTime: 0.657  ,  TotalTime: 0.835  ,  Loss/total: 0.70210  ,  Loss/giou: 0.17114  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28194  ,  IoU: 0.84132
[train: 50, 117 / 117] FPS: 76.6 (111.2)  ,  DataTime: 0.111 (0.063)  ,  ForwardTime: 0.661  ,  TotalTime: 0.835  ,  Loss/total: 0.69115  ,  Loss/giou: 0.16833  ,  Loss/l1: 0.01498  ,  Loss/location: 0.27960  ,  IoU: 0.84309
[train: 50, 117 / 117] FPS: 76.7 (111.3)  ,  DataTime: 0.133 (0.078)  ,  ForwardTime: 0.623  ,  TotalTime: 0.835  ,  Loss/total: 0.71134  ,  Loss/giou: 0.17394  ,  Loss/l1: 0.01629  ,  Loss/location: 0.28199  ,  IoU: 0.83964[train: 50, 117 / 117] FPS: 76.6 (111.0)  ,  DataTime: 0.108 (0.072)  ,  ForwardTime: 0.656  ,  TotalTime: 0.835  ,  Loss/total: 0.69386  ,  Loss/giou: 0.16795  ,  Loss/l1: 0.01485  ,  Loss/location: 0.28368  ,  IoU: 0.84292

[train: 50, 117 / 117] FPS: 76.6 (111.1)  ,  DataTime: 0.127 (0.080)  ,  ForwardTime: 0.629  ,  TotalTime: 0.835  ,  Loss/total: 0.69807  ,  Loss/giou: 0.17027  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28049  ,  IoU: 0.84193
Epoch Time: 0:01:37.690568
Avg Data Time: 0.11691
Avg GPU Trans Time: 0.07839
Avg Forward Time: 0.63966
Epoch Time: 0:01:37.733872
Avg Data Time: 0.10510
Avg GPU Trans Time: 0.07346
Avg Forward Time: 0.65677
Epoch Time: 0:01:37.781194
Avg Data Time: 0.09998
Avg GPU Trans Time: 0.06712
Avg Forward Time: 0.66863
Epoch Time: 0:01:37.718527
Avg Data Time: 0.10794
Avg GPU Trans Time: 0.07169
Avg Forward Time: 0.65557
Epoch Time: 0:01:37.665696
Avg Data Time: 0.13341
Avg GPU Trans Time: 0.07798
Avg Forward Time: 0.62336
Epoch Time: 0:01:37.715688
Avg Data Time: 0.10349
Avg GPU Trans Time: 0.07514
Avg Forward Time: 0.65654
Epoch Time: 0:01:37.716971
Avg Data Time: 0.12651
Avg GPU Trans Time: 0.08012
Avg Forward Time: 0.62856
Epoch Time: 0:01:37.697397
Avg Data Time: 0.11123
Avg GPU Trans Time: 0.06279
Avg Forward Time: 0.66100
[val: 50, 50 / 78] FPS: 39.8 (214.0)  ,  DataTime: 1.090 (0.038)  ,  ForwardTime: 0.481  ,  TotalTime: 1.609  ,  Loss/total: 0.83385  ,  Loss/giou: 0.19810  ,  Loss/l1: 0.02089  ,  Loss/location: 0.33320  ,  IoU: 0.82279
[val: 50, 50 / 78] FPS: 37.8 (191.9)  ,  DataTime: 1.189 (0.044)  ,  ForwardTime: 0.460  ,  TotalTime: 1.694  ,  Loss/total: 0.83822  ,  Loss/giou: 0.20093  ,  Loss/l1: 0.02121  ,  Loss/location: 0.33030  ,  IoU: 0.82011
[val: 50, 50 / 78] FPS: 37.5 (179.4)  ,  DataTime: 1.432 (0.045)  ,  ForwardTime: 0.232  ,  TotalTime: 1.708  ,  Loss/total: 0.81052  ,  Loss/giou: 0.19759  ,  Loss/l1: 0.02043  ,  Loss/location: 0.31317  ,  IoU: 0.82260
[val: 50, 50 / 78] FPS: 36.7 (213.5)  ,  DataTime: 1.430 (0.041)  ,  ForwardTime: 0.273  ,  TotalTime: 1.743  ,  Loss/total: 0.82518  ,  Loss/giou: 0.19965  ,  Loss/l1: 0.02051  ,  Loss/location: 0.32333  ,  IoU: 0.82055
[val: 50, 50 / 78] FPS: 36.7 (211.8)  ,  DataTime: 1.309 (0.043)  ,  ForwardTime: 0.391  ,  TotalTime: 1.743  ,  Loss/total: 0.86160  ,  Loss/giou: 0.20636  ,  Loss/l1: 0.02244  ,  Loss/location: 0.33667  ,  IoU: 0.81679
[val: 50, 50 / 78] FPS: 36.7 (222.3)  ,  DataTime: 1.299 (0.046)  ,  ForwardTime: 0.402  ,  TotalTime: 1.746  ,  Loss/total: 0.85773  ,  Loss/giou: 0.20733  ,  Loss/l1: 0.02216  ,  Loss/location: 0.33224  ,  IoU: 0.81584
[val: 50, 50 / 78] FPS: 36.5 (214.4)  ,  DataTime: 1.483 (0.041)  ,  ForwardTime: 0.230  ,  TotalTime: 1.755  ,  Loss/total: 0.81225  ,  Loss/giou: 0.19655  ,  Loss/l1: 0.02003  ,  Loss/location: 0.31897  ,  IoU: 0.82295
[val: 50, 50 / 78] FPS: 36.1 (240.9)  ,  DataTime: 1.457 (0.044)  ,  ForwardTime: 0.274  ,  TotalTime: 1.775  ,  Loss/total: 0.81423  ,  Loss/giou: 0.19523  ,  Loss/l1: 0.02000  ,  Loss/location: 0.32375  ,  IoU: 0.82438
[val: 50, 78 / 78] FPS: 42.9 (190.6)  ,  DataTime: 1.067 (0.046)  ,  ForwardTime: 0.378  ,  TotalTime: 1.491  ,  Loss/total: 0.82594  ,  Loss/giou: 0.19939  ,  Loss/l1: 0.02084  ,  Loss/location: 0.32294  ,  IoU: 0.82108
Epoch Time: 0:01:56.275422
Avg Data Time: 1.06678
Avg GPU Trans Time: 0.04606
Avg Forward Time: 0.37787
[val: 50, 78 / 78] FPS: 42.7 (195.2)  ,  DataTime: 1.109 (0.047)  ,  ForwardTime: 0.342  ,  TotalTime: 1.497  ,  Loss/total: 0.84472  ,  Loss/giou: 0.20610  ,  Loss/l1: 0.02183  ,  Loss/location: 0.32338  ,  IoU: 0.81663
[val: 50, 78 / 78] FPS: 42.7 (197.8)  ,  DataTime: 1.066 (0.040)  ,  ForwardTime: 0.391  ,  TotalTime: 1.498  ,  Loss/total: 0.82528  ,  Loss/giou: 0.19674  ,  Loss/l1: 0.02051  ,  Loss/location: 0.32922  ,  IoU: 0.82341
Epoch Time: 0:01:56.788261
Avg Data Time: 1.10854
Avg GPU Trans Time: 0.04678
Avg Forward Time: 0.34197
Epoch Time: 0:01:56.811814
Avg Data Time: 1.06642
Avg GPU Trans Time: 0.03977
Avg Forward Time: 0.39140
[val: 50, 78 / 78] FPS: 42.3 (195.9)  ,  DataTime: 1.132 (0.046)  ,  ForwardTime: 0.334  ,  TotalTime: 1.512  ,  Loss/total: 0.85562  ,  Loss/giou: 0.20481  ,  Loss/l1: 0.02220  ,  Loss/location: 0.33503  ,  IoU: 0.81770
Epoch Time: 0:01:57.934669
Avg Data Time: 1.13176
Avg GPU Trans Time: 0.04575
Avg Forward Time: 0.33447
[val: 50, 78 / 78] FPS: 42.2 (188.9)  ,  DataTime: 1.213 (0.043)  ,  ForwardTime: 0.260  ,  TotalTime: 1.516  ,  Loss/total: 0.82851  ,  Loss/giou: 0.20045  ,  Loss/l1: 0.02068  ,  Loss/location: 0.32423  ,  IoU: 0.82021
[val: 50, 78 / 78] FPS: 42.2 (172.8)  ,  DataTime: 1.214 (0.044)  ,  ForwardTime: 0.260  ,  TotalTime: 1.518  ,  Loss/total: 0.82026  ,  Loss/giou: 0.19573  ,  Loss/l1: 0.02030  ,  Loss/location: 0.32731  ,  IoU: 0.82452
[val: 50, 78 / 78] FPS: 42.1 (186.6)  ,  DataTime: 1.240 (0.047)  ,  ForwardTime: 0.232  ,  TotalTime: 1.519  ,  Loss/total: 0.83040  ,  Loss/giou: 0.20071  ,  Loss/l1: 0.02111  ,  Loss/location: 0.32345  ,  IoU: 0.82038
Epoch Time: 0:01:58.281747
Avg Data Time: 1.21350
Avg GPU Trans Time: 0.04327
Avg Forward Time: 0.25966
Epoch Time: 0:01:58.394940
Avg Data Time: 1.21385
Avg GPU Trans Time: 0.04410
Avg Forward Time: 0.25994
Epoch Time: 0:01:58.451939
Avg Data Time: 1.23979
Avg GPU Trans Time: 0.04687
Avg Forward Time: 0.23196
[val: 50, 78 / 78] FPS: 41.7 (179.1)  ,  DataTime: 1.258 (0.044)  ,  ForwardTime: 0.234  ,  TotalTime: 1.535  ,  Loss/total: 0.81802  ,  Loss/giou: 0.19788  ,  Loss/l1: 0.02055  ,  Loss/location: 0.31951  ,  IoU: 0.82264
Epoch Time: 0:01:59.765655
Avg Data Time: 1.25821
Avg GPU Trans Time: 0.04363
Avg Forward Time: 0.23362
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[train: 51, 50 / 117] FPS: 62.5 (83.1)  ,  DataTime: 0.158 (0.082)  ,  ForwardTime: 0.785  ,  TotalTime: 1.024  ,  Loss/total: 0.70938  ,  Loss/giou: 0.17260  ,  Loss/l1: 0.01554  ,  Loss/location: 0.28652  ,  IoU: 0.83974
[train: 51, 50 / 117] FPS: 64.6 (83.3)  ,  DataTime: 0.203 (0.077)  ,  ForwardTime: 0.711  ,  TotalTime: 0.991  ,  Loss/total: 0.70947  ,  Loss/giou: 0.17198  ,  Loss/l1: 0.01581  ,  Loss/location: 0.28643  ,  IoU: 0.84073
[train: 51, 50 / 117] FPS: 61.8 (82.8)  ,  DataTime: 0.157 (0.084)  ,  ForwardTime: 0.794  ,  TotalTime: 1.035  ,  Loss/total: 0.71356  ,  Loss/giou: 0.17334  ,  Loss/l1: 0.01573  ,  Loss/location: 0.28820  ,  IoU: 0.83883
[train: 51, 50 / 117] FPS: 64.3 (82.8)  ,  DataTime: 0.196 (0.083)  ,  ForwardTime: 0.716  ,  TotalTime: 0.995  ,  Loss/total: 0.70429  ,  Loss/giou: 0.17065  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28486  ,  IoU: 0.84180
[train: 51, 50 / 117] FPS: 64.5 (82.8)  ,  DataTime: 0.176 (0.083)  ,  ForwardTime: 0.733  ,  TotalTime: 0.992  ,  Loss/total: 0.69637  ,  Loss/giou: 0.17225  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27534  ,  IoU: 0.83958
[train: 51, 50 / 117] FPS: 62.5 (82.8)  ,  DataTime: 0.157 (0.080)  ,  ForwardTime: 0.787  ,  TotalTime: 1.023  ,  Loss/total: 0.70609  ,  Loss/giou: 0.17185  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28305  ,  IoU: 0.84107[train: 51, 50 / 117] FPS: 68.0 (82.8)  ,  DataTime: 0.214 (0.072)  ,  ForwardTime: 0.656  ,  TotalTime: 0.941  ,  Loss/total: 0.69186  ,  Loss/giou: 0.16838  ,  Loss/l1: 0.01500  ,  Loss/location: 0.28011  ,  IoU: 0.84327

[train: 51, 50 / 117] FPS: 63.9 (82.9)  ,  DataTime: 0.185 (0.076)  ,  ForwardTime: 0.741  ,  TotalTime: 1.002  ,  Loss/total: 0.69240  ,  Loss/giou: 0.16876  ,  Loss/l1: 0.01533  ,  Loss/location: 0.27823  ,  IoU: 0.84320
[train: 51, 100 / 117] FPS: 72.9 (107.6)  ,  DataTime: 0.127 (0.083)  ,  ForwardTime: 0.668  ,  TotalTime: 0.878  ,  Loss/total: 0.70135  ,  Loss/giou: 0.17059  ,  Loss/l1: 0.01557  ,  Loss/location: 0.28231  ,  IoU: 0.84172[train: 51, 100 / 117] FPS: 73.0 (107.8)  ,  DataTime: 0.130 (0.078)  ,  ForwardTime: 0.669  ,  TotalTime: 0.876  ,  Loss/total: 0.70403  ,  Loss/giou: 0.16964  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28917  ,  IoU: 0.84178[train: 51, 100 / 117] FPS: 75.2 (107.7)  ,  DataTime: 0.133 (0.067)  ,  ForwardTime: 0.652  ,  TotalTime: 0.851  ,  Loss/total: 0.69970  ,  Loss/giou: 0.17181  ,  Loss/l1: 0.01566  ,  Loss/location: 0.27779  ,  IoU: 0.84109[train: 51, 100 / 117] FPS: 71.2 (107.6)  ,  DataTime: 0.108 (0.082)  ,  ForwardTime: 0.709  ,  TotalTime: 0.898  ,  Loss/total: 0.71474  ,  Loss/giou: 0.17425  ,  Loss/l1: 0.01613  ,  Loss/location: 0.28562  ,  IoU: 0.83893



[train: 51, 100 / 117] FPS: 73.0 (108.8)  ,  DataTime: 0.115 (0.081)  ,  ForwardTime: 0.680  ,  TotalTime: 0.877  ,  Loss/total: 0.70349  ,  Loss/giou: 0.17228  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28120  ,  IoU: 0.84007
[train: 51, 100 / 117] FPS: 71.7 (107.7)  ,  DataTime: 0.107 (0.081)  ,  ForwardTime: 0.705  ,  TotalTime: 0.892  ,  Loss/total: 0.71340  ,  Loss/giou: 0.17329  ,  Loss/l1: 0.01617  ,  Loss/location: 0.28597  ,  IoU: 0.84018
[train: 51, 100 / 117] FPS: 72.6 (107.8)  ,  DataTime: 0.120 (0.080)  ,  ForwardTime: 0.681  ,  TotalTime: 0.882  ,  Loss/total: 0.71556  ,  Loss/giou: 0.17301  ,  Loss/l1: 0.01575  ,  Loss/location: 0.29078  ,  IoU: 0.83956
[train: 51, 100 / 117] FPS: 71.7 (107.4)  ,  DataTime: 0.109 (0.080)  ,  ForwardTime: 0.703  ,  TotalTime: 0.893  ,  Loss/total: 0.70045  ,  Loss/giou: 0.17092  ,  Loss/l1: 0.01533  ,  Loss/location: 0.28196  ,  IoU: 0.84127
[train: 51, 117 / 117] FPS: 75.4 (110.2)  ,  DataTime: 0.099 (0.075)  ,  ForwardTime: 0.674  ,  TotalTime: 0.849  ,  Loss/total: 0.69842  ,  Loss/giou: 0.17079  ,  Loss/l1: 0.01546  ,  Loss/location: 0.27954  ,  IoU: 0.84178[train: 51, 117 / 117] FPS: 76.7 (110.1)  ,  DataTime: 0.116 (0.074)  ,  ForwardTime: 0.645  ,  TotalTime: 0.835  ,  Loss/total: 0.70979  ,  Loss/giou: 0.17103  ,  Loss/l1: 0.01543  ,  Loss/location: 0.29056  ,  IoU: 0.84085

[train: 51, 117 / 117] FPS: 75.4 (110.1)  ,  DataTime: 0.097 (0.077)  ,  ForwardTime: 0.675  ,  TotalTime: 0.848  ,  Loss/total: 0.70771  ,  Loss/giou: 0.17242  ,  Loss/l1: 0.01594  ,  Loss/location: 0.28318  ,  IoU: 0.84056[train: 51, 117 / 117] FPS: 78.7 (110.1)  ,  DataTime: 0.119 (0.064)  ,  ForwardTime: 0.631  ,  TotalTime: 0.813  ,  Loss/total: 0.70261  ,  Loss/giou: 0.17218  ,  Loss/l1: 0.01571  ,  Loss/location: 0.27968  ,  IoU: 0.84067
[train: 51, 117 / 117] FPS: 76.5 (110.1)  ,  DataTime: 0.115 (0.078)  ,  ForwardTime: 0.644  ,  TotalTime: 0.836  ,  Loss/total: 0.69555  ,  Loss/giou: 0.16975  ,  Loss/l1: 0.01536  ,  Loss/location: 0.27926  ,  IoU: 0.84226

[train: 51, 117 / 117] FPS: 76.6 (110.2)  ,  DataTime: 0.104 (0.077)  ,  ForwardTime: 0.654  ,  TotalTime: 0.835  ,  Loss/total: 0.71016  ,  Loss/giou: 0.17447  ,  Loss/l1: 0.01589  ,  Loss/location: 0.28176  ,  IoU: 0.83846[train: 51, 117 / 117] FPS: 75.0 (110.1)  ,  DataTime: 0.098 (0.076)  ,  ForwardTime: 0.680  ,  TotalTime: 0.853  ,  Loss/total: 0.71407  ,  Loss/giou: 0.17390  ,  Loss/l1: 0.01601  ,  Loss/location: 0.28622  ,  IoU: 0.83913

[train: 51, 117 / 117] FPS: 76.3 (110.2)  ,  DataTime: 0.108 (0.076)  ,  ForwardTime: 0.655  ,  TotalTime: 0.839  ,  Loss/total: 0.71110  ,  Loss/giou: 0.17217  ,  Loss/l1: 0.01557  ,  Loss/location: 0.28893  ,  IoU: 0.84022
Epoch Time: 0:01:38.167864
Avg Data Time: 0.10834
Avg GPU Trans Time: 0.07569
Avg Forward Time: 0.65501
Epoch Time: 0:01:39.257698
Avg Data Time: 0.09674
Avg GPU Trans Time: 0.07657
Avg Forward Time: 0.67505
Epoch Time: 0:01:37.694507
Avg Data Time: 0.10419
Avg GPU Trans Time: 0.07682
Avg Forward Time: 0.65398
Epoch Time: 0:01:37.657117
Avg Data Time: 0.11650
Avg GPU Trans Time: 0.07354
Avg Forward Time: 0.64464
Epoch Time: 0:01:35.155455
Avg Data Time: 0.11856
Avg GPU Trans Time: 0.06356
Avg Forward Time: 0.63117
Epoch Time: 0:01:37.840593
Avg Data Time: 0.11461
Avg GPU Trans Time: 0.07791
Avg Forward Time: 0.64373
Epoch Time: 0:01:39.298995
Avg Data Time: 0.09919
Avg GPU Trans Time: 0.07525
Avg Forward Time: 0.67427
Epoch Time: 0:01:39.857123
Avg Data Time: 0.09767
Avg GPU Trans Time: 0.07625
Avg Forward Time: 0.67956
[train: 52, 50 / 117] FPS: 64.5 (83.6)  ,  DataTime: 0.180 (0.082)  ,  ForwardTime: 0.731  ,  TotalTime: 0.993  ,  Loss/total: 0.69148  ,  Loss/giou: 0.16738  ,  Loss/l1: 0.01497  ,  Loss/location: 0.28189  ,  IoU: 0.84394
[train: 52, 50 / 117] FPS: 64.5 (83.3)  ,  DataTime: 0.240 (0.077)  ,  ForwardTime: 0.676  ,  TotalTime: 0.993  ,  Loss/total: 0.69353  ,  Loss/giou: 0.16749  ,  Loss/l1: 0.01512  ,  Loss/location: 0.28292  ,  IoU: 0.84455
[train: 52, 50 / 117] FPS: 64.5 (83.8)  ,  DataTime: 0.193 (0.074)  ,  ForwardTime: 0.726  ,  TotalTime: 0.993  ,  Loss/total: 0.70252  ,  Loss/giou: 0.17160  ,  Loss/l1: 0.01575  ,  Loss/location: 0.28059  ,  IoU: 0.84108
[train: 52, 50 / 117] FPS: 64.4 (83.3)  ,  DataTime: 0.205 (0.083)  ,  ForwardTime: 0.706  ,  TotalTime: 0.993  ,  Loss/total: 0.71993  ,  Loss/giou: 0.17467  ,  Loss/l1: 0.01598  ,  Loss/location: 0.29069  ,  IoU: 0.83781
[train: 52, 50 / 117] FPS: 64.5 (83.5)  ,  DataTime: 0.202 (0.074)  ,  ForwardTime: 0.717  ,  TotalTime: 0.993  ,  Loss/total: 0.74042  ,  Loss/giou: 0.17855  ,  Loss/l1: 0.01725  ,  Loss/location: 0.29704  ,  IoU: 0.83729
[train: 52, 50 / 117] FPS: 64.5 (83.4)  ,  DataTime: 0.157 (0.079)  ,  ForwardTime: 0.756  ,  TotalTime: 0.993  ,  Loss/total: 0.70472  ,  Loss/giou: 0.17275  ,  Loss/l1: 0.01601  ,  Loss/location: 0.27917  ,  IoU: 0.84076
[train: 52, 50 / 117] FPS: 64.5 (83.3)  ,  DataTime: 0.159 (0.082)  ,  ForwardTime: 0.752  ,  TotalTime: 0.993  ,  Loss/total: 0.70643  ,  Loss/giou: 0.17335  ,  Loss/l1: 0.01604  ,  Loss/location: 0.27952  ,  IoU: 0.84000
[train: 52, 50 / 117] FPS: 64.4 (83.6)  ,  DataTime: 0.212 (0.083)  ,  ForwardTime: 0.699  ,  TotalTime: 0.993  ,  Loss/total: 0.69911  ,  Loss/giou: 0.16886  ,  Loss/l1: 0.01528  ,  Loss/location: 0.28496  ,  IoU: 0.84306
[train: 52, 100 / 117] FPS: 73.4 (106.8)  ,  DataTime: 0.115 (0.081)  ,  ForwardTime: 0.675  ,  TotalTime: 0.872  ,  Loss/total: 0.72075  ,  Loss/giou: 0.17428  ,  Loss/l1: 0.01629  ,  Loss/location: 0.29071  ,  IoU: 0.83883[train: 52, 100 / 117] FPS: 73.4 (106.8)  ,  DataTime: 0.130 (0.080)  ,  ForwardTime: 0.662  ,  TotalTime: 0.872  ,  Loss/total: 0.71635  ,  Loss/giou: 0.17461  ,  Loss/l1: 0.01604  ,  Loss/location: 0.28696  ,  IoU: 0.83778

[train: 52, 100 / 117] FPS: 73.4 (106.7)  ,  DataTime: 0.146 (0.072)  ,  ForwardTime: 0.654  ,  TotalTime: 0.872  ,  Loss/total: 0.69095  ,  Loss/giou: 0.16792  ,  Loss/l1: 0.01502  ,  Loss/location: 0.27998  ,  IoU: 0.84375
[train: 52, 100 / 117] FPS: 73.4 (106.7)  ,  DataTime: 0.125 (0.070)  ,  ForwardTime: 0.676  ,  TotalTime: 0.872  ,  Loss/total: 0.73010  ,  Loss/giou: 0.17694  ,  Loss/l1: 0.01659  ,  Loss/location: 0.29327  ,  IoU: 0.83761
[train: 52, 100 / 117] FPS: 73.4 (106.7)  ,  DataTime: 0.105 (0.080)  ,  ForwardTime: 0.687  ,  TotalTime: 0.872  ,  Loss/total: 0.70741  ,  Loss/giou: 0.17382  ,  Loss/l1: 0.01613  ,  Loss/location: 0.27911  ,  IoU: 0.83962
[train: 52, 100 / 117] FPS: 73.4 (106.7)  ,  DataTime: 0.121 (0.067)  ,  ForwardTime: 0.684  ,  TotalTime: 0.872  ,  Loss/total: 0.70150  ,  Loss/giou: 0.16909  ,  Loss/l1: 0.01518  ,  Loss/location: 0.28742  ,  IoU: 0.84273
[train: 52, 100 / 117] FPS: 73.4 (106.8)  ,  DataTime: 0.132 (0.081)  ,  ForwardTime: 0.658  ,  TotalTime: 0.872  ,  Loss/total: 0.71195  ,  Loss/giou: 0.17271  ,  Loss/l1: 0.01570  ,  Loss/location: 0.28806  ,  IoU: 0.84005
[train: 52, 100 / 117] FPS: 73.4 (107.3)  ,  DataTime: 0.105 (0.082)  ,  ForwardTime: 0.684  ,  TotalTime: 0.872  ,  Loss/total: 0.70562  ,  Loss/giou: 0.17322  ,  Loss/l1: 0.01575  ,  Loss/location: 0.28044  ,  IoU: 0.83935
[train: 52, 117 / 117] FPS: 77.1 (104.9)  ,  DataTime: 0.095 (0.077)  ,  ForwardTime: 0.658  ,  TotalTime: 0.831  ,  Loss/total: 0.70058  ,  Loss/giou: 0.17217  ,  Loss/l1: 0.01567  ,  Loss/location: 0.27788  ,  IoU: 0.84026[train: 52, 117 / 117] FPS: 77.0 (105.0)  ,  DataTime: 0.095 (0.075)  ,  ForwardTime: 0.661  ,  TotalTime: 0.831  ,  Loss/total: 0.71496  ,  Loss/giou: 0.17583  ,  Loss/l1: 0.01641  ,  Loss/location: 0.28123  ,  IoU: 0.83816

[train: 52, 117 / 117] FPS: 77.0 (104.8)  ,  DataTime: 0.104 (0.076)  ,  ForwardTime: 0.651  ,  TotalTime: 0.831  ,  Loss/total: 0.71449  ,  Loss/giou: 0.17274  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28925  ,  IoU: 0.83986
[train: 52, 117 / 117] FPS: 77.0 (104.9)  ,  DataTime: 0.130 (0.068)  ,  ForwardTime: 0.633  ,  TotalTime: 0.831  ,  Loss/total: 0.68877  ,  Loss/giou: 0.16797  ,  Loss/l1: 0.01507  ,  Loss/location: 0.27750  ,  IoU: 0.84382[train: 52, 117 / 117] FPS: 77.0 (104.9)  ,  DataTime: 0.116 (0.076)  ,  ForwardTime: 0.639  ,  TotalTime: 0.831  ,  Loss/total: 0.71478  ,  Loss/giou: 0.17403  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28699  ,  IoU: 0.83813

[train: 52, 117 / 117] FPS: 77.0 (104.9)  ,  DataTime: 0.118 (0.077)  ,  ForwardTime: 0.636  ,  TotalTime: 0.831  ,  Loss/total: 0.70836  ,  Loss/giou: 0.17227  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28575  ,  IoU: 0.84040
[train: 52, 117 / 117] FPS: 77.0 (104.8)  ,  DataTime: 0.113 (0.067)  ,  ForwardTime: 0.652  ,  TotalTime: 0.831  ,  Loss/total: 0.72869  ,  Loss/giou: 0.17634  ,  Loss/l1: 0.01651  ,  Loss/location: 0.29347  ,  IoU: 0.83786
[train: 52, 117 / 117] FPS: 77.0 (104.7)  ,  DataTime: 0.109 (0.063)  ,  ForwardTime: 0.659  ,  TotalTime: 0.831  ,  Loss/total: 0.69836  ,  Loss/giou: 0.16964  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28263  ,  IoU: 0.84243
Epoch Time: 0:01:37.197890
Avg Data Time: 0.12962
Avg GPU Trans Time: 0.06823
Avg Forward Time: 0.63290
Epoch Time: 0:01:37.191036
Avg Data Time: 0.10380
Avg GPU Trans Time: 0.07630
Avg Forward Time: 0.65060
Epoch Time: 0:01:37.182642
Avg Data Time: 0.09513
Avg GPU Trans Time: 0.07724
Avg Forward Time: 0.65825
Epoch Time: 0:01:37.197639
Avg Data Time: 0.11251
Avg GPU Trans Time: 0.06652
Avg Forward Time: 0.65172
Epoch Time: 0:01:37.220211
Avg Data Time: 0.11649
Avg GPU Trans Time: 0.07560
Avg Forward Time: 0.63885
Epoch Time: 0:01:37.209787
Avg Data Time: 0.11805
Avg GPU Trans Time: 0.07693
Avg Forward Time: 0.63588
Epoch Time: 0:01:37.200390
Avg Data Time: 0.10870
Avg GPU Trans Time: 0.06275
Avg Forward Time: 0.65933
Epoch Time: 0:01:37.190444
Avg Data Time: 0.09482
Avg GPU Trans Time: 0.07514
Avg Forward Time: 0.66073
[train: 53, 50 / 117] FPS: 64.4 (81.2)  ,  DataTime: 0.185 (0.070)  ,  ForwardTime: 0.739  ,  TotalTime: 0.994  ,  Loss/total: 0.69863  ,  Loss/giou: 0.17179  ,  Loss/l1: 0.01560  ,  Loss/location: 0.27702  ,  IoU: 0.84034
[train: 53, 50 / 117] FPS: 64.4 (81.3)  ,  DataTime: 0.194 (0.089)  ,  ForwardTime: 0.710  ,  TotalTime: 0.993  ,  Loss/total: 0.70481  ,  Loss/giou: 0.17268  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28125  ,  IoU: 0.83927[train: 53, 50 / 117] FPS: 64.4 (81.2)  ,  DataTime: 0.191 (0.062)  ,  ForwardTime: 0.740  ,  TotalTime: 0.993  ,  Loss/total: 0.68744  ,  Loss/giou: 0.16781  ,  Loss/l1: 0.01496  ,  Loss/location: 0.27700  ,  IoU: 0.84296

[train: 53, 50 / 117] FPS: 64.4 (81.1)  ,  DataTime: 0.211 (0.090)  ,  ForwardTime: 0.692  ,  TotalTime: 0.994  ,  Loss/total: 0.72546  ,  Loss/giou: 0.17470  ,  Loss/l1: 0.01581  ,  Loss/location: 0.29699  ,  IoU: 0.83880
[train: 53, 50 / 117] FPS: 64.4 (81.1)  ,  DataTime: 0.209 (0.084)  ,  ForwardTime: 0.701  ,  TotalTime: 0.994  ,  Loss/total: 0.70569  ,  Loss/giou: 0.17189  ,  Loss/l1: 0.01566  ,  Loss/location: 0.28359  ,  IoU: 0.84057
[train: 53, 50 / 117] FPS: 64.4 (81.1)  ,  DataTime: 0.208 (0.088)  ,  ForwardTime: 0.698  ,  TotalTime: 0.994  ,  Loss/total: 0.69096  ,  Loss/giou: 0.17074  ,  Loss/l1: 0.01571  ,  Loss/location: 0.27091  ,  IoU: 0.84183
[train: 53, 50 / 117] FPS: 64.4 (80.9)  ,  DataTime: 0.191 (0.094)  ,  ForwardTime: 0.709  ,  TotalTime: 0.994  ,  Loss/total: 0.72337  ,  Loss/giou: 0.17688  ,  Loss/l1: 0.01586  ,  Loss/location: 0.29030  ,  IoU: 0.83557
[train: 53, 50 / 117] FPS: 64.4 (81.1)  ,  DataTime: 0.193 (0.080)  ,  ForwardTime: 0.721  ,  TotalTime: 0.994  ,  Loss/total: 0.69154  ,  Loss/giou: 0.17232  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27038  ,  IoU: 0.84000
[train: 53, 100 / 117] FPS: 73.3 (105.8)  ,  DataTime: 0.122 (0.084)  ,  ForwardTime: 0.666  ,  TotalTime: 0.873  ,  Loss/total: 0.69906  ,  Loss/giou: 0.16984  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28360  ,  IoU: 0.84175
[train: 53, 100 / 117] FPS: 73.3 (106.1)  ,  DataTime: 0.120 (0.085)  ,  ForwardTime: 0.668  ,  TotalTime: 0.873  ,  Loss/total: 0.72086  ,  Loss/giou: 0.17544  ,  Loss/l1: 0.01594  ,  Loss/location: 0.29028  ,  IoU: 0.83755
[train: 53, 100 / 117] FPS: 73.3 (105.7)  ,  DataTime: 0.129 (0.081)  ,  ForwardTime: 0.663  ,  TotalTime: 0.873  ,  Loss/total: 0.70196  ,  Loss/giou: 0.17162  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28098  ,  IoU: 0.84070
[train: 53, 100 / 117] FPS: 73.3 (105.7)  ,  DataTime: 0.119 (0.060)  ,  ForwardTime: 0.694  ,  TotalTime: 0.873  ,  Loss/total: 0.69616  ,  Loss/giou: 0.16831  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28482  ,  IoU: 0.84263
[train: 53, 100 / 117] FPS: 73.3 (105.7)  ,  DataTime: 0.131 (0.085)  ,  ForwardTime: 0.657  ,  TotalTime: 0.873  ,  Loss/total: 0.69883  ,  Loss/giou: 0.16898  ,  Loss/l1: 0.01496  ,  Loss/location: 0.28609  ,  IoU: 0.84275
[train: 53, 100 / 117] FPS: 73.3 (105.8)  ,  DataTime: 0.130 (0.084)  ,  ForwardTime: 0.660  ,  TotalTime: 0.873  ,  Loss/total: 0.69812  ,  Loss/giou: 0.17151  ,  Loss/l1: 0.01572  ,  Loss/location: 0.27650  ,  IoU: 0.84096
[train: 53, 100 / 117] FPS: 73.3 (105.6)  ,  DataTime: 0.121 (0.079)  ,  ForwardTime: 0.673  ,  TotalTime: 0.873  ,  Loss/total: 0.69316  ,  Loss/giou: 0.17128  ,  Loss/l1: 0.01546  ,  Loss/location: 0.27330  ,  IoU: 0.84139
[train: 53, 100 / 117] FPS: 73.3 (105.8)  ,  DataTime: 0.118 (0.069)  ,  ForwardTime: 0.686  ,  TotalTime: 0.873  ,  Loss/total: 0.71041  ,  Loss/giou: 0.17292  ,  Loss/l1: 0.01607  ,  Loss/location: 0.28423  ,  IoU: 0.84014
[train: 53, 117 / 117] FPS: 77.0 (110.0)  ,  DataTime: 0.106 (0.065)  ,  ForwardTime: 0.659  ,  TotalTime: 0.831  ,  Loss/total: 0.71029  ,  Loss/giou: 0.17313  ,  Loss/l1: 0.01601  ,  Loss/location: 0.28397  ,  IoU: 0.83982
[train: 53, 117 / 117] FPS: 77.1 (109.8)  ,  DataTime: 0.107 (0.057)  ,  ForwardTime: 0.667  ,  TotalTime: 0.830  ,  Loss/total: 0.69147  ,  Loss/giou: 0.16696  ,  Loss/l1: 0.01472  ,  Loss/location: 0.28396  ,  IoU: 0.84363
[train: 53, 117 / 117] FPS: 77.1 (109.8)  ,  DataTime: 0.109 (0.079)  ,  ForwardTime: 0.643  ,  TotalTime: 0.830  ,  Loss/total: 0.70180  ,  Loss/giou: 0.17062  ,  Loss/l1: 0.01518  ,  Loss/location: 0.28465  ,  IoU: 0.84105
[train: 53, 117 / 117] FPS: 77.1 (109.8)  ,  DataTime: 0.117 (0.079)  ,  ForwardTime: 0.634  ,  TotalTime: 0.831  ,  Loss/total: 0.70241  ,  Loss/giou: 0.16984  ,  Loss/l1: 0.01516  ,  Loss/location: 0.28695  ,  IoU: 0.84202
[train: 53, 117 / 117] FPS: 77.0 (109.8)  ,  DataTime: 0.107 (0.080)  ,  ForwardTime: 0.643  ,  TotalTime: 0.831  ,  Loss/total: 0.71379  ,  Loss/giou: 0.17415  ,  Loss/l1: 0.01577  ,  Loss/location: 0.28662  ,  IoU: 0.83849[train: 53, 117 / 117] FPS: 77.1 (109.8)  ,  DataTime: 0.109 (0.074)  ,  ForwardTime: 0.648  ,  TotalTime: 0.831  ,  Loss/total: 0.68995  ,  Loss/giou: 0.17057  ,  Loss/l1: 0.01528  ,  Loss/location: 0.27242  ,  IoU: 0.84176

[train: 53, 117 / 117] FPS: 77.0 (109.8)  ,  DataTime: 0.116 (0.079)  ,  ForwardTime: 0.636  ,  TotalTime: 0.831  ,  Loss/total: 0.69221  ,  Loss/giou: 0.17022  ,  Loss/l1: 0.01556  ,  Loss/location: 0.27395  ,  IoU: 0.84210
[train: 53, 117 / 117] FPS: 77.0 (109.4)  ,  DataTime: 0.115 (0.076)  ,  ForwardTime: 0.640  ,  TotalTime: 0.831  ,  Loss/total: 0.70049  ,  Loss/giou: 0.17082  ,  Loss/l1: 0.01558  ,  Loss/location: 0.28096  ,  IoU: 0.84174
Epoch Time: 0:01:37.173155
Avg Data Time: 0.10882
Avg GPU Trans Time: 0.07360
Avg Forward Time: 0.64812
Epoch Time: 0:01:37.184326
Avg Data Time: 0.11593
Avg GPU Trans Time: 0.07863
Avg Forward Time: 0.63608
Epoch Time: 0:01:37.196521
Avg Data Time: 0.10603
Avg GPU Trans Time: 0.06528
Avg Forward Time: 0.65942
Epoch Time: 0:01:37.173598
Avg Data Time: 0.11713
Avg GPU Trans Time: 0.07942
Avg Forward Time: 0.63400
Epoch Time: 0:01:37.161427
Avg Data Time: 0.10915
Avg GPU Trans Time: 0.07876
Avg Forward Time: 0.64253
Epoch Time: 0:01:37.186973
Avg Data Time: 0.10734
Avg GPU Trans Time: 0.07999
Avg Forward Time: 0.64332
Epoch Time: 0:01:37.163572
Avg Data Time: 0.10657
Avg GPU Trans Time: 0.05726
Avg Forward Time: 0.66662
Epoch Time: 0:01:37.187352
Avg Data Time: 0.11490
Avg GPU Trans Time: 0.07577
Avg Forward Time: 0.63999
[train: 54, 50 / 117] FPS: 63.8 (83.8)  ,  DataTime: 0.238 (0.084)  ,  ForwardTime: 0.682  ,  TotalTime: 1.004  ,  Loss/total: 0.69350  ,  Loss/giou: 0.16794  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28150  ,  IoU: 0.84427
[train: 54, 50 / 117] FPS: 63.8 (83.7)  ,  DataTime: 0.186 (0.087)  ,  ForwardTime: 0.730  ,  TotalTime: 1.004  ,  Loss/total: 0.70683  ,  Loss/giou: 0.17118  ,  Loss/l1: 0.01479  ,  Loss/location: 0.29052  ,  IoU: 0.83939
[train: 54, 50 / 117] FPS: 63.7 (83.7)  ,  DataTime: 0.197 (0.078)  ,  ForwardTime: 0.730  ,  TotalTime: 1.004  ,  Loss/total: 0.72229  ,  Loss/giou: 0.17582  ,  Loss/l1: 0.01657  ,  Loss/location: 0.28778  ,  IoU: 0.83814
[train: 54, 50 / 117] FPS: 63.7 (83.5)  ,  DataTime: 0.181 (0.091)  ,  ForwardTime: 0.731  ,  TotalTime: 1.004  ,  Loss/total: 0.68842  ,  Loss/giou: 0.16534  ,  Loss/l1: 0.01436  ,  Loss/location: 0.28592  ,  IoU: 0.84497
[train: 54, 50 / 117] FPS: 63.7 (83.6)  ,  DataTime: 0.205 (0.081)  ,  ForwardTime: 0.719  ,  TotalTime: 1.004  ,  Loss/total: 0.68819  ,  Loss/giou: 0.16620  ,  Loss/l1: 0.01451  ,  Loss/location: 0.28325  ,  IoU: 0.84390[train: 54, 50 / 117] FPS: 63.8 (83.7)  ,  DataTime: 0.191 (0.087)  ,  ForwardTime: 0.726  ,  TotalTime: 1.004  ,  Loss/total: 0.65437  ,  Loss/giou: 0.16019  ,  Loss/l1: 0.01416  ,  Loss/location: 0.26321  ,  IoU: 0.84993

[train: 54, 50 / 117] FPS: 63.8 (83.4)  ,  DataTime: 0.216 (0.086)  ,  ForwardTime: 0.702  ,  TotalTime: 1.004  ,  Loss/total: 0.71172  ,  Loss/giou: 0.17495  ,  Loss/l1: 0.01627  ,  Loss/location: 0.28049  ,  IoU: 0.83908
[train: 54, 50 / 117] FPS: 63.8 (83.5)  ,  DataTime: 0.213 (0.079)  ,  ForwardTime: 0.711  ,  TotalTime: 1.004  ,  Loss/total: 0.69639  ,  Loss/giou: 0.17068  ,  Loss/l1: 0.01606  ,  Loss/location: 0.27473  ,  IoU: 0.84225
[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.121 (0.086)  ,  ForwardTime: 0.672  ,  TotalTime: 0.879  ,  Loss/total: 0.68303  ,  Loss/giou: 0.16579  ,  Loss/l1: 0.01413  ,  Loss/location: 0.28081  ,  IoU: 0.84410
[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.117 (0.087)  ,  ForwardTime: 0.675  ,  TotalTime: 0.879  ,  Loss/total: 0.69091  ,  Loss/giou: 0.16808  ,  Loss/l1: 0.01482  ,  Loss/location: 0.28063  ,  IoU: 0.84293
[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.122 (0.083)  ,  ForwardTime: 0.673  ,  TotalTime: 0.879  ,  Loss/total: 0.67459  ,  Loss/giou: 0.16382  ,  Loss/l1: 0.01444  ,  Loss/location: 0.27473  ,  IoU: 0.84652
[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.133 (0.079)  ,  ForwardTime: 0.666  ,  TotalTime: 0.879  ,  Loss/total: 0.69765  ,  Loss/giou: 0.17042  ,  Loss/l1: 0.01552  ,  Loss/location: 0.27919  ,  IoU: 0.84206
[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.135 (0.080)  ,  ForwardTime: 0.664  ,  TotalTime: 0.879  ,  Loss/total: 0.71765  ,  Loss/giou: 0.17501  ,  Loss/l1: 0.01653  ,  Loss/location: 0.28497  ,  IoU: 0.83874[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.128 (0.077)  ,  ForwardTime: 0.674  ,  TotalTime: 0.879  ,  Loss/total: 0.70346  ,  Loss/giou: 0.17010  ,  Loss/l1: 0.01533  ,  Loss/location: 0.28661  ,  IoU: 0.84168[train: 54, 100 / 117] FPS: 72.8 (107.8)  ,  DataTime: 0.148 (0.082)  ,  ForwardTime: 0.649  ,  TotalTime: 0.879  ,  Loss/total: 0.69861  ,  Loss/giou: 0.16925  ,  Loss/l1: 0.01530  ,  Loss/location: 0.28360  ,  IoU: 0.84281


[train: 54, 100 / 117] FPS: 72.8 (107.7)  ,  DataTime: 0.124 (0.072)  ,  ForwardTime: 0.683  ,  TotalTime: 0.879  ,  Loss/total: 0.70531  ,  Loss/giou: 0.17264  ,  Loss/l1: 0.01582  ,  Loss/location: 0.28091  ,  IoU: 0.84031
[train: 54, 117 / 117] FPS: 76.6 (108.1)  ,  DataTime: 0.120 (0.075)  ,  ForwardTime: 0.641  ,  TotalTime: 0.836  ,  Loss/total: 0.71507  ,  Loss/giou: 0.17469  ,  Loss/l1: 0.01626  ,  Loss/location: 0.28439  ,  IoU: 0.83853[train: 54, 117 / 117] FPS: 76.5 (108.1)  ,  DataTime: 0.109 (0.078)  ,  ForwardTime: 0.649  ,  TotalTime: 0.836  ,  Loss/total: 0.67927  ,  Loss/giou: 0.16557  ,  Loss/l1: 0.01471  ,  Loss/location: 0.27458  ,  IoU: 0.84503

[train: 54, 117 / 117] FPS: 76.5 (108.1)  ,  DataTime: 0.105 (0.082)  ,  ForwardTime: 0.649  ,  TotalTime: 0.836  ,  Loss/total: 0.69438  ,  Loss/giou: 0.16859  ,  Loss/l1: 0.01491  ,  Loss/location: 0.28263  ,  IoU: 0.84253
[train: 54, 117 / 117] FPS: 76.6 (108.1)  ,  DataTime: 0.109 (0.080)  ,  ForwardTime: 0.647  ,  TotalTime: 0.836  ,  Loss/total: 0.68228  ,  Loss/giou: 0.16620  ,  Loss/l1: 0.01434  ,  Loss/location: 0.27817  ,  IoU: 0.84416
[train: 54, 117 / 117] FPS: 76.5 (108.1)  ,  DataTime: 0.131 (0.077)  ,  ForwardTime: 0.628  ,  TotalTime: 0.836  ,  Loss/total: 0.69497  ,  Loss/giou: 0.16914  ,  Loss/l1: 0.01527  ,  Loss/location: 0.28037  ,  IoU: 0.84270[train: 54, 117 / 117] FPS: 76.6 (108.1)  ,  DataTime: 0.119 (0.074)  ,  ForwardTime: 0.642  ,  TotalTime: 0.836  ,  Loss/total: 0.69366  ,  Loss/giou: 0.16924  ,  Loss/l1: 0.01531  ,  Loss/location: 0.27864  ,  IoU: 0.84286

[train: 54, 117 / 117] FPS: 76.5 (108.2)  ,  DataTime: 0.111 (0.067)  ,  ForwardTime: 0.658  ,  TotalTime: 0.836  ,  Loss/total: 0.70624  ,  Loss/giou: 0.17316  ,  Loss/l1: 0.01595  ,  Loss/location: 0.28015  ,  IoU: 0.84008
[train: 54, 117 / 117] FPS: 76.5 (108.1)  ,  DataTime: 0.115 (0.073)  ,  ForwardTime: 0.649  ,  TotalTime: 0.836  ,  Loss/total: 0.69836  ,  Loss/giou: 0.16950  ,  Loss/l1: 0.01531  ,  Loss/location: 0.28280  ,  IoU: 0.84229
Epoch Time: 0:01:37.818177
Avg Data Time: 0.10861
Avg GPU Trans Time: 0.08024
Avg Forward Time: 0.64721
Epoch Time: 0:01:37.821141
Avg Data Time: 0.10946
Avg GPU Trans Time: 0.07797
Avg Forward Time: 0.64865
Epoch Time: 0:01:37.823484
Avg Data Time: 0.13119
Avg GPU Trans Time: 0.07722
Avg Forward Time: 0.62769
Epoch Time: 0:01:37.845343
Avg Data Time: 0.11453
Avg GPU Trans Time: 0.07266
Avg Forward Time: 0.64910
Epoch Time: 0:01:37.817106
Avg Data Time: 0.11918
Avg GPU Trans Time: 0.07438
Avg Forward Time: 0.64249
Epoch Time: 0:01:37.825639
Avg Data Time: 0.10474
Avg GPU Trans Time: 0.08203
Avg Forward Time: 0.64935
Epoch Time: 0:01:37.842802
Avg Data Time: 0.11123
Avg GPU Trans Time: 0.06743
Avg Forward Time: 0.65761
Epoch Time: 0:01:37.814495
Avg Data Time: 0.12036
Avg GPU Trans Time: 0.07502
Avg Forward Time: 0.64064
[train: 55, 50 / 117] FPS: 64.6 (87.1)  ,  DataTime: 0.197 (0.080)  ,  ForwardTime: 0.713  ,  TotalTime: 0.991  ,  Loss/total: 0.70764  ,  Loss/giou: 0.17213  ,  Loss/l1: 0.01588  ,  Loss/location: 0.28398  ,  IoU: 0.84087
[train: 55, 50 / 117] FPS: 64.6 (86.8)  ,  DataTime: 0.205 (0.080)  ,  ForwardTime: 0.705  ,  TotalTime: 0.991  ,  Loss/total: 0.72001  ,  Loss/giou: 0.17801  ,  Loss/l1: 0.01619  ,  Loss/location: 0.28305  ,  IoU: 0.83533
[train: 55, 50 / 117] FPS: 64.6 (86.8)  ,  DataTime: 0.209 (0.083)  ,  ForwardTime: 0.699  ,  TotalTime: 0.991  ,  Loss/total: 0.68106  ,  Loss/giou: 0.16489  ,  Loss/l1: 0.01407  ,  Loss/location: 0.28094  ,  IoU: 0.84422
[train: 55, 50 / 117] FPS: 64.6 (86.7)  ,  DataTime: 0.188 (0.081)  ,  ForwardTime: 0.721  ,  TotalTime: 0.990  ,  Loss/total: 0.71515  ,  Loss/giou: 0.17050  ,  Loss/l1: 0.01533  ,  Loss/location: 0.29751  ,  IoU: 0.84111
[train: 55, 50 / 117] FPS: 64.6 (86.4)  ,  DataTime: 0.167 (0.070)  ,  ForwardTime: 0.754  ,  TotalTime: 0.990  ,  Loss/total: 0.73376  ,  Loss/giou: 0.17704  ,  Loss/l1: 0.01690  ,  Loss/location: 0.29517  ,  IoU: 0.83748[train: 55, 50 / 117] FPS: 64.6 (86.3)  ,  DataTime: 0.224 (0.082)  ,  ForwardTime: 0.684  ,  TotalTime: 0.990  ,  Loss/total: 0.69830  ,  Loss/giou: 0.16965  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28221  ,  IoU: 0.84257

[train: 55, 50 / 117] FPS: 64.6 (86.5)  ,  DataTime: 0.178 (0.083)  ,  ForwardTime: 0.730  ,  TotalTime: 0.991  ,  Loss/total: 0.70163  ,  Loss/giou: 0.17160  ,  Loss/l1: 0.01569  ,  Loss/location: 0.27996  ,  IoU: 0.84044
[train: 55, 50 / 117] FPS: 64.6 (86.5)  ,  DataTime: 0.198 (0.082)  ,  ForwardTime: 0.711  ,  TotalTime: 0.991  ,  Loss/total: 0.74440  ,  Loss/giou: 0.17939  ,  Loss/l1: 0.01651  ,  Loss/location: 0.30309  ,  IoU: 0.83421
[train: 55, 100 / 117] FPS: 73.7 (109.3)  ,  DataTime: 0.107 (0.066)  ,  ForwardTime: 0.696  ,  TotalTime: 0.869  ,  Loss/total: 0.72134  ,  Loss/giou: 0.17504  ,  Loss/l1: 0.01625  ,  Loss/location: 0.29001  ,  IoU: 0.83843
[train: 55, 100 / 117] FPS: 73.7 (109.6)  ,  DataTime: 0.120 (0.079)  ,  ForwardTime: 0.669  ,  TotalTime: 0.869  ,  Loss/total: 0.70872  ,  Loss/giou: 0.17064  ,  Loss/l1: 0.01562  ,  Loss/location: 0.28932  ,  IoU: 0.84190[train: 55, 100 / 117] FPS: 73.7 (109.3)  ,  DataTime: 0.128 (0.079)  ,  ForwardTime: 0.662  ,  TotalTime: 0.869  ,  Loss/total: 0.71601  ,  Loss/giou: 0.17545  ,  Loss/l1: 0.01593  ,  Loss/location: 0.28546  ,  IoU: 0.83770
[train: 55, 100 / 117] FPS: 73.7 (109.3)  ,  DataTime: 0.138 (0.078)  ,  ForwardTime: 0.653  ,  TotalTime: 0.869  ,  Loss/total: 0.69727  ,  Loss/giou: 0.16979  ,  Loss/l1: 0.01520  ,  Loss/location: 0.28171  ,  IoU: 0.84197

[train: 55, 100 / 117] FPS: 73.7 (109.3)  ,  DataTime: 0.124 (0.074)  ,  ForwardTime: 0.671  ,  TotalTime: 0.869  ,  Loss/total: 0.72493  ,  Loss/giou: 0.17690  ,  Loss/l1: 0.01655  ,  Loss/location: 0.28840  ,  IoU: 0.83678
[train: 55, 100 / 117] FPS: 73.7 (109.6)  ,  DataTime: 0.125 (0.077)  ,  ForwardTime: 0.667  ,  TotalTime: 0.869  ,  Loss/total: 0.72161  ,  Loss/giou: 0.17377  ,  Loss/l1: 0.01576  ,  Loss/location: 0.29527  ,  IoU: 0.83870
[train: 55, 100 / 117] FPS: 73.7 (109.2)  ,  DataTime: 0.116 (0.081)  ,  ForwardTime: 0.672  ,  TotalTime: 0.869  ,  Loss/total: 0.69556  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01549  ,  Loss/location: 0.27590  ,  IoU: 0.84113
[train: 55, 100 / 117] FPS: 73.6 (109.3)  ,  DataTime: 0.131 (0.080)  ,  ForwardTime: 0.658  ,  TotalTime: 0.869  ,  Loss/total: 0.70356  ,  Loss/giou: 0.17101  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28508  ,  IoU: 0.84036
[train: 55, 117 / 117] FPS: 77.3 (106.7)  ,  DataTime: 0.114 (0.075)  ,  ForwardTime: 0.639  ,  TotalTime: 0.827  ,  Loss/total: 0.71466  ,  Loss/giou: 0.17481  ,  Loss/l1: 0.01578  ,  Loss/location: 0.28614  ,  IoU: 0.83790
[train: 55, 117 / 117] FPS: 77.4 (106.7)  ,  DataTime: 0.108 (0.075)  ,  ForwardTime: 0.645  ,  TotalTime: 0.827  ,  Loss/total: 0.71119  ,  Loss/giou: 0.17145  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28987  ,  IoU: 0.84108[train: 55, 117 / 117] FPS: 77.3 (106.7)  ,  DataTime: 0.104 (0.077)  ,  ForwardTime: 0.647  ,  TotalTime: 0.827  ,  Loss/total: 0.69567  ,  Loss/giou: 0.17094  ,  Loss/l1: 0.01548  ,  Loss/location: 0.27641  ,  IoU: 0.84132[train: 55, 117 / 117] FPS: 77.4 (106.7)  ,  DataTime: 0.123 (0.073)  ,  ForwardTime: 0.631  ,  TotalTime: 0.827  ,  Loss/total: 0.69870  ,  Loss/giou: 0.17042  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28141  ,  IoU: 0.84156


[train: 55, 117 / 117] FPS: 77.3 (106.7)  ,  DataTime: 0.111 (0.070)  ,  ForwardTime: 0.647  ,  TotalTime: 0.827  ,  Loss/total: 0.72682  ,  Loss/giou: 0.17721  ,  Loss/l1: 0.01659  ,  Loss/location: 0.28945  ,  IoU: 0.83640
[train: 55, 117 / 117] FPS: 77.4 (106.8)  ,  DataTime: 0.112 (0.072)  ,  ForwardTime: 0.643  ,  TotalTime: 0.827  ,  Loss/total: 0.72226  ,  Loss/giou: 0.17437  ,  Loss/l1: 0.01583  ,  Loss/location: 0.29440  ,  IoU: 0.83821
[train: 55, 117 / 117] FPS: 77.4 (106.6)  ,  DataTime: 0.097 (0.063)  ,  ForwardTime: 0.668  ,  TotalTime: 0.827  ,  Loss/total: 0.72202  ,  Loss/giou: 0.17518  ,  Loss/l1: 0.01631  ,  Loss/location: 0.29009  ,  IoU: 0.83834
[train: 55, 117 / 117] FPS: 77.3 (106.6)  ,  DataTime: 0.117 (0.076)  ,  ForwardTime: 0.635  ,  TotalTime: 0.828  ,  Loss/total: 0.69997  ,  Loss/giou: 0.17012  ,  Loss/l1: 0.01517  ,  Loss/location: 0.28389  ,  IoU: 0.84114
Epoch Time: 0:01:36.811309
Avg Data Time: 0.10374
Avg GPU Trans Time: 0.07650
Avg Forward Time: 0.64720
Epoch Time: 0:01:36.812178
Avg Data Time: 0.11386
Avg GPU Trans Time: 0.07476
Avg Forward Time: 0.63884
Epoch Time: 0:01:36.833473
Avg Data Time: 0.11707
Avg GPU Trans Time: 0.07573
Avg Forward Time: 0.63484
Epoch Time: 0:01:36.800858
Avg Data Time: 0.11173
Avg GPU Trans Time: 0.07234
Avg Forward Time: 0.64329
Epoch Time: 0:01:36.784411
Avg Data Time: 0.10771
Avg GPU Trans Time: 0.07456
Avg Forward Time: 0.64495
Epoch Time: 0:01:36.799260
Avg Data Time: 0.09678
Avg GPU Trans Time: 0.06296
Avg Forward Time: 0.66761
Epoch Time: 0:01:36.807583
Avg Data Time: 0.11117
Avg GPU Trans Time: 0.06950
Avg Forward Time: 0.64675
Epoch Time: 0:01:36.794982
Avg Data Time: 0.12325
Avg GPU Trans Time: 0.07347
Avg Forward Time: 0.63059
[val: 55, 50 / 78] FPS: 37.9 (191.7)  ,  DataTime: 1.275 (0.042)  ,  ForwardTime: 0.373  ,  TotalTime: 1.690  ,  Loss/total: 0.84783  ,  Loss/giou: 0.20487  ,  Loss/l1: 0.02192  ,  Loss/location: 0.32850  ,  IoU: 0.81776
[val: 55, 50 / 78] FPS: 37.5 (195.5)  ,  DataTime: 1.429 (0.044)  ,  ForwardTime: 0.234  ,  TotalTime: 1.707  ,  Loss/total: 0.82363  ,  Loss/giou: 0.20060  ,  Loss/l1: 0.02162  ,  Loss/location: 0.31434  ,  IoU: 0.82262
[val: 55, 50 / 78] FPS: 36.7 (206.7)  ,  DataTime: 1.404 (0.042)  ,  ForwardTime: 0.296  ,  TotalTime: 1.742  ,  Loss/total: 0.83110  ,  Loss/giou: 0.20066  ,  Loss/l1: 0.02124  ,  Loss/location: 0.32357  ,  IoU: 0.82069
[val: 55, 50 / 78] FPS: 35.6 (199.1)  ,  DataTime: 1.426 (0.041)  ,  ForwardTime: 0.330  ,  TotalTime: 1.797  ,  Loss/total: 0.83532  ,  Loss/giou: 0.20244  ,  Loss/l1: 0.02129  ,  Loss/location: 0.32397  ,  IoU: 0.81815
[val: 55, 50 / 78] FPS: 34.1 (179.1)  ,  DataTime: 1.558 (0.043)  ,  ForwardTime: 0.273  ,  TotalTime: 1.875  ,  Loss/total: 0.81361  ,  Loss/giou: 0.19857  ,  Loss/l1: 0.02107  ,  Loss/location: 0.31110  ,  IoU: 0.82229
[val: 55, 50 / 78] FPS: 34.1 (198.2)  ,  DataTime: 1.600 (0.047)  ,  ForwardTime: 0.227  ,  TotalTime: 1.874  ,  Loss/total: 0.83328  ,  Loss/giou: 0.20050  ,  Loss/l1: 0.02087  ,  Loss/location: 0.32790  ,  IoU: 0.82035
[val: 55, 50 / 78] FPS: 34.0 (206.0)  ,  DataTime: 1.522 (0.048)  ,  ForwardTime: 0.311  ,  TotalTime: 1.881  ,  Loss/total: 0.83254  ,  Loss/giou: 0.20090  ,  Loss/l1: 0.02155  ,  Loss/location: 0.32302  ,  IoU: 0.82090
[val: 55, 50 / 78] FPS: 31.5 (191.5)  ,  DataTime: 1.757 (0.044)  ,  ForwardTime: 0.232  ,  TotalTime: 2.034  ,  Loss/total: 0.82289  ,  Loss/giou: 0.19733  ,  Loss/l1: 0.02093  ,  Loss/location: 0.32356  ,  IoU: 0.82301
[val: 55, 78 / 78] FPS: 43.6 (211.0)  ,  DataTime: 1.186 (0.047)  ,  ForwardTime: 0.235  ,  TotalTime: 1.468  ,  Loss/total: 0.84080  ,  Loss/giou: 0.20242  ,  Loss/l1: 0.02154  ,  Loss/location: 0.32826  ,  IoU: 0.82026
Epoch Time: 0:01:54.538558
Avg Data Time: 1.18639
Avg GPU Trans Time: 0.04718
Avg Forward Time: 0.23487
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 55, 78 / 78] FPS: 42.9 (215.0)  ,  DataTime: 1.155 (0.042)  ,  ForwardTime: 0.295  ,  TotalTime: 1.492  ,  Loss/total: 0.82930  ,  Loss/giou: 0.20074  ,  Loss/l1: 0.02092  ,  Loss/location: 0.32323  ,  IoU: 0.81958
Epoch Time: 0:01:56.403191
Avg Data Time: 1.15512
Avg GPU Trans Time: 0.04249
Avg Forward Time: 0.29474
[val: 55, 78 / 78] FPS: 42.8 (213.9)  ,  DataTime: 1.181 (0.044)  ,  ForwardTime: 0.272  ,  TotalTime: 1.497  ,  Loss/total: 0.84281  ,  Loss/giou: 0.20191  ,  Loss/l1: 0.02153  ,  Loss/location: 0.33136  ,  IoU: 0.81972
Epoch Time: 0:01:56.730560
Avg Data Time: 1.18140
Avg GPU Trans Time: 0.04351
Avg Forward Time: 0.27164
[val: 55, 78 / 78] FPS: 42.6 (189.0)  ,  DataTime: 1.171 (0.048)  ,  ForwardTime: 0.283  ,  TotalTime: 1.502  ,  Loss/total: 0.81775  ,  Loss/giou: 0.19765  ,  Loss/l1: 0.02087  ,  Loss/location: 0.31809  ,  IoU: 0.82301
[val: 55, 78 / 78] FPS: 42.6 (204.4)  ,  DataTime: 1.138 (0.044)  ,  ForwardTime: 0.321  ,  TotalTime: 1.503  ,  Loss/total: 0.84040  ,  Loss/giou: 0.20276  ,  Loss/l1: 0.02159  ,  Loss/location: 0.32691  ,  IoU: 0.81944
Epoch Time: 0:01:57.133948
Avg Data Time: 1.17113
Avg GPU Trans Time: 0.04784
Avg Forward Time: 0.28274
Epoch Time: 0:01:57.213144
Avg Data Time: 1.13791
Avg GPU Trans Time: 0.04355
Avg Forward Time: 0.32127
[val: 55, 78 / 78] FPS: 41.5 (167.2)  ,  DataTime: 1.261 (0.049)  ,  ForwardTime: 0.230  ,  TotalTime: 1.541  ,  Loss/total: 0.83390  ,  Loss/giou: 0.20107  ,  Loss/l1: 0.02111  ,  Loss/location: 0.32620  ,  IoU: 0.82022
Epoch Time: 0:02:00.176873
Avg Data Time: 1.26132
Avg GPU Trans Time: 0.04930
Avg Forward Time: 0.23010
[val: 55, 78 / 78] FPS: 41.0 (187.5)  ,  DataTime: 1.259 (0.044)  ,  ForwardTime: 0.258  ,  TotalTime: 1.561  ,  Loss/total: 0.82685  ,  Loss/giou: 0.20078  ,  Loss/l1: 0.02114  ,  Loss/location: 0.31958  ,  IoU: 0.81965
Epoch Time: 0:02:01.728980
Avg Data Time: 1.25877
Avg GPU Trans Time: 0.04434
Avg Forward Time: 0.25752
[val: 55, 78 / 78] FPS: 40.8 (179.8)  ,  DataTime: 1.288 (0.046)  ,  ForwardTime: 0.235  ,  TotalTime: 1.569  ,  Loss/total: 0.82657  ,  Loss/giou: 0.19940  ,  Loss/l1: 0.02105  ,  Loss/location: 0.32255  ,  IoU: 0.82126
Epoch Time: 0:02:02.363916
Avg Data Time: 1.28761
Avg GPU Trans Time: 0.04613
Avg Forward Time: 0.23503
[train: 56, 50 / 117] FPS: 63.9 (83.8)  ,  DataTime: 0.154 (0.086)  ,  ForwardTime: 0.761  ,  TotalTime: 1.001  ,  Loss/total: 0.72020  ,  Loss/giou: 0.17420  ,  Loss/l1: 0.01581  ,  Loss/location: 0.29276  ,  IoU: 0.83796[train: 56, 50 / 117] FPS: 64.6 (83.6)  ,  DataTime: 0.164 (0.081)  ,  ForwardTime: 0.745  ,  TotalTime: 0.990  ,  Loss/total: 0.69678  ,  Loss/giou: 0.16907  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28221  ,  IoU: 0.84336

[train: 56, 50 / 117] FPS: 64.5 (83.6)  ,  DataTime: 0.153 (0.084)  ,  ForwardTime: 0.755  ,  TotalTime: 0.992  ,  Loss/total: 0.71567  ,  Loss/giou: 0.17052  ,  Loss/l1: 0.01544  ,  Loss/location: 0.29745  ,  IoU: 0.84163
[train: 56, 50 / 117] FPS: 72.3 (83.5)  ,  DataTime: 0.159 (0.071)  ,  ForwardTime: 0.655  ,  TotalTime: 0.885  ,  Loss/total: 0.69410  ,  Loss/giou: 0.17041  ,  Loss/l1: 0.01562  ,  Loss/location: 0.27515  ,  IoU: 0.84168
[train: 56, 50 / 117] FPS: 71.3 (83.4)  ,  DataTime: 0.179 (0.081)  ,  ForwardTime: 0.638  ,  TotalTime: 0.898  ,  Loss/total: 0.70698  ,  Loss/giou: 0.17294  ,  Loss/l1: 0.01587  ,  Loss/location: 0.28175  ,  IoU: 0.83959
[train: 56, 50 / 117] FPS: 62.2 (83.4)  ,  DataTime: 0.133 (0.083)  ,  ForwardTime: 0.813  ,  TotalTime: 1.028  ,  Loss/total: 0.70547  ,  Loss/giou: 0.17100  ,  Loss/l1: 0.01524  ,  Loss/location: 0.28729  ,  IoU: 0.84079
[train: 56, 50 / 117] FPS: 63.5 (83.5)  ,  DataTime: 0.161 (0.080)  ,  ForwardTime: 0.767  ,  TotalTime: 1.008  ,  Loss/total: 0.69861  ,  Loss/giou: 0.16846  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28645  ,  IoU: 0.84339
[train: 56, 50 / 117] FPS: 69.0 (83.5)  ,  DataTime: 0.195 (0.079)  ,  ForwardTime: 0.654  ,  TotalTime: 0.927  ,  Loss/total: 0.70140  ,  Loss/giou: 0.17027  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28460  ,  IoU: 0.84109
[train: 56, 100 / 117] FPS: 72.9 (108.2)  ,  DataTime: 0.106 (0.084)  ,  ForwardTime: 0.688  ,  TotalTime: 0.878  ,  Loss/total: 0.71046  ,  Loss/giou: 0.17164  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28953  ,  IoU: 0.84017[train: 56, 100 / 117] FPS: 76.1 (108.2)  ,  DataTime: 0.124 (0.078)  ,  ForwardTime: 0.639  ,  TotalTime: 0.841  ,  Loss/total: 0.70441  ,  Loss/giou: 0.17050  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28602  ,  IoU: 0.84148[train: 56, 100 / 117] FPS: 73.3 (108.6)  ,  DataTime: 0.110 (0.080)  ,  ForwardTime: 0.682  ,  TotalTime: 0.873  ,  Loss/total: 0.70156  ,  Loss/giou: 0.17099  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28137  ,  IoU: 0.84182
[train: 56, 100 / 117] FPS: 77.4 (108.2)  ,  DataTime: 0.118 (0.080)  ,  ForwardTime: 0.628  ,  TotalTime: 0.827  ,  Loss/total: 0.69705  ,  Loss/giou: 0.17077  ,  Loss/l1: 0.01546  ,  Loss/location: 0.27820  ,  IoU: 0.84129


[train: 56, 100 / 117] FPS: 73.2 (108.2)  ,  DataTime: 0.105 (0.082)  ,  ForwardTime: 0.686  ,  TotalTime: 0.874  ,  Loss/total: 0.70896  ,  Loss/giou: 0.17082  ,  Loss/l1: 0.01545  ,  Loss/location: 0.29005  ,  IoU: 0.84155[train: 56, 100 / 117] FPS: 78.0 (108.1)  ,  DataTime: 0.107 (0.067)  ,  ForwardTime: 0.646  ,  TotalTime: 0.820  ,  Loss/total: 0.70241  ,  Loss/giou: 0.17281  ,  Loss/l1: 0.01594  ,  Loss/location: 0.27707  ,  IoU: 0.84005

[train: 56, 100 / 117] FPS: 71.8 (108.1)  ,  DataTime: 0.094 (0.079)  ,  ForwardTime: 0.718  ,  TotalTime: 0.892  ,  Loss/total: 0.71738  ,  Loss/giou: 0.17389  ,  Loss/l1: 0.01566  ,  Loss/location: 0.29128  ,  IoU: 0.83837
[train: 56, 100 / 117] FPS: 72.6 (108.1)  ,  DataTime: 0.106 (0.078)  ,  ForwardTime: 0.697  ,  TotalTime: 0.882  ,  Loss/total: 0.70872  ,  Loss/giou: 0.17177  ,  Loss/l1: 0.01564  ,  Loss/location: 0.28700  ,  IoU: 0.84092
[train: 56, 117 / 117] FPS: 76.9 (107.2)  ,  DataTime: 0.096 (0.077)  ,  ForwardTime: 0.660  ,  TotalTime: 0.833  ,  Loss/total: 0.71003  ,  Loss/giou: 0.17099  ,  Loss/l1: 0.01548  ,  Loss/location: 0.29065  ,  IoU: 0.84132[train: 56, 117 / 117] FPS: 76.3 (107.1)  ,  DataTime: 0.096 (0.074)  ,  ForwardTime: 0.669  ,  TotalTime: 0.839  ,  Loss/total: 0.70881  ,  Loss/giou: 0.17137  ,  Loss/l1: 0.01556  ,  Loss/location: 0.28827  ,  IoU: 0.84091

[train: 56, 117 / 117] FPS: 79.5 (107.2)  ,  DataTime: 0.111 (0.073)  ,  ForwardTime: 0.620  ,  TotalTime: 0.805  ,  Loss/total: 0.70108  ,  Loss/giou: 0.17000  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28400  ,  IoU: 0.84196
[train: 56, 117 / 117] FPS: 75.5 (107.2)  ,  DataTime: 0.086 (0.075)  ,  ForwardTime: 0.687  ,  TotalTime: 0.848  ,  Loss/total: 0.71349  ,  Loss/giou: 0.17310  ,  Loss/l1: 0.01561  ,  Loss/location: 0.28923  ,  IoU: 0.83913
[train: 56, 117 / 117] FPS: 77.0 (107.1)  ,  DataTime: 0.099 (0.075)  ,  ForwardTime: 0.657  ,  TotalTime: 0.832  ,  Loss/total: 0.70987  ,  Loss/giou: 0.17295  ,  Loss/l1: 0.01596  ,  Loss/location: 0.28417  ,  IoU: 0.84036
[train: 56, 117 / 117] FPS: 76.5 (107.3)  ,  DataTime: 0.096 (0.079)  ,  ForwardTime: 0.662  ,  TotalTime: 0.836  ,  Loss/total: 0.70817  ,  Loss/giou: 0.17151  ,  Loss/l1: 0.01554  ,  Loss/location: 0.28745  ,  IoU: 0.84033
[train: 56, 117 / 117] FPS: 80.8 (107.5)  ,  DataTime: 0.106 (0.076)  ,  ForwardTime: 0.611  ,  TotalTime: 0.792  ,  Loss/total: 0.69872  ,  Loss/giou: 0.17088  ,  Loss/l1: 0.01543  ,  Loss/location: 0.27983  ,  IoU: 0.84106
[train: 56, 117 / 117] FPS: 81.4 (107.1)  ,  DataTime: 0.097 (0.064)  ,  ForwardTime: 0.626  ,  TotalTime: 0.787  ,  Loss/total: 0.70817  ,  Loss/giou: 0.17376  ,  Loss/l1: 0.01591  ,  Loss/location: 0.28111  ,  IoU: 0.83905
Epoch Time: 0:01:38.186154
Avg Data Time: 0.09607
Avg GPU Trans Time: 0.07404
Avg Forward Time: 0.66908
Epoch Time: 0:01:37.410737
Avg Data Time: 0.09568
Avg GPU Trans Time: 0.07705
Avg Forward Time: 0.65984
Epoch Time: 0:01:34.150886
Avg Data Time: 0.11144
Avg GPU Trans Time: 0.07348
Avg Forward Time: 0.61979
Epoch Time: 0:01:37.302760
Avg Data Time: 0.09911
Avg GPU Trans Time: 0.07546
Avg Forward Time: 0.65708
Epoch Time: 0:01:32.685557
Avg Data Time: 0.10594
Avg GPU Trans Time: 0.07550
Avg Forward Time: 0.61074
Epoch Time: 0:01:39.212098
Avg Data Time: 0.08629
Avg GPU Trans Time: 0.07489
Avg Forward Time: 0.68679
Epoch Time: 0:01:37.842116
Avg Data Time: 0.09609
Avg GPU Trans Time: 0.07863
Avg Forward Time: 0.66154
Epoch Time: 0:01:32.030534
Avg Data Time: 0.09673
Avg GPU Trans Time: 0.06359
Avg Forward Time: 0.62626
[train: 57, 50 / 117] FPS: 64.5 (84.6)  ,  DataTime: 0.171 (0.080)  ,  ForwardTime: 0.741  ,  TotalTime: 0.992  ,  Loss/total: 0.70756  ,  Loss/giou: 0.17160  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28672  ,  IoU: 0.84097[train: 57, 50 / 117] FPS: 64.6 (84.9)  ,  DataTime: 0.227 (0.085)  ,  ForwardTime: 0.679  ,  TotalTime: 0.991  ,  Loss/total: 0.70726  ,  Loss/giou: 0.17149  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28605  ,  IoU: 0.84093

[train: 57, 50 / 117] FPS: 64.5 (84.9)  ,  DataTime: 0.187 (0.079)  ,  ForwardTime: 0.726  ,  TotalTime: 0.992  ,  Loss/total: 0.70332  ,  Loss/giou: 0.17479  ,  Loss/l1: 0.01606  ,  Loss/location: 0.27341  ,  IoU: 0.83866
[train: 57, 50 / 117] FPS: 64.5 (84.6)  ,  DataTime: 0.210 (0.086)  ,  ForwardTime: 0.696  ,  TotalTime: 0.992  ,  Loss/total: 0.68477  ,  Loss/giou: 0.16668  ,  Loss/l1: 0.01473  ,  Loss/location: 0.27777  ,  IoU: 0.84444
[train: 57, 50 / 117] FPS: 64.5 (84.9)  ,  DataTime: 0.209 (0.081)  ,  ForwardTime: 0.702  ,  TotalTime: 0.992  ,  Loss/total: 0.72576  ,  Loss/giou: 0.17765  ,  Loss/l1: 0.01621  ,  Loss/location: 0.28940  ,  IoU: 0.83529
[train: 57, 50 / 117] FPS: 64.6 (84.4)  ,  DataTime: 0.195 (0.074)  ,  ForwardTime: 0.722  ,  TotalTime: 0.991  ,  Loss/total: 0.70882  ,  Loss/giou: 0.17192  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28754  ,  IoU: 0.84014
[train: 57, 50 / 117] FPS: 64.5 (84.5)  ,  DataTime: 0.210 (0.088)  ,  ForwardTime: 0.693  ,  TotalTime: 0.992  ,  Loss/total: 0.72698  ,  Loss/giou: 0.17759  ,  Loss/l1: 0.01629  ,  Loss/location: 0.29036  ,  IoU: 0.83571
[train: 57, 50 / 117] FPS: 64.5 (84.6)  ,  DataTime: 0.200 (0.073)  ,  ForwardTime: 0.718  ,  TotalTime: 0.992  ,  Loss/total: 0.70349  ,  Loss/giou: 0.16926  ,  Loss/l1: 0.01514  ,  Loss/location: 0.28928  ,  IoU: 0.84202
[train: 57, 100 / 117] FPS: 73.4 (104.6)  ,  DataTime: 0.124 (0.070)  ,  ForwardTime: 0.678  ,  TotalTime: 0.872  ,  Loss/total: 0.70696  ,  Loss/giou: 0.17327  ,  Loss/l1: 0.01553  ,  Loss/location: 0.28277  ,  IoU: 0.83887[train: 57, 100 / 117] FPS: 73.4 (104.6)  ,  DataTime: 0.131 (0.079)  ,  ForwardTime: 0.662  ,  TotalTime: 0.872  ,  Loss/total: 0.72219  ,  Loss/giou: 0.17692  ,  Loss/l1: 0.01637  ,  Loss/location: 0.28653  ,  IoU: 0.83646[train: 57, 100 / 117] FPS: 73.4 (104.3)  ,  DataTime: 0.126 (0.068)  ,  ForwardTime: 0.677  ,  TotalTime: 0.872  ,  Loss/total: 0.69456  ,  Loss/giou: 0.16945  ,  Loss/l1: 0.01515  ,  Loss/location: 0.27994  ,  IoU: 0.84239


[train: 57, 100 / 117] FPS: 73.4 (104.2)  ,  DataTime: 0.111 (0.079)  ,  ForwardTime: 0.682  ,  TotalTime: 0.872  ,  Loss/total: 0.69307  ,  Loss/giou: 0.16779  ,  Loss/l1: 0.01511  ,  Loss/location: 0.28192  ,  IoU: 0.84393
[train: 57, 100 / 117] FPS: 73.4 (104.6)  ,  DataTime: 0.132 (0.085)  ,  ForwardTime: 0.655  ,  TotalTime: 0.872  ,  Loss/total: 0.71647  ,  Loss/giou: 0.17477  ,  Loss/l1: 0.01616  ,  Loss/location: 0.28615  ,  IoU: 0.83838
[train: 57, 100 / 117] FPS: 73.4 (104.2)  ,  DataTime: 0.140 (0.083)  ,  ForwardTime: 0.648  ,  TotalTime: 0.872  ,  Loss/total: 0.70061  ,  Loss/giou: 0.17046  ,  Loss/l1: 0.01559  ,  Loss/location: 0.28176  ,  IoU: 0.84201[train: 57, 100 / 117] FPS: 73.4 (104.2)  ,  DataTime: 0.132 (0.081)  ,  ForwardTime: 0.658  ,  TotalTime: 0.872  ,  Loss/total: 0.70041  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01534  ,  Loss/location: 0.28147  ,  IoU: 0.84097

[train: 57, 100 / 117] FPS: 73.4 (104.2)  ,  DataTime: 0.117 (0.077)  ,  ForwardTime: 0.677  ,  TotalTime: 0.872  ,  Loss/total: 0.70079  ,  Loss/giou: 0.17249  ,  Loss/l1: 0.01563  ,  Loss/location: 0.27765  ,  IoU: 0.83995
[train: 57, 117 / 117] FPS: 77.1 (108.1)  ,  DataTime: 0.099 (0.074)  ,  ForwardTime: 0.657  ,  TotalTime: 0.830  ,  Loss/total: 0.68858  ,  Loss/giou: 0.16712  ,  Loss/l1: 0.01494  ,  Loss/location: 0.27964  ,  IoU: 0.84420
[train: 57, 117 / 117] FPS: 77.1 (108.1)  ,  DataTime: 0.118 (0.079)  ,  ForwardTime: 0.634  ,  TotalTime: 0.830  ,  Loss/total: 0.70825  ,  Loss/giou: 0.17290  ,  Loss/l1: 0.01581  ,  Loss/location: 0.28338  ,  IoU: 0.83982[train: 57, 117 / 117] FPS: 77.1 (108.1)  ,  DataTime: 0.105 (0.073)  ,  ForwardTime: 0.653  ,  TotalTime: 0.831  ,  Loss/total: 0.69909  ,  Loss/giou: 0.17200  ,  Loss/l1: 0.01553  ,  Loss/location: 0.27744  ,  IoU: 0.84032

[train: 57, 117 / 117] FPS: 77.1 (108.2)  ,  DataTime: 0.111 (0.067)  ,  ForwardTime: 0.653  ,  TotalTime: 0.830  ,  Loss/total: 0.70541  ,  Loss/giou: 0.17249  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28296  ,  IoU: 0.83964[train: 57, 117 / 117] FPS: 77.1 (108.2)  ,  DataTime: 0.117 (0.075)  ,  ForwardTime: 0.639  ,  TotalTime: 0.830  ,  Loss/total: 0.71701  ,  Loss/giou: 0.17526  ,  Loss/l1: 0.01621  ,  Loss/location: 0.28546  ,  IoU: 0.83805

[train: 57, 117 / 117] FPS: 77.1 (108.0)  ,  DataTime: 0.118 (0.076)  ,  ForwardTime: 0.636  ,  TotalTime: 0.830  ,  Loss/total: 0.70295  ,  Loss/giou: 0.17141  ,  Loss/l1: 0.01533  ,  Loss/location: 0.28348  ,  IoU: 0.84058
[train: 57, 117 / 117] FPS: 77.1 (108.1)  ,  DataTime: 0.112 (0.065)  ,  ForwardTime: 0.653  ,  TotalTime: 0.830  ,  Loss/total: 0.69536  ,  Loss/giou: 0.16940  ,  Loss/l1: 0.01503  ,  Loss/location: 0.28139  ,  IoU: 0.84212
[train: 57, 117 / 117] FPS: 77.1 (108.2)  ,  DataTime: 0.125 (0.079)  ,  ForwardTime: 0.627  ,  TotalTime: 0.830  ,  Loss/total: 0.69729  ,  Loss/giou: 0.16975  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28049  ,  IoU: 0.84258
Epoch Time: 0:01:37.170801
Avg Data Time: 0.10471
Avg GPU Trans Time: 0.07290
Avg Forward Time: 0.65290
Epoch Time: 0:01:37.153226
Avg Data Time: 0.12477
Avg GPU Trans Time: 0.07861
Avg Forward Time: 0.62699
Epoch Time: 0:01:37.165148
Avg Data Time: 0.09932
Avg GPU Trans Time: 0.07440
Avg Forward Time: 0.65676
Epoch Time: 0:01:37.165289
Avg Data Time: 0.11764
Avg GPU Trans Time: 0.07914
Avg Forward Time: 0.63369
Epoch Time: 0:01:37.158000
Avg Data Time: 0.11702
Avg GPU Trans Time: 0.07454
Avg Forward Time: 0.63885
Epoch Time: 0:01:37.162969
Avg Data Time: 0.11834
Avg GPU Trans Time: 0.07621
Avg Forward Time: 0.63590
Epoch Time: 0:01:37.159250
Avg Data Time: 0.11236
Avg GPU Trans Time: 0.06471
Avg Forward Time: 0.65335
Epoch Time: 0:01:37.151403
Avg Data Time: 0.11098
Avg GPU Trans Time: 0.06667
Avg Forward Time: 0.65271
[train: 58, 50 / 117] FPS: 64.2 (84.7)  ,  DataTime: 0.215 (0.088)  ,  ForwardTime: 0.694  ,  TotalTime: 0.997  ,  Loss/total: 0.67706  ,  Loss/giou: 0.16417  ,  Loss/l1: 0.01408  ,  Loss/location: 0.27833  ,  IoU: 0.84564
[train: 58, 50 / 117] FPS: 64.2 (84.7)  ,  DataTime: 0.174 (0.083)  ,  ForwardTime: 0.739  ,  TotalTime: 0.996  ,  Loss/total: 0.70156  ,  Loss/giou: 0.17082  ,  Loss/l1: 0.01506  ,  Loss/location: 0.28461  ,  IoU: 0.84093
[train: 58, 50 / 117] FPS: 64.2 (84.6)  ,  DataTime: 0.194 (0.088)  ,  ForwardTime: 0.715  ,  TotalTime: 0.997  ,  Loss/total: 0.67743  ,  Loss/giou: 0.16558  ,  Loss/l1: 0.01435  ,  Loss/location: 0.27451  ,  IoU: 0.84517
[train: 58, 50 / 117] FPS: 64.2 (84.4)  ,  DataTime: 0.203 (0.084)  ,  ForwardTime: 0.709  ,  TotalTime: 0.996  ,  Loss/total: 0.69385  ,  Loss/giou: 0.16843  ,  Loss/l1: 0.01546  ,  Loss/location: 0.27966  ,  IoU: 0.84346
[train: 58, 50 / 117] FPS: 64.2 (84.4)  ,  DataTime: 0.224 (0.088)  ,  ForwardTime: 0.685  ,  TotalTime: 0.997  ,  Loss/total: 0.71173  ,  Loss/giou: 0.17250  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28898  ,  IoU: 0.83994
[train: 58, 50 / 117] FPS: 64.2 (84.0)  ,  DataTime: 0.199 (0.072)  ,  ForwardTime: 0.725  ,  TotalTime: 0.996  ,  Loss/total: 0.72861  ,  Loss/giou: 0.17842  ,  Loss/l1: 0.01717  ,  Loss/location: 0.28592  ,  IoU: 0.83618
[train: 58, 50 / 117] FPS: 64.2 (84.2)  ,  DataTime: 0.200 (0.084)  ,  ForwardTime: 0.712  ,  TotalTime: 0.997  ,  Loss/total: 0.71105  ,  Loss/giou: 0.17453  ,  Loss/l1: 0.01615  ,  Loss/location: 0.28127  ,  IoU: 0.83886[train: 58, 50 / 117] FPS: 64.2 (84.7)  ,  DataTime: 0.205 (0.088)  ,  ForwardTime: 0.704  ,  TotalTime: 0.997  ,  Loss/total: 0.72775  ,  Loss/giou: 0.17838  ,  Loss/l1: 0.01756  ,  Loss/location: 0.28318  ,  IoU: 0.83779

[train: 58, 100 / 117] FPS: 73.2 (107.5)  ,  DataTime: 0.134 (0.083)  ,  ForwardTime: 0.657  ,  TotalTime: 0.875  ,  Loss/total: 0.68531  ,  Loss/giou: 0.16712  ,  Loss/l1: 0.01471  ,  Loss/location: 0.27753  ,  IoU: 0.84380[train: 58, 100 / 117] FPS: 73.2 (107.5)  ,  DataTime: 0.111 (0.080)  ,  ForwardTime: 0.683  ,  TotalTime: 0.874  ,  Loss/total: 0.70594  ,  Loss/giou: 0.17253  ,  Loss/l1: 0.01555  ,  Loss/location: 0.28315  ,  IoU: 0.83971[train: 58, 100 / 117] FPS: 73.2 (107.5)  ,  DataTime: 0.128 (0.083)  ,  ForwardTime: 0.663  ,  TotalTime: 0.875  ,  Loss/total: 0.71508  ,  Loss/giou: 0.17373  ,  Loss/l1: 0.01644  ,  Loss/location: 0.28545  ,  IoU: 0.84047


[train: 58, 100 / 117] FPS: 73.2 (107.5)  ,  DataTime: 0.123 (0.082)  ,  ForwardTime: 0.670  ,  TotalTime: 0.875  ,  Loss/total: 0.69313  ,  Loss/giou: 0.17029  ,  Loss/l1: 0.01506  ,  Loss/location: 0.27726  ,  IoU: 0.84151
[train: 58, 100 / 117] FPS: 73.2 (107.4)  ,  DataTime: 0.138 (0.082)  ,  ForwardTime: 0.655  ,  TotalTime: 0.875  ,  Loss/total: 0.71850  ,  Loss/giou: 0.17414  ,  Loss/l1: 0.01602  ,  Loss/location: 0.29011  ,  IoU: 0.83880
[train: 58, 100 / 117] FPS: 73.2 (107.5)  ,  DataTime: 0.126 (0.081)  ,  ForwardTime: 0.667  ,  TotalTime: 0.875  ,  Loss/total: 0.71440  ,  Loss/giou: 0.17383  ,  Loss/l1: 0.01626  ,  Loss/location: 0.28546  ,  IoU: 0.83962
[train: 58, 100 / 117] FPS: 73.2 (107.4)  ,  DataTime: 0.127 (0.081)  ,  ForwardTime: 0.666  ,  TotalTime: 0.874  ,  Loss/total: 0.68231  ,  Loss/giou: 0.16550  ,  Loss/l1: 0.01476  ,  Loss/location: 0.27750  ,  IoU: 0.84512
[train: 58, 100 / 117] FPS: 73.2 (107.8)  ,  DataTime: 0.125 (0.070)  ,  ForwardTime: 0.679  ,  TotalTime: 0.874  ,  Loss/total: 0.72337  ,  Loss/giou: 0.17703  ,  Loss/l1: 0.01661  ,  Loss/location: 0.28627  ,  IoU: 0.83650
[train: 58, 117 / 117] FPS: 76.9 (110.2)  ,  DataTime: 0.119 (0.078)  ,  ForwardTime: 0.636  ,  TotalTime: 0.833  ,  Loss/total: 0.68762  ,  Loss/giou: 0.16756  ,  Loss/l1: 0.01482  ,  Loss/location: 0.27840  ,  IoU: 0.84347
[train: 58, 117 / 117] FPS: 76.9 (110.1)  ,  DataTime: 0.115 (0.078)  ,  ForwardTime: 0.640  ,  TotalTime: 0.833  ,  Loss/total: 0.71451  ,  Loss/giou: 0.17386  ,  Loss/l1: 0.01642  ,  Loss/location: 0.28468  ,  IoU: 0.84038
[train: 58, 117 / 117] FPS: 76.9 (110.0)  ,  DataTime: 0.114 (0.077)  ,  ForwardTime: 0.642  ,  TotalTime: 0.832  ,  Loss/total: 0.68390  ,  Loss/giou: 0.16614  ,  Loss/l1: 0.01482  ,  Loss/location: 0.27752  ,  IoU: 0.84464
[train: 58, 117 / 117] FPS: 76.9 (110.0)  ,  DataTime: 0.113 (0.076)  ,  ForwardTime: 0.643  ,  TotalTime: 0.833  ,  Loss/total: 0.71226  ,  Loss/giou: 0.17285  ,  Loss/l1: 0.01598  ,  Loss/location: 0.28666  ,  IoU: 0.84008
[train: 58, 117 / 117] FPS: 76.9 (110.0)  ,  DataTime: 0.123 (0.077)  ,  ForwardTime: 0.633  ,  TotalTime: 0.833  ,  Loss/total: 0.72218  ,  Loss/giou: 0.17538  ,  Loss/l1: 0.01635  ,  Loss/location: 0.28966  ,  IoU: 0.83816
[train: 58, 117 / 117] FPS: 76.9 (109.9)  ,  DataTime: 0.110 (0.076)  ,  ForwardTime: 0.646  ,  TotalTime: 0.833  ,  Loss/total: 0.68982  ,  Loss/giou: 0.16927  ,  Loss/l1: 0.01490  ,  Loss/location: 0.27676  ,  IoU: 0.84225
[train: 58, 117 / 117] FPS: 76.9 (110.1)  ,  DataTime: 0.112 (0.066)  ,  ForwardTime: 0.654  ,  TotalTime: 0.832  ,  Loss/total: 0.71385  ,  Loss/giou: 0.17446  ,  Loss/l1: 0.01611  ,  Loss/location: 0.28437  ,  IoU: 0.83833
[train: 58, 117 / 117] FPS: 76.9 (109.5)  ,  DataTime: 0.099 (0.075)  ,  ForwardTime: 0.658  ,  TotalTime: 0.832  ,  Loss/total: 0.70192  ,  Loss/giou: 0.17148  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28207  ,  IoU: 0.84041
Epoch Time: 0:01:37.405271
Avg Data Time: 0.11916
Avg GPU Trans Time: 0.07779
Avg Forward Time: 0.63557
Epoch Time: 0:01:37.398647
Avg Data Time: 0.09950
Avg GPU Trans Time: 0.07509
Avg Forward Time: 0.65788
Epoch Time: 0:01:37.405486
Avg Data Time: 0.11334
Avg GPU Trans Time: 0.07608
Avg Forward Time: 0.64311
Epoch Time: 0:01:37.420166
Avg Data Time: 0.11498
Avg GPU Trans Time: 0.07782
Avg Forward Time: 0.63986
Epoch Time: 0:01:37.386144
Avg Data Time: 0.11382
Avg GPU Trans Time: 0.07663
Avg Forward Time: 0.64191
Epoch Time: 0:01:37.395823
Avg Data Time: 0.11203
Avg GPU Trans Time: 0.06615
Avg Forward Time: 0.65427
Epoch Time: 0:01:37.417696
Avg Data Time: 0.11044
Avg GPU Trans Time: 0.07640
Avg Forward Time: 0.64579
Epoch Time: 0:01:37.432718
Avg Data Time: 0.12278
Avg GPU Trans Time: 0.07737
Avg Forward Time: 0.63261
[train: 59, 50 / 117] FPS: 63.6 (86.2)  ,  DataTime: 0.212 (0.076)  ,  ForwardTime: 0.719  ,  TotalTime: 1.006  ,  Loss/total: 0.71983  ,  Loss/giou: 0.17461  ,  Loss/l1: 0.01638  ,  Loss/location: 0.28868  ,  IoU: 0.83958
[train: 59, 50 / 117] FPS: 63.6 (85.9)  ,  DataTime: 0.212 (0.086)  ,  ForwardTime: 0.708  ,  TotalTime: 1.006  ,  Loss/total: 0.68944  ,  Loss/giou: 0.16778  ,  Loss/l1: 0.01519  ,  Loss/location: 0.27794  ,  IoU: 0.84405
[train: 59, 50 / 117] FPS: 63.6 (85.7)  ,  DataTime: 0.195 (0.089)  ,  ForwardTime: 0.723  ,  TotalTime: 1.006  ,  Loss/total: 0.70894  ,  Loss/giou: 0.17263  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28624  ,  IoU: 0.83891
[train: 59, 50 / 117] FPS: 63.6 (86.1)  ,  DataTime: 0.197 (0.084)  ,  ForwardTime: 0.725  ,  TotalTime: 1.006  ,  Loss/total: 0.67024  ,  Loss/giou: 0.16500  ,  Loss/l1: 0.01477  ,  Loss/location: 0.26640  ,  IoU: 0.84610[train: 59, 50 / 117] FPS: 63.6 (85.7)  ,  DataTime: 0.201 (0.085)  ,  ForwardTime: 0.720  ,  TotalTime: 1.006  ,  Loss/total: 0.72338  ,  Loss/giou: 0.17367  ,  Loss/l1: 0.01587  ,  Loss/location: 0.29667  ,  IoU: 0.83897

[train: 59, 50 / 117] FPS: 63.6 (86.0)  ,  DataTime: 0.193 (0.087)  ,  ForwardTime: 0.726  ,  TotalTime: 1.006  ,  Loss/total: 0.68551  ,  Loss/giou: 0.16839  ,  Loss/l1: 0.01519  ,  Loss/location: 0.27277  ,  IoU: 0.84314
[train: 59, 50 / 117] FPS: 63.6 (85.9)  ,  DataTime: 0.196 (0.086)  ,  ForwardTime: 0.724  ,  TotalTime: 1.006  ,  Loss/total: 0.69863  ,  Loss/giou: 0.16794  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28628  ,  IoU: 0.84434
[train: 59, 50 / 117] FPS: 63.6 (85.7)  ,  DataTime: 0.205 (0.086)  ,  ForwardTime: 0.716  ,  TotalTime: 1.007  ,  Loss/total: 0.69931  ,  Loss/giou: 0.17099  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28005  ,  IoU: 0.84090
[train: 59, 100 / 117] FPS: 72.5 (103.9)  ,  DataTime: 0.128 (0.083)  ,  ForwardTime: 0.672  ,  TotalTime: 0.882  ,  Loss/total: 0.71252  ,  Loss/giou: 0.17272  ,  Loss/l1: 0.01579  ,  Loss/location: 0.28814  ,  IoU: 0.83962
[train: 59, 100 / 117] FPS: 72.5 (103.8)  ,  DataTime: 0.124 (0.084)  ,  ForwardTime: 0.674  ,  TotalTime: 0.882  ,  Loss/total: 0.70243  ,  Loss/giou: 0.17054  ,  Loss/l1: 0.01548  ,  Loss/location: 0.28398  ,  IoU: 0.84142
[train: 59, 100 / 117] FPS: 72.5 (103.8)  ,  DataTime: 0.123 (0.082)  ,  ForwardTime: 0.677  ,  TotalTime: 0.882  ,  Loss/total: 0.69668  ,  Loss/giou: 0.17042  ,  Loss/l1: 0.01548  ,  Loss/location: 0.27847  ,  IoU: 0.84166
[train: 59, 100 / 117] FPS: 72.5 (104.1)  ,  DataTime: 0.125 (0.083)  ,  ForwardTime: 0.675  ,  TotalTime: 0.882  ,  Loss/total: 0.69162  ,  Loss/giou: 0.16820  ,  Loss/l1: 0.01509  ,  Loss/location: 0.27978  ,  IoU: 0.84351
[train: 59, 100 / 117] FPS: 72.6 (104.3)  ,  DataTime: 0.125 (0.080)  ,  ForwardTime: 0.677  ,  TotalTime: 0.882  ,  Loss/total: 0.69027  ,  Loss/giou: 0.16968  ,  Loss/l1: 0.01535  ,  Loss/location: 0.27418  ,  IoU: 0.84222
[train: 59, 100 / 117] FPS: 72.6 (103.8)  ,  DataTime: 0.131 (0.072)  ,  ForwardTime: 0.679  ,  TotalTime: 0.882  ,  Loss/total: 0.72267  ,  Loss/giou: 0.17510  ,  Loss/l1: 0.01643  ,  Loss/location: 0.29032  ,  IoU: 0.83876
[train: 59, 100 / 117] FPS: 72.6 (103.9)  ,  DataTime: 0.134 (0.083)  ,  ForwardTime: 0.665  ,  TotalTime: 0.882  ,  Loss/total: 0.68793  ,  Loss/giou: 0.16757  ,  Loss/l1: 0.01501  ,  Loss/location: 0.27775  ,  IoU: 0.84385
[train: 59, 100 / 117] FPS: 72.5 (103.4)  ,  DataTime: 0.128 (0.083)  ,  ForwardTime: 0.671  ,  TotalTime: 0.883  ,  Loss/total: 0.71557  ,  Loss/giou: 0.17393  ,  Loss/l1: 0.01611  ,  Loss/location: 0.28715  ,  IoU: 0.83917
[train: 59, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.114 (0.078)  ,  ForwardTime: 0.647  ,  TotalTime: 0.839  ,  Loss/total: 0.70475  ,  Loss/giou: 0.17150  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28357  ,  IoU: 0.84081[train: 59, 117 / 117] FPS: 76.3 (111.9)  ,  DataTime: 0.120 (0.078)  ,  ForwardTime: 0.641  ,  TotalTime: 0.839  ,  Loss/total: 0.68564  ,  Loss/giou: 0.16717  ,  Loss/l1: 0.01481  ,  Loss/location: 0.27725  ,  IoU: 0.84390[train: 59, 117 / 117] FPS: 76.3 (112.1)  ,  DataTime: 0.117 (0.067)  ,  ForwardTime: 0.655  ,  TotalTime: 0.839  ,  Loss/total: 0.72274  ,  Loss/giou: 0.17501  ,  Loss/l1: 0.01654  ,  Loss/location: 0.29002  ,  IoU: 0.83906[train: 59, 117 / 117] FPS: 76.3 (112.2)  ,  DataTime: 0.112 (0.076)  ,  ForwardTime: 0.651  ,  TotalTime: 0.839  ,  Loss/total: 0.69109  ,  Loss/giou: 0.16945  ,  Loss/l1: 0.01523  ,  Loss/location: 0.27602  ,  IoU: 0.84231



[train: 59, 117 / 117] FPS: 76.2 (111.9)  ,  DataTime: 0.115 (0.078)  ,  ForwardTime: 0.647  ,  TotalTime: 0.839  ,  Loss/total: 0.71389  ,  Loss/giou: 0.17346  ,  Loss/l1: 0.01606  ,  Loss/location: 0.28669  ,  IoU: 0.83972
[train: 59, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.112 (0.078)  ,  ForwardTime: 0.649  ,  TotalTime: 0.839  ,  Loss/total: 0.69856  ,  Loss/giou: 0.16963  ,  Loss/l1: 0.01529  ,  Loss/location: 0.28286  ,  IoU: 0.84220
[train: 59, 117 / 117] FPS: 76.3 (111.8)  ,  DataTime: 0.111 (0.079)  ,  ForwardTime: 0.649  ,  TotalTime: 0.839  ,  Loss/total: 0.69988  ,  Loss/giou: 0.17023  ,  Loss/l1: 0.01541  ,  Loss/location: 0.28233  ,  IoU: 0.84165
[train: 59, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.110 (0.078)  ,  ForwardTime: 0.651  ,  TotalTime: 0.839  ,  Loss/total: 0.69776  ,  Loss/giou: 0.17044  ,  Loss/l1: 0.01548  ,  Loss/location: 0.27948  ,  IoU: 0.84164
Epoch Time: 0:01:38.218526
Avg Data Time: 0.11472
Avg GPU Trans Time: 0.07807
Avg Forward Time: 0.64668
Epoch Time: 0:01:38.176545
Avg Data Time: 0.11181
Avg GPU Trans Time: 0.07805
Avg Forward Time: 0.64926
Epoch Time: 0:01:38.174597
Avg Data Time: 0.11008
Avg GPU Trans Time: 0.07776
Avg Forward Time: 0.65126
Epoch Time: 0:01:38.178977
Avg Data Time: 0.11401
Avg GPU Trans Time: 0.07811
Avg Forward Time: 0.64702
Epoch Time: 0:01:38.169573
Avg Data Time: 0.11236
Avg GPU Trans Time: 0.07578
Avg Forward Time: 0.65092
Epoch Time: 0:01:38.170026
Avg Data Time: 0.11708
Avg GPU Trans Time: 0.06728
Avg Forward Time: 0.65470
Epoch Time: 0:01:38.196480
Avg Data Time: 0.11113
Avg GPU Trans Time: 0.07886
Avg Forward Time: 0.64929
Epoch Time: 0:01:38.148458
Avg Data Time: 0.11962
Avg GPU Trans Time: 0.07785
Avg Forward Time: 0.64141
[train: 60, 50 / 117] FPS: 63.6 (83.3)  ,  DataTime: 0.214 (0.082)  ,  ForwardTime: 0.711  ,  TotalTime: 1.006  ,  Loss/total: 0.71123  ,  Loss/giou: 0.17245  ,  Loss/l1: 0.01546  ,  Loss/location: 0.28901  ,  IoU: 0.84004[train: 60, 50 / 117] FPS: 63.6 (83.7)  ,  DataTime: 0.210 (0.081)  ,  ForwardTime: 0.716  ,  TotalTime: 1.006  ,  Loss/total: 0.67564  ,  Loss/giou: 0.16353  ,  Loss/l1: 0.01424  ,  Loss/location: 0.27737  ,  IoU: 0.84664

[train: 60, 50 / 117] FPS: 63.6 (83.3)  ,  DataTime: 0.219 (0.083)  ,  ForwardTime: 0.703  ,  TotalTime: 1.006  ,  Loss/total: 0.69963  ,  Loss/giou: 0.16816  ,  Loss/l1: 0.01510  ,  Loss/location: 0.28783  ,  IoU: 0.84361
[train: 60, 50 / 117] FPS: 63.6 (83.3)  ,  DataTime: 0.185 (0.085)  ,  ForwardTime: 0.736  ,  TotalTime: 1.006  ,  Loss/total: 0.69364  ,  Loss/giou: 0.16739  ,  Loss/l1: 0.01473  ,  Loss/location: 0.28522  ,  IoU: 0.84348
[train: 60, 50 / 117] FPS: 63.6 (83.4)  ,  DataTime: 0.188 (0.080)  ,  ForwardTime: 0.737  ,  TotalTime: 1.006  ,  Loss/total: 0.70355  ,  Loss/giou: 0.16911  ,  Loss/l1: 0.01557  ,  Loss/location: 0.28750  ,  IoU: 0.84359
[train: 60, 50 / 117] FPS: 63.6 (83.5)  ,  DataTime: 0.205 (0.088)  ,  ForwardTime: 0.713  ,  TotalTime: 1.006  ,  Loss/total: 0.71415  ,  Loss/giou: 0.17254  ,  Loss/l1: 0.01559  ,  Loss/location: 0.29110  ,  IoU: 0.83997
[train: 60, 50 / 117] FPS: 63.6 (83.3)  ,  DataTime: 0.207 (0.078)  ,  ForwardTime: 0.721  ,  TotalTime: 1.006  ,  Loss/total: 0.69348  ,  Loss/giou: 0.17019  ,  Loss/l1: 0.01467  ,  Loss/location: 0.27974  ,  IoU: 0.84044
[train: 60, 50 / 117] FPS: 63.6 (83.4)  ,  DataTime: 0.186 (0.090)  ,  ForwardTime: 0.730  ,  TotalTime: 1.006  ,  Loss/total: 0.71185  ,  Loss/giou: 0.17553  ,  Loss/l1: 0.01613  ,  Loss/location: 0.28016  ,  IoU: 0.83759
[train: 60, 100 / 117] FPS: 72.6 (107.0)  ,  DataTime: 0.129 (0.082)  ,  ForwardTime: 0.670  ,  TotalTime: 0.881  ,  Loss/total: 0.69183  ,  Loss/giou: 0.16901  ,  Loss/l1: 0.01515  ,  Loss/location: 0.27804  ,  IoU: 0.84271
[train: 60, 100 / 117] FPS: 72.6 (106.4)  ,  DataTime: 0.133 (0.078)  ,  ForwardTime: 0.670  ,  TotalTime: 0.882  ,  Loss/total: 0.69375  ,  Loss/giou: 0.16851  ,  Loss/l1: 0.01525  ,  Loss/location: 0.28046  ,  IoU: 0.84349
[train: 60, 100 / 117] FPS: 72.6 (106.4)  ,  DataTime: 0.133 (0.080)  ,  ForwardTime: 0.668  ,  TotalTime: 0.882  ,  Loss/total: 0.70330  ,  Loss/giou: 0.17039  ,  Loss/l1: 0.01526  ,  Loss/location: 0.28620  ,  IoU: 0.84187
[train: 60, 100 / 117] FPS: 72.6 (106.4)  ,  DataTime: 0.120 (0.080)  ,  ForwardTime: 0.682  ,  TotalTime: 0.881  ,  Loss/total: 0.69964  ,  Loss/giou: 0.16974  ,  Loss/l1: 0.01568  ,  Loss/location: 0.28177  ,  IoU: 0.84283
[train: 60, 100 / 117] FPS: 72.6 (106.9)  ,  DataTime: 0.134 (0.077)  ,  ForwardTime: 0.671  ,  TotalTime: 0.881  ,  Loss/total: 0.69072  ,  Loss/giou: 0.16757  ,  Loss/l1: 0.01487  ,  Loss/location: 0.28121  ,  IoU: 0.84389
[train: 60, 100 / 117] FPS: 72.6 (106.4)  ,  DataTime: 0.120 (0.084)  ,  ForwardTime: 0.677  ,  TotalTime: 0.881  ,  Loss/total: 0.70071  ,  Loss/giou: 0.17124  ,  Loss/l1: 0.01533  ,  Loss/location: 0.28159  ,  IoU: 0.84096
[train: 60, 100 / 117] FPS: 72.6 (106.7)  ,  DataTime: 0.120 (0.088)  ,  ForwardTime: 0.673  ,  TotalTime: 0.882  ,  Loss/total: 0.69671  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01521  ,  Loss/location: 0.27966  ,  IoU: 0.84127
[train: 60, 100 / 117] FPS: 72.6 (106.5)  ,  DataTime: 0.131 (0.074)  ,  ForwardTime: 0.676  ,  TotalTime: 0.881  ,  Loss/total: 0.69547  ,  Loss/giou: 0.17022  ,  Loss/l1: 0.01488  ,  Loss/location: 0.28065  ,  IoU: 0.84067
[train: 60, 117 / 117] FPS: 76.3 (112.1)  ,  DataTime: 0.116 (0.077)  ,  ForwardTime: 0.645  ,  TotalTime: 0.838  ,  Loss/total: 0.69968  ,  Loss/giou: 0.17101  ,  Loss/l1: 0.01555  ,  Loss/location: 0.27991  ,  IoU: 0.84143
[train: 60, 117 / 117] FPS: 76.3 (112.1)  ,  DataTime: 0.108 (0.083)  ,  ForwardTime: 0.647  ,  TotalTime: 0.838  ,  Loss/total: 0.70129  ,  Loss/giou: 0.17172  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28052  ,  IoU: 0.84057
[train: 60, 117 / 117] FPS: 76.4 (112.0)  ,  DataTime: 0.107 (0.075)  ,  ForwardTime: 0.656  ,  TotalTime: 0.838  ,  Loss/total: 0.69924  ,  Loss/giou: 0.16947  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28204  ,  IoU: 0.84301
[train: 60, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.119 (0.074)  ,  ForwardTime: 0.646  ,  TotalTime: 0.838  ,  Loss/total: 0.69223  ,  Loss/giou: 0.16771  ,  Loss/l1: 0.01507  ,  Loss/location: 0.28146  ,  IoU: 0.84390
[train: 60, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.119 (0.072)  ,  ForwardTime: 0.647  ,  TotalTime: 0.838  ,  Loss/total: 0.69778  ,  Loss/giou: 0.16954  ,  Loss/l1: 0.01515  ,  Loss/location: 0.28293  ,  IoU: 0.84237
[train: 60, 117 / 117] FPS: 76.4 (112.1)  ,  DataTime: 0.117 (0.070)  ,  ForwardTime: 0.652  ,  TotalTime: 0.838  ,  Loss/total: 0.69697  ,  Loss/giou: 0.17027  ,  Loss/l1: 0.01487  ,  Loss/location: 0.28207  ,  IoU: 0.84057[train: 60, 117 / 117] FPS: 76.4 (112.0)  ,  DataTime: 0.107 (0.079)  ,  ForwardTime: 0.652  ,  TotalTime: 0.838  ,  Loss/total: 0.70321  ,  Loss/giou: 0.17154  ,  Loss/l1: 0.01551  ,  Loss/location: 0.28259  ,  IoU: 0.84085

[train: 60, 117 / 117] FPS: 76.3 (112.0)  ,  DataTime: 0.119 (0.075)  ,  ForwardTime: 0.644  ,  TotalTime: 0.839  ,  Loss/total: 0.70502  ,  Loss/giou: 0.17119  ,  Loss/l1: 0.01539  ,  Loss/location: 0.28571  ,  IoU: 0.84120
Epoch Time: 0:01:38.067006
Avg Data Time: 0.10693
Avg GPU Trans Time: 0.07536
Avg Forward Time: 0.65588
Epoch Time: 0:01:38.082595
Avg Data Time: 0.10766
Avg GPU Trans Time: 0.08335
Avg Forward Time: 0.64731
Epoch Time: 0:01:38.108632
Avg Data Time: 0.11935
Avg GPU Trans Time: 0.07535
Avg Forward Time: 0.64384
Epoch Time: 0:01:38.064706
Avg Data Time: 0.10749
Avg GPU Trans Time: 0.07910
Avg Forward Time: 0.65157
Epoch Time: 0:01:38.068146
Avg Data Time: 0.11678
Avg GPU Trans Time: 0.06987
Avg Forward Time: 0.65154
Epoch Time: 0:01:38.078924
Avg Data Time: 0.11615
Avg GPU Trans Time: 0.07746
Avg Forward Time: 0.64467
Epoch Time: 0:01:38.075643
Avg Data Time: 0.11912
Avg GPU Trans Time: 0.07194
Avg Forward Time: 0.64719
Epoch Time: 0:01:38.088793
Avg Data Time: 0.11896
Avg GPU Trans Time: 0.07378
Avg Forward Time: 0.64562
[val: 60, 50 / 78] FPS: 38.5 (189.2)  ,  DataTime: 1.394 (0.040)  ,  ForwardTime: 0.227  ,  TotalTime: 1.661  ,  Loss/total: 0.86543  ,  Loss/giou: 0.20661  ,  Loss/l1: 0.02251  ,  Loss/location: 0.33964  ,  IoU: 0.81591
[val: 60, 50 / 78] FPS: 38.4 (203.8)  ,  DataTime: 1.280 (0.044)  ,  ForwardTime: 0.342  ,  TotalTime: 1.666  ,  Loss/total: 0.87173  ,  Loss/giou: 0.20991  ,  Loss/l1: 0.02311  ,  Loss/location: 0.33636  ,  IoU: 0.81383
[val: 60, 50 / 78] FPS: 37.9 (212.1)  ,  DataTime: 1.257 (0.041)  ,  ForwardTime: 0.389  ,  TotalTime: 1.687  ,  Loss/total: 0.82815  ,  Loss/giou: 0.19890  ,  Loss/l1: 0.02076  ,  Loss/location: 0.32655  ,  IoU: 0.82168
[val: 60, 50 / 78] FPS: 37.6 (241.0)  ,  DataTime: 1.313 (0.042)  ,  ForwardTime: 0.346  ,  TotalTime: 1.701  ,  Loss/total: 0.82578  ,  Loss/giou: 0.19881  ,  Loss/l1: 0.02103  ,  Loss/location: 0.32302  ,  IoU: 0.82197
[val: 60, 50 / 78] FPS: 37.2 (231.4)  ,  DataTime: 1.254 (0.043)  ,  ForwardTime: 0.425  ,  TotalTime: 1.722  ,  Loss/total: 0.78758  ,  Loss/giou: 0.19051  ,  Loss/l1: 0.01974  ,  Loss/location: 0.30788  ,  IoU: 0.82846
[val: 60, 50 / 78] FPS: 36.9 (220.2)  ,  DataTime: 1.470 (0.042)  ,  ForwardTime: 0.224  ,  TotalTime: 1.736  ,  Loss/total: 0.85154  ,  Loss/giou: 0.20342  ,  Loss/l1: 0.02158  ,  Loss/location: 0.33680  ,  IoU: 0.81842
[val: 60, 50 / 78] FPS: 36.1 (217.2)  ,  DataTime: 1.348 (0.041)  ,  ForwardTime: 0.384  ,  TotalTime: 1.773  ,  Loss/total: 0.79045  ,  Loss/giou: 0.19129  ,  Loss/l1: 0.01977  ,  Loss/location: 0.30904  ,  IoU: 0.82740
[val: 60, 50 / 78] FPS: 30.3 (217.4)  ,  DataTime: 1.832 (0.046)  ,  ForwardTime: 0.232  ,  TotalTime: 2.110  ,  Loss/total: 0.81600  ,  Loss/giou: 0.19735  ,  Loss/l1: 0.02084  ,  Loss/location: 0.31712  ,  IoU: 0.82313
[val: 60, 78 / 78] FPS: 41.5 (208.6)  ,  DataTime: 1.276 (0.040)  ,  ForwardTime: 0.229  ,  TotalTime: 1.544  ,  Loss/total: 0.84850  ,  Loss/giou: 0.20252  ,  Loss/l1: 0.02193  ,  Loss/location: 0.33383  ,  IoU: 0.81897
Epoch Time: 0:02:00.418879
Avg Data Time: 1.27563
Avg GPU Trans Time: 0.03965
Avg Forward Time: 0.22855
/home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints/train/vipt/coesot
[val: 60, 78 / 78] FPS: 41.3 (226.3)  ,  DataTime: 1.154 (0.043)  ,  ForwardTime: 0.352  ,  TotalTime: 1.550  ,  Loss/total: 0.79891  ,  Loss/giou: 0.19325  ,  Loss/l1: 0.01998  ,  Loss/location: 0.31248  ,  IoU: 0.82608
Epoch Time: 0:02:00.881541
Avg Data Time: 1.15421
Avg GPU Trans Time: 0.04325
Avg Forward Time: 0.35230
Finished training!
Finished training!
[val: 60, 78 / 78] FPS: 40.9 (218.4)  ,  DataTime: 1.219 (0.041)  ,  ForwardTime: 0.303  ,  TotalTime: 1.563  ,  Loss/total: 0.82445  ,  Loss/giou: 0.19862  ,  Loss/l1: 0.02083  ,  Loss/location: 0.32306  ,  IoU: 0.82181
[rank0]:[W727 05:13:11.795463058 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
Epoch Time: 0:02:01.946650
Avg Data Time: 1.21897
Avg GPU Trans Time: 0.04102
Avg Forward Time: 0.30342
Finished training!
[val: 60, 78 / 78] FPS: 40.7 (220.7)  ,  DataTime: 1.306 (0.043)  ,  ForwardTime: 0.224  ,  TotalTime: 1.573  ,  Loss/total: 0.85532  ,  Loss/giou: 0.20372  ,  Loss/l1: 0.02168  ,  Loss/location: 0.33949  ,  IoU: 0.81828
[val: 60, 78 / 78] FPS: 40.7 (225.6)  ,  DataTime: 1.204 (0.041)  ,  ForwardTime: 0.327  ,  TotalTime: 1.573  ,  Loss/total: 0.80623  ,  Loss/giou: 0.19391  ,  Loss/l1: 0.01997  ,  Loss/location: 0.31854  ,  IoU: 0.82557
Epoch Time: 0:02:02.694080
Avg Data Time: 1.30600
Avg GPU Trans Time: 0.04262
Avg Forward Time: 0.22439
Finished training!
Epoch Time: 0:02:02.684103
Avg Data Time: 1.20447
Avg GPU Trans Time: 0.04096
Avg Forward Time: 0.32745
Finished training!
[val: 60, 78 / 78] FPS: 40.6 (189.7)  ,  DataTime: 1.204 (0.042)  ,  ForwardTime: 0.332  ,  TotalTime: 1.578  ,  Loss/total: 0.82500  ,  Loss/giou: 0.19766  ,  Loss/l1: 0.02047  ,  Loss/location: 0.32732  ,  IoU: 0.82201
[val: 60, 78 / 78] FPS: 40.5 (226.7)  ,  DataTime: 1.237 (0.043)  ,  ForwardTime: 0.300  ,  TotalTime: 1.580  ,  Loss/total: 0.87221  ,  Loss/giou: 0.20983  ,  Loss/l1: 0.02261  ,  Loss/location: 0.33952  ,  IoU: 0.81311
Epoch Time: 0:02:03.101148
Avg Data Time: 1.20432
Avg GPU Trans Time: 0.04225
Avg Forward Time: 0.33165
Finished training!
Epoch Time: 0:02:03.223564
Avg Data Time: 1.23736
Avg GPU Trans Time: 0.04290
Avg Forward Time: 0.29953
Finished training!
[val: 60, 78 / 78] FPS: 39.5 (218.1)  ,  DataTime: 1.348 (0.044)  ,  ForwardTime: 0.229  ,  TotalTime: 1.620  ,  Loss/total: 0.83108  ,  Loss/giou: 0.20092  ,  Loss/l1: 0.02122  ,  Loss/location: 0.32314  ,  IoU: 0.82010
Epoch Time: 0:02:06.330471
Avg Data Time: 1.34752
Avg GPU Trans Time: 0.04358
Avg Forward Time: 0.22852
Finished training!
args.config  coesot
