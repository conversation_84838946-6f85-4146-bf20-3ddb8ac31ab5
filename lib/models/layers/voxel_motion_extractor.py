import torch
import torch.nn as nn
import torch.nn.functional as F
import spconv.pytorch as spconv
from typing import Tuple, Optional, Dict


class ProgressiveReceptiveFieldEncoder(nn.Module):
    """
    渐进式感受野稀疏体素编码器

    设计理念：感受野逐渐扩大，从局部到整体
    - 第1层: kernel=3, 局部细节特征
    - 第2层: kernel=5, 中等范围特征
    - 第3层: kernel=7, 大范围上下文
    - 第4层: kernel=3, 特征整合
    - 时间池化: 8 → 4（在四层卷积后）
    - 运动分析: 时间差分 + 空间梯度
    - 直接拼接融合: 语义特征 + 运动特征 → 统一网络融合
    """
    
    def __init__(self, input_dim=4, hidden_dims=[32, 64, 128, 256], output_dim=256):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 渐进式感受野稀疏卷积网络
        # 第1层: 3x3 局部细节提取
        self.sparse_conv1 = spconv.SubMConv3d(input_dim, hidden_dims[0], 3, padding=1)
        self.norm1 = nn.BatchNorm1d(hidden_dims[0])
        
        # 第2层: 5x5 中等范围特征
        self.sparse_conv2 = spconv.SubMConv3d(hidden_dims[0], hidden_dims[1], 5, padding=2)
        self.norm2 = nn.BatchNorm1d(hidden_dims[1])

        # 第3层: 7x7 大范围上下文
        self.sparse_conv3 = spconv.SubMConv3d(hidden_dims[1], hidden_dims[2], 7, padding=3)
        self.norm3 = nn.BatchNorm1d(hidden_dims[2])

        # 第4层: 3x3 特征整合
        self.sparse_conv4 = spconv.SubMConv3d(hidden_dims[2], output_dim, 3, padding=1)
        self.norm4 = nn.BatchNorm1d(output_dim)

        # 时间维度池化：8 → 4（移到四层卷积之后）
        self.temporal_pool = spconv.SparseConv3d(output_dim, output_dim,
                                               kernel_size=(2, 1, 1), stride=(2, 1, 1), padding=(0, 0, 0))
        self.norm_pool = nn.BatchNorm1d(output_dim)

        # 稀疏域内运动分析模块（新增：在稀疏域内完成运动分析）
        self.motion_analysis_dim = output_dim // 4  # 运动分析特征维度

        # 时间差分分析（稀疏域内）
        self.temporal_diff_conv = spconv.SubMConv3d(output_dim, self.motion_analysis_dim,
                                                   kernel_size=(3, 1, 1), padding=(1, 0, 0))
        self.temporal_diff_norm = nn.BatchNorm1d(self.motion_analysis_dim)

        # 空间梯度分析（稀疏域内）
        self.spatial_grad_conv = spconv.SubMConv3d(output_dim, self.motion_analysis_dim,
                                                  kernel_size=(1, 3, 3), padding=(0, 1, 1))
        self.spatial_grad_norm = nn.BatchNorm1d(self.motion_analysis_dim)

        # 统一融合网络（直接拼接方案）
        fusion_input_dim = output_dim + self.motion_analysis_dim * 2  # 语义特征 + 时间运动 + 空间运动
        self.unified_fusion_conv = spconv.SubMConv3d(fusion_input_dim, output_dim,
                                                    kernel_size=3, padding=1)
        self.unified_fusion_norm = nn.BatchNorm1d(output_dim)

        # 可选的残差连接控制
        self.use_residual = False
        
        # 激活函数（使用GELU减少死神经元问题）
        self.gelu = nn.GELU()


        
    def forward(self, voxel_features, voxel_coords, spatial_shape):
        """
        Args:
            voxel_features: [N, input_dim] 体素特征
            voxel_coords: [N, 4] 体素坐标 (batch_idx, t, y, x)
            spatial_shape: [3] 空间形状 (T, H, W)
        Returns:
            sparse_feature_map: 稀疏特征图
            dense_features: 密集特征 [B, output_dim, T, H, W]
        """
        # 构建稀疏张量
        sparse_tensor = spconv.SparseConvTensor(
            features=voxel_features,
            indices=voxel_coords.int(),
            spatial_shape=spatial_shape,
            batch_size=int(voxel_coords[:, 0].max().item()) + 1
        )
        
        # 渐进式感受野处理（使用GELU减少死神经元）
        # 第1层: 3x3 局部细节
        x1 = self.sparse_conv1(sparse_tensor)
        x1 = x1.replace_feature(self.gelu(self.norm1(x1.features)))

        # 第2层: 5x5 中等范围
        x2 = self.sparse_conv2(x1)
        x2 = x2.replace_feature(self.gelu(self.norm2(x2.features)))

        # 第3层: 7x7 大范围上下文
        x3 = self.sparse_conv3(x2)
        x3 = x3.replace_feature(self.gelu(self.norm3(x3.features)))

        # 第4层: 3x3 特征整合（语义特征主线）
        x4 = self.sparse_conv4(x3)
        x4 = x4.replace_feature(self.gelu(self.norm4(x4.features)))

        # 时间维度池化：8 → 4（移到四层卷积之后）
        x4_pooled = self.temporal_pool(x4)
        x4_pooled = x4_pooled.replace_feature(self.gelu(self.norm_pool(x4_pooled.features)))

        # 稀疏域内运动分析（简化的直接拼接方案）
        # 1. 时间差分计算
        temporal_motion = self.temporal_diff_conv(x4_pooled)
        temporal_motion = temporal_motion.replace_feature(
            self.gelu(self.temporal_diff_norm(temporal_motion.features))
        )

        # 2. 空间梯度计算
        spatial_motion = self.spatial_grad_conv(x4_pooled)
        spatial_motion = spatial_motion.replace_feature(
            self.gelu(self.spatial_grad_norm(spatial_motion.features))
        )

        # 3. 直接拼接所有特征（简化方案）
        all_features = torch.cat([
            x4_pooled.features,           # 语义特征 [N, 256]
            temporal_motion.features,     # 时间运动 [N, 64]
            spatial_motion.features       # 空间运动 [N, 64]
        ], dim=1)  # [N, 384]

        # 4. 统一融合网络
        unified_sparse = spconv.SparseConvTensor(
            features=all_features,
            indices=x4_pooled.indices,
            spatial_shape=x4_pooled.spatial_shape,
            batch_size=x4_pooled.batch_size
        )

        motion_enhanced = self.unified_fusion_conv(unified_sparse)  # [N, 384] → [N, 256]
        motion_enhanced = motion_enhanced.replace_feature(
            self.gelu(self.unified_fusion_norm(motion_enhanced.features))
        )

        # 5. 可选的残差连接
        if self.use_residual:
            final_features = x4_pooled.features + motion_enhanced.features
        else:
            final_features = motion_enhanced.features

        # 构建最终的稀疏特征图
        motion_enhanced_sparse = spconv.SparseConvTensor(
            features=final_features,
            indices=x4_pooled.indices,
            spatial_shape=x4_pooled.spatial_shape,
            batch_size=x4_pooled.batch_size
        )

        sparse_feature_map = motion_enhanced_sparse

        # 转换为密集表示
        dense_features = motion_enhanced_sparse.dense()

        return sparse_feature_map, dense_features


class VoxelMotionExtractor(nn.Module):
    """
    体素运动特征提取器

    整合功能：
    1. 渐进式感受野稀疏体素编码
    2. 时空关联显著性建模（不涉及全局运动统计）
    3. 空间自适应时间注意力融合（不同空间位置的个性化时间权重）
    4. 运动强度计算

    设计理念：
    - 移除复杂的全局运动统计模块
    - 保留时空联合的时间步池化（空间自适应）
    - 避免粗暴的统一时间池化
    - 每个空间位置有不同的时间权重

    在主体模型层间循环前调用一次，输出可被所有层复用
    """

    def __init__(self, motion_channels=256, temporal_temperature=2.0):
        super().__init__()
        self.motion_channels = motion_channels
        self.temporal_temperature = temporal_temperature

        # 渐进式感受野编码器
        self.sparse_encoder = ProgressiveReceptiveFieldEncoder(
            input_dim=1,
            hidden_dims=[32, 64, 128, motion_channels],
            output_dim=motion_channels
        )

        # 时空联合分析模块（保留空间自适应的时间池化）
        self.spatiotemporal_correlation = nn.ModuleDict({
            'motion_saliency': nn.Conv3d(motion_channels, 1, 3, padding=1),
            'temporal_consistency': nn.Conv3d(motion_channels, 1, (3, 1, 1), padding=(1, 0, 0)),
            'spatial_coherence': nn.Conv3d(motion_channels, 1, (1, 3, 3), padding=(0, 1, 1)),
            'correlation_fusion': nn.Conv3d(3, 1, 1)
        })

        # 空间自适应时间注意力（不同空间位置的个性化时间权重）
        self.adaptive_temporal_attention = nn.Sequential(
            nn.Conv3d(motion_channels + 1, 32, 3, padding=1),  # +1 for motion saliency
            nn.GELU(),
            nn.Conv3d(32, 1, 1)
        )


        
    def forward(self, voxel_coords: torch.Tensor, voxel_features: torch.Tensor, 
                spatial_shape: list, empty_batch_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        体素运动特征提取
        
        Args:
            voxel_coords: [N, 4] 体素坐标
            voxel_features: [N, 4] 体素特征
            spatial_shape: [3] 空间形状 (T, H, W)
            empty_batch_mask: [B] 空batch标记
            
        Returns:
            processed_motion_features: [B, motion_channels, H, W] 处理后的运动特征
            motion_intensity: [B] 运动强度评分
        """
        
        # 1. 渐进式感受野稀疏编码（包含稀疏域内运动分析）
        sparse_feature_map, dense_features = self.sparse_encoder(
            voxel_features, voxel_coords, spatial_shape
        )

        # dense_features: [B, motion_channels, T, H, W] where T=4
        B, C, T, H, W = dense_features.shape

        # 2. 时空关联显著性建模（不涉及全局运动统计）
        spatiotemporal_correlation, motion_saliency = self.compute_spatiotemporal_correlation(dense_features)

        # 3. 空间自适应时间注意力计算（不同空间位置的个性化时间权重）
        attention_weights = self.compute_adaptive_temporal_weights(dense_features, spatiotemporal_correlation)

        # 4. 空间保持的显著性池化（强制保持H×W维度）
        processed_motion_features = torch.sum(dense_features * attention_weights, dim=2)  # [B, C, H, W]

        # 确保输出维度严格为 [B, C, H, W]
        assert processed_motion_features.shape == (B, C, H, W), f"输出维度错误: {processed_motion_features.shape}"

        # 5. 运动强度计算（基于处理后的特征）
        motion_intensity = torch.mean(torch.norm(processed_motion_features, dim=1), dim=[1, 2])  # [B]
        
        # 4. 处理空batch（避免inplace操作）
        if empty_batch_mask is not None:
            # 创建mask张量避免inplace操作
            batch_mask = (~empty_batch_mask).float()  # [B] 非空batch为1，空batch为0

            # 应用mask到特征和强度
            processed_motion_features = processed_motion_features * batch_mask.view(B, 1, 1, 1)
            motion_intensity = motion_intensity * batch_mask
        
        return processed_motion_features, motion_intensity

    def compute_spatiotemporal_correlation(self, dense_features):
        """
        时空关联显著性建模（不涉及全局运动统计）
        计算每个空间位置在不同时间步的运动显著性关联
        """
        B, C, T, H, W = dense_features.shape

        # 1. 运动显著性计算（基于原始特征）
        motion_saliency = torch.sigmoid(self.spatiotemporal_correlation['motion_saliency'](dense_features))  # [B, 1, T, H, W]

        # 2. 时间一致性分析
        temporal_consistency = torch.sigmoid(self.spatiotemporal_correlation['temporal_consistency'](dense_features))  # [B, 1, T, H, W]

        # 3. 空间连贯性分析
        spatial_coherence = torch.sigmoid(self.spatiotemporal_correlation['spatial_coherence'](dense_features))  # [B, 1, T, H, W]

        # 4. 时空关联融合
        correlation_input = torch.cat([motion_saliency, temporal_consistency, spatial_coherence], dim=1)  # [B, 3, T, H, W]
        spatiotemporal_correlation = torch.sigmoid(self.spatiotemporal_correlation['correlation_fusion'](correlation_input))  # [B, 1, T, H, W]

        return spatiotemporal_correlation, motion_saliency

    def compute_adaptive_temporal_weights(self, dense_features, spatiotemporal_correlation):
        """
        空间自适应时间注意力计算
        不同空间位置的个性化时间权重（这是需要保留的核心功能）
        """
        # 结合原始特征和时空关联信息
        enhanced_features = torch.cat([dense_features, spatiotemporal_correlation], dim=1)

        # 计算空间自适应的时间注意力logits
        attention_logits = self.adaptive_temporal_attention(enhanced_features)  # [B, 1, T, H, W]

        # 应用温度参数和softmax
        attention_weights = F.softmax(attention_logits / self.temporal_temperature, dim=2)  # [B, 1, T, H, W]

        return attention_weights


class DenseVoxelEncoder(nn.Module):
    """
    密集3D卷积体素编码器

    功能：
    1. 基于密集3D卷积的时空特征提取
    2. 运动方向感知
    3. 时间差分建模
    4. 多特征融合

    适用于：密集体素网格输入 [B, 1, T, H, W]
    """

    def __init__(self, embed_dim=768):
        super().__init__()

        # 增强的3D卷积网络 - 专注于时空运动模式（使用GELU优化）
        self.conv3d = nn.Sequential(
            # 第一层：细粒度时空特征
            nn.Conv3d(1, 32, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.BatchNorm3d(32),
            nn.GELU(),  # 使用GELU替代ReLU

            # 第二层：运动方向感知
            nn.Conv3d(32, 64, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.BatchNorm3d(64),
            nn.GELU(),
            nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2)),  # 保留时间维度

            # 第三层：运动趋势建模
            nn.Conv3d(64, 128, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.BatchNorm3d(128),
            nn.GELU(),

            # 第四层：长时间依赖
            nn.Conv3d(128, 64, kernel_size=(5, 3, 3), padding=(2, 1, 1)),  # 扩大时间感受野
            nn.BatchNorm3d(64)
            # 移除最后一层激活，保持更多信息
        )

        # 运动方向感知模块（优化激活函数）
        self.motion_direction = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=(3, 1, 1), padding=(1, 0, 0)),  # 时间方向
            nn.Conv3d(16, 16, kernel_size=(1, 3, 1), padding=(0, 1, 0)),  # 垂直方向
            nn.Conv3d(16, 16, kernel_size=(1, 1, 3), padding=(0, 0, 1)),  # 水平方向
            nn.GELU()  # 使用GELU替代ReLU
        )

        # 时间差分模块 - 捕获运动变化
        self.temporal_diff = nn.Sequential(
            nn.Conv3d(1, 32, kernel_size=(2, 3, 3), padding=(0, 1, 1)),  # 时间差分
            nn.GELU()  # 使用GELU替代ReLU
        )

        self.adaptive_pool = nn.AdaptiveAvgPool3d((4, 8, 8))  # 增加时间分辨率

        # 特征融合（优化激活函数）
        self.feature_fusion = nn.Sequential(
            nn.Conv3d(64 + 16 + 32, 64, kernel_size=1),  # 融合主特征+方向+差分
            nn.BatchNorm3d(64)
            # 移除激活，保持线性融合
        )

        # 投影层
        self.projection = nn.Sequential(
            nn.Linear(64, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.Dropout(0.1)
        )

    def forward(self, voxel_grid):
        """
        密集体素网格编码

        Args:
            voxel_grid: [B, 1, T, H, W] 密集体素网格

        Returns:
            voxel_features: [B, 256, embed_dim] 体素特征tokens
        """
        # 主要时空特征
        main_features = self.conv3d(voxel_grid)  # [B, 64, T, H/2, W/2]

        # 运动方向特征
        direction_features = self.motion_direction(voxel_grid)  # [B, 16, T, H, W]
        direction_features = F.adaptive_avg_pool3d(direction_features, main_features.shape[2:])

        # 时间差分特征（捕获运动变化）
        if voxel_grid.shape[2] > 1:  # 确保有足够的时间帧
            diff_features = self.temporal_diff(voxel_grid)  # [B, 32, T-1, H, W]
            # 填充时间维度以匹配
            diff_features = F.pad(diff_features, (0, 0, 0, 0, 1, 0))  # 在时间维度前填充
            diff_features = F.adaptive_avg_pool3d(diff_features, main_features.shape[2:])
        else:
            diff_features = torch.zeros(main_features.shape[0], 32, *main_features.shape[2:],
                                      device=main_features.device)

        # 融合多种特征
        combined_features = torch.cat([main_features, direction_features, diff_features], dim=1)
        fused_features = self.feature_fusion(combined_features)  # [B, 64, T, H, W]

        # 自适应池化
        pooled_features = self.adaptive_pool(fused_features)  # [B, 64, 4, 8, 8]

        # 投影
        B = pooled_features.shape[0]
        voxel_tokens = pooled_features.flatten(2).transpose(1, 2)  # [B, 256, 64]
        voxel_features = self.projection(voxel_tokens)  # [B, 256, embed_dim]

        return voxel_features

